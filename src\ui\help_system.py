#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
帮助系统和新手引导模块

提供内置帮助系统、新手引导、功能提示等用户体验功能。
"""

import flet as ft
import json
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, asdict
from enum import Enum
import markdown
from datetime import datetime

from ..utils.logger import LoggerManager
from ..utils.error_handler import ErrorHandler


class GuideStep(Enum):
    """引导步骤类型"""
    HIGHLIGHT = "highlight"  # 高亮元素
    TOOLTIP = "tooltip"      # 工具提示
    MODAL = "modal"          # 模态对话框
    OVERLAY = "overlay"      # 遮罩层
    ANIMATION = "animation"  # 动画效果


class HelpCategory(Enum):
    """帮助分类"""
    GETTING_STARTED = "getting_started"
    BASIC_FEATURES = "basic_features"
    ADVANCED_FEATURES = "advanced_features"
    TROUBLESHOOTING = "troubleshooting"
    FAQ = "faq"
    TIPS = "tips"


@dataclass
class GuideStepData:
    """引导步骤数据"""
    id: str
    title: str
    content: str
    step_type: GuideStep
    target_element: Optional[str] = None
    position: str = "bottom"  # top, bottom, left, right
    duration: int = 5000  # 毫秒
    skippable: bool = True
    required_action: Optional[str] = None
    next_step: Optional[str] = None
    conditions: Optional[Dict[str, Any]] = None


@dataclass
class HelpArticle:
    """帮助文章"""
    id: str
    title: str
    category: HelpCategory
    content: str
    tags: List[str]
    created_at: datetime
    updated_at: datetime
    view_count: int = 0
    helpful_count: int = 0
    related_articles: List[str] = None


class TutorialManager:
    """教程管理器"""
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("help.tutorial")
        self.error_handler = ErrorHandler()
        self.tutorials: Dict[str, List[GuideStepData]] = {}
        self.current_tutorial: Optional[str] = None
        self.current_step: int = 0
        self.completed_tutorials: List[str] = []
        self._load_tutorials()
    
    def _load_tutorials(self):
        """加载教程数据"""
        try:
            # 新手入门教程
            self.tutorials["first_time"] = [
                GuideStepData(
                    id="welcome",
                    title="欢迎使用竹落AI小说创作平台",
                    content="欢迎来到竹落AI小说创作平台！让我们通过简单的引导来了解主要功能。",
                    step_type=GuideStep.MODAL,
                    duration=0,  # 需要用户点击继续
                    skippable=True
                ),
                GuideStepData(
                    id="create_project",
                    title="创建您的第一个项目",
                    content="点击这里创建您的第一个小说项目。项目是管理您作品的基本单位。",
                    step_type=GuideStep.HIGHLIGHT,
                    target_element="new_project_btn",
                    position="bottom",
                    required_action="click"
                ),
                GuideStepData(
                    id="project_info",
                    title="填写项目信息",
                    content="为您的项目起一个好听的名字，选择合适的类型，这将帮助AI更好地理解您的创作意图。",
                    step_type=GuideStep.TOOLTIP,
                    target_element="project_form",
                    position="right"
                ),
                GuideStepData(
                    id="first_chapter",
                    title="创建第一个章节",
                    content="现在让我们创建第一个章节开始写作吧！",
                    step_type=GuideStep.HIGHLIGHT,
                    target_element="new_chapter_btn",
                    position="bottom"
                ),
                GuideStepData(
                    id="ai_assistant",
                    title="AI写作助手",
                    content="这是AI写作助手，它可以帮您续写内容、提供创作建议。试试输入一些文字，然后点击AI续写！",
                    step_type=GuideStep.HIGHLIGHT,
                    target_element="ai_panel",
                    position="left"
                ),
                GuideStepData(
                    id="completion",
                    title="恭喜完成新手引导！",
                    content="您已经掌握了基本操作！现在可以开始您的创作之旅了。如需更多帮助，请点击右上角的帮助按钮。",
                    step_type=GuideStep.MODAL,
                    duration=0
                )
            ]
            
            # AI功能教程
            self.tutorials["ai_features"] = [
                GuideStepData(
                    id="ai_intro",
                    title="AI功能介绍",
                    content="让我们深入了解AI写作助手的强大功能。",
                    step_type=GuideStep.MODAL
                ),
                GuideStepData(
                    id="ai_continue",
                    title="AI续写功能",
                    content="选中文本或将光标放在需要续写的位置，然后点击AI续写按钮。",
                    step_type=GuideStep.HIGHLIGHT,
                    target_element="ai_continue_btn"
                ),
                GuideStepData(
                    id="ai_chat",
                    title="AI对话助手",
                    content="您可以与AI进行对话，询问创作建议、角色发展等问题。",
                    step_type=GuideStep.HIGHLIGHT,
                    target_element="ai_chat_panel"
                ),
                GuideStepData(
                    id="ai_settings",
                    title="AI设置",
                    content="在这里可以调整AI的创作风格、创意程度等参数。",
                    step_type=GuideStep.HIGHLIGHT,
                    target_element="ai_settings_btn"
                )
            ]
            
            # 角色管理教程
            self.tutorials["character_management"] = [
                GuideStepData(
                    id="char_intro",
                    title="角色管理系统",
                    content="良好的角色管理是优秀小说的基础。让我们学习如何使用角色管理功能。",
                    step_type=GuideStep.MODAL
                ),
                GuideStepData(
                    id="add_character",
                    title="添加角色",
                    content="点击这里添加新角色，填写详细的角色信息。",
                    step_type=GuideStep.HIGHLIGHT,
                    target_element="add_character_btn"
                ),
                GuideStepData(
                    id="character_card",
                    title="角色卡片",
                    content="角色卡片显示角色的基本信息，点击可以查看详细资料。",
                    step_type=GuideStep.TOOLTIP,
                    target_element="character_card"
                ),
                GuideStepData(
                    id="relationship_map",
                    title="关系图谱",
                    content="关系图谱可以可视化角色之间的关系，帮助您理清复杂的人物关系。",
                    step_type=GuideStep.HIGHLIGHT,
                    target_element="relationship_map"
                )
            ]
            
            self.logger.info("教程数据加载完成")
            
        except Exception as e:
            self.error_handler.handle_error(e, {"action": "load_tutorials"})
    
    def start_tutorial(self, tutorial_id: str) -> bool:
        """开始教程"""
        if tutorial_id not in self.tutorials:
            self.logger.warning(f"教程不存在: {tutorial_id}")
            return False
        
        self.current_tutorial = tutorial_id
        self.current_step = 0
        self.logger.info(f"开始教程: {tutorial_id}")
        return True
    
    def get_current_step(self) -> Optional[GuideStepData]:
        """获取当前步骤"""
        if not self.current_tutorial:
            return None
        
        tutorial = self.tutorials.get(self.current_tutorial)
        if not tutorial or self.current_step >= len(tutorial):
            return None
        
        return tutorial[self.current_step]
    
    def next_step(self) -> Optional[GuideStepData]:
        """下一步"""
        if not self.current_tutorial:
            return None
        
        tutorial = self.tutorials.get(self.current_tutorial)
        if not tutorial:
            return None
        
        self.current_step += 1
        
        if self.current_step >= len(tutorial):
            # 教程完成
            self.complete_tutorial()
            return None
        
        return tutorial[self.current_step]
    
    def previous_step(self) -> Optional[GuideStepData]:
        """上一步"""
        if not self.current_tutorial or self.current_step <= 0:
            return None
        
        self.current_step -= 1
        tutorial = self.tutorials[self.current_tutorial]
        return tutorial[self.current_step]
    
    def skip_tutorial(self):
        """跳过教程"""
        if self.current_tutorial:
            self.logger.info(f"跳过教程: {self.current_tutorial}")
            self.current_tutorial = None
            self.current_step = 0
    
    def complete_tutorial(self):
        """完成教程"""
        if self.current_tutorial:
            if self.current_tutorial not in self.completed_tutorials:
                self.completed_tutorials.append(self.current_tutorial)
            
            self.logger.info(f"完成教程: {self.current_tutorial}")
            self.current_tutorial = None
            self.current_step = 0
    
    def is_tutorial_completed(self, tutorial_id: str) -> bool:
        """检查教程是否已完成"""
        return tutorial_id in self.completed_tutorials
    
    def get_tutorial_progress(self) -> Dict[str, Any]:
        """获取教程进度"""
        if not self.current_tutorial:
            return {}
        
        tutorial = self.tutorials.get(self.current_tutorial)
        if not tutorial:
            return {}
        
        return {
            "tutorial_id": self.current_tutorial,
            "current_step": self.current_step,
            "total_steps": len(tutorial),
            "progress": (self.current_step / len(tutorial)) * 100
        }


class HelpContentManager:
    """帮助内容管理器"""
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("help.content")
        self.error_handler = ErrorHandler()
        self.articles: Dict[str, HelpArticle] = {}
        self.search_index: Dict[str, List[str]] = {}
        self._load_help_content()
    
    def _load_help_content(self):
        """加载帮助内容"""
        try:
            # 从用户指南文件加载内容
            docs_path = Path(__file__).parent.parent.parent / "docs" / "user_guide.md"
            
            if docs_path.exists():
                with open(docs_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 解析Markdown内容
                self._parse_markdown_content(content)
            
            # 添加内置帮助文章
            self._add_builtin_articles()
            
            # 构建搜索索引
            self._build_search_index()
            
            self.logger.info(f"加载了 {len(self.articles)} 篇帮助文章")
            
        except Exception as e:
            self.error_handler.handle_error(e, {"action": "load_help_content"})
    
    def _parse_markdown_content(self, content: str):
        """解析Markdown内容"""
        # 简单的Markdown解析，按标题分割内容
        sections = content.split('\n## ')
        
        for i, section in enumerate(sections):
            if i == 0:  # 跳过标题部分
                continue
            
            lines = section.split('\n')
            title = lines[0].strip()
            content_text = '\n'.join(lines[1:]).strip()
            
            # 确定分类
            category = self._determine_category(title)
            
            article = HelpArticle(
                id=f"guide_{i}",
                title=title,
                category=category,
                content=content_text,
                tags=self._extract_tags(title, content_text),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            self.articles[article.id] = article
    
    def _determine_category(self, title: str) -> HelpCategory:
        """根据标题确定分类"""
        title_lower = title.lower()
        
        if any(word in title_lower for word in ['快速开始', '入门', '开始']):
            return HelpCategory.GETTING_STARTED
        elif any(word in title_lower for word in ['高级', 'advanced']):
            return HelpCategory.ADVANCED_FEATURES
        elif any(word in title_lower for word in ['问题', '故障', '错误']):
            return HelpCategory.TROUBLESHOOTING
        elif any(word in title_lower for word in ['常见', 'faq']):
            return HelpCategory.FAQ
        elif any(word in title_lower for word in ['技巧', '建议', 'tips']):
            return HelpCategory.TIPS
        else:
            return HelpCategory.BASIC_FEATURES
    
    def _extract_tags(self, title: str, content: str) -> List[str]:
        """提取标签"""
        tags = []
        text = (title + ' ' + content).lower()
        
        # 常见标签关键词
        tag_keywords = {
            'ai': ['ai', '人工智能', '智能'],
            '写作': ['写作', '创作', '编辑'],
            '角色': ['角色', '人物', '角色管理'],
            '项目': ['项目', '管理', '组织'],
            '设置': ['设置', '配置', '选项'],
            '协作': ['协作', '团队', '共享'],
            '导出': ['导出', '保存', '格式'],
            '备份': ['备份', '恢复', '同步']
        }
        
        for tag, keywords in tag_keywords.items():
            if any(keyword in text for keyword in keywords):
                tags.append(tag)
        
        return tags
    
    def _add_builtin_articles(self):
        """添加内置帮助文章"""
        builtin_articles = [
            {
                "id": "quick_start",
                "title": "5分钟快速上手",
                "category": HelpCategory.GETTING_STARTED,
                "content": """
# 5分钟快速上手

## 第1步：创建项目
1. 点击「新建项目」按钮
2. 输入项目名称，如"我的第一部小说"
3. 选择小说类型
4. 点击「创建」

## 第2步：新建章节
1. 在项目中点击「新建章节」
2. 输入章节标题
3. 开始在编辑器中写作

## 第3步：使用AI助手
1. 写几句话作为开头
2. 点击「AI续写」按钮
3. 选择满意的续写内容

## 第4步：管理角色
1. 点击右侧「角色管理」
2. 添加主要角色信息
3. 设置角色关系

## 第5步：保存和导出
1. 内容会自动保存
2. 完成后可导出为Word或PDF格式

恭喜！您已经掌握了基本操作。
""",
                "tags": ["新手", "入门", "快速"]
            },
            {
                "id": "ai_tips",
                "title": "AI写作技巧",
                "category": HelpCategory.TIPS,
                "content": """
# AI写作技巧

## 提供清晰的上下文
- 在使用AI续写前，确保前文有足够的背景信息
- 描述当前场景、角色状态、情节发展方向

## 使用具体的指令
- 不要只说"继续写"，而要说"描写主角的内心独白"
- 指定写作风格："用悬疑的语调描述"
- 指定内容长度："写200字左右"

## 多次尝试
- AI每次生成的内容都不同
- 可以多次点击续写，选择最满意的版本
- 结合多个版本的优点进行人工编辑

## 渐进式创作
- 不要一次性让AI写太长的内容
- 分段落、分场景逐步推进
- 每次续写后检查逻辑连贯性

## 保持角色一致性
- 在角色管理中详细描述角色性格
- 续写时提醒AI注意角色特点
- 定期检查角色行为是否符合设定
""",
                "tags": ["AI", "技巧", "写作"]
            },
            {
                "id": "common_issues",
                "title": "常见问题解决",
                "category": HelpCategory.TROUBLESHOOTING,
                "content": """
# 常见问题解决

## AI功能无法使用
**问题**: 点击AI续写没有反应
**解决方案**:
1. 检查网络连接是否正常
2. 确认API密钥是否正确配置
3. 检查账户余额是否充足
4. 尝试重启应用程序

## 文件保存失败
**问题**: 提示"保存失败"错误
**解决方案**:
1. 检查磁盘空间是否充足
2. 确认文件夹是否有写入权限
3. 尝试另存为到其他位置
4. 关闭可能占用文件的其他程序

## 应用运行缓慢
**问题**: 界面响应慢，操作卡顿
**解决方案**:
1. 关闭不必要的插件
2. 清理临时文件和缓存
3. 减少同时打开的项目数量
4. 重启应用程序

## 协作同步问题
**问题**: 多人协作时内容不同步
**解决方案**:
1. 检查网络连接稳定性
2. 确认所有用户使用相同版本
3. 手动刷新或重新加载项目
4. 联系技术支持
""",
                "tags": ["问题", "故障", "解决"]
            }
        ]
        
        for article_data in builtin_articles:
            article = HelpArticle(
                id=article_data["id"],
                title=article_data["title"],
                category=article_data["category"],
                content=article_data["content"],
                tags=article_data["tags"],
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            self.articles[article.id] = article
    
    def _build_search_index(self):
        """构建搜索索引"""
        self.search_index.clear()
        
        for article_id, article in self.articles.items():
            # 索引标题和内容中的关键词
            text = (article.title + ' ' + article.content).lower()
            words = text.split()
            
            for word in words:
                if len(word) > 1:  # 忽略单字符
                    if word not in self.search_index:
                        self.search_index[word] = []
                    if article_id not in self.search_index[word]:
                        self.search_index[word].append(article_id)
    
    def search_articles(self, query: str) -> List[HelpArticle]:
        """搜索文章"""
        if not query.strip():
            return list(self.articles.values())
        
        query_words = query.lower().split()
        matching_articles = set()
        
        for word in query_words:
            if word in self.search_index:
                matching_articles.update(self.search_index[word])
        
        # 按相关性排序
        results = []
        for article_id in matching_articles:
            article = self.articles[article_id]
            # 简单的相关性计算
            relevance = sum(1 for word in query_words 
                          if word in article.title.lower() or word in article.content.lower())
            results.append((relevance, article))
        
        # 按相关性降序排序
        results.sort(key=lambda x: x[0], reverse=True)
        return [article for _, article in results]
    
    def get_articles_by_category(self, category: HelpCategory) -> List[HelpArticle]:
        """按分类获取文章"""
        return [article for article in self.articles.values() 
                if article.category == category]
    
    def get_article(self, article_id: str) -> Optional[HelpArticle]:
        """获取指定文章"""
        return self.articles.get(article_id)
    
    def increment_view_count(self, article_id: str):
        """增加浏览次数"""
        if article_id in self.articles:
            self.articles[article_id].view_count += 1
    
    def increment_helpful_count(self, article_id: str):
        """增加有用计数"""
        if article_id in self.articles:
            self.articles[article_id].helpful_count += 1


class HelpSystemUI:
    """帮助系统UI组件"""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.tutorial_manager = TutorialManager()
        self.content_manager = HelpContentManager()
        self.logger = LoggerManager().get_logger("help.ui")
        
        # UI状态
        self.is_tutorial_active = False
        self.current_overlay = None
        self.help_dialog = None
        
        # 回调函数
        self.on_tutorial_complete: Optional[Callable] = None
        self.on_help_close: Optional[Callable] = None
    
    def show_welcome_guide(self):
        """显示欢迎引导"""
        if self.tutorial_manager.is_tutorial_completed("first_time"):
            return
        
        self.start_tutorial("first_time")
    
    def start_tutorial(self, tutorial_id: str):
        """开始教程"""
        if self.tutorial_manager.start_tutorial(tutorial_id):
            self.is_tutorial_active = True
            self._show_current_step()
    
    def _show_current_step(self):
        """显示当前步骤"""
        step = self.tutorial_manager.get_current_step()
        if not step:
            self._end_tutorial()
            return
        
        if step.step_type == GuideStep.MODAL:
            self._show_modal_step(step)
        elif step.step_type == GuideStep.HIGHLIGHT:
            self._show_highlight_step(step)
        elif step.step_type == GuideStep.TOOLTIP:
            self._show_tooltip_step(step)
        elif step.step_type == GuideStep.OVERLAY:
            self._show_overlay_step(step)
    
    def _show_modal_step(self, step: GuideStepData):
        """显示模态步骤"""
        def on_next(e):
            self._next_step()
        
        def on_skip(e):
            self._skip_tutorial()
        
        # 创建模态对话框
        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text(step.title, size=20, weight=ft.FontWeight.BOLD),
            content=ft.Container(
                content=ft.Column([
                    ft.Text(step.content, size=14),
                    ft.Container(height=20),
                    self._create_progress_indicator()
                ], tight=True),
                width=400
            ),
            actions=[
                ft.TextButton("跳过", on_click=on_skip) if step.skippable else None,
                ft.ElevatedButton("下一步", on_click=on_next)
            ],
            actions_alignment=ft.MainAxisAlignment.END
        )
        
        # 过滤None值
        dialog.actions = [action for action in dialog.actions if action is not None]
        
        self.page.dialog = dialog
        dialog.open = True
        self.page.update()
    
    def _show_highlight_step(self, step: GuideStepData):
        """显示高亮步骤"""
        # 创建高亮遮罩
        overlay = ft.Container(
            content=ft.Stack([
                # 半透明遮罩
                ft.Container(
                    bgcolor=ft.colors.BLACK54,
                    expand=True
                ),
                # 提示框
                ft.Positioned(
                    top=100,
                    right=20,
                    child=ft.Card(
                        content=ft.Container(
                            content=ft.Column([
                                ft.Text(step.title, size=16, weight=ft.FontWeight.BOLD),
                                ft.Text(step.content, size=12),
                                ft.Container(height=10),
                                ft.Row([
                                    ft.TextButton("跳过", on_click=lambda e: self._skip_tutorial()),
                                    ft.ElevatedButton("下一步", on_click=lambda e: self._next_step())
                                ], alignment=ft.MainAxisAlignment.END)
                            ], tight=True),
                            padding=20,
                            width=300
                        )
                    )
                )
            ]),
            expand=True
        )
        
        self.current_overlay = overlay
        self.page.overlay.append(overlay)
        self.page.update()
        
        # 自动进入下一步（如果设置了持续时间）
        if step.duration > 0:
            asyncio.create_task(self._auto_next_step(step.duration))
    
    def _show_tooltip_step(self, step: GuideStepData):
        """显示工具提示步骤"""
        # 简化实现，使用悬浮提示
        tooltip = ft.Tooltip(
            message=f"{step.title}: {step.content}",
            child=ft.Container(
                content=ft.Text("💡", size=20),
                bgcolor=ft.colors.BLUE,
                border_radius=20,
                padding=10,
                on_click=lambda e: self._next_step()
            )
        )
        
        # 添加到页面右下角
        positioned_tooltip = ft.Positioned(
            bottom=20,
            right=20,
            child=tooltip
        )
        
        self.current_overlay = positioned_tooltip
        self.page.overlay.append(positioned_tooltip)
        self.page.update()
        
        if step.duration > 0:
            asyncio.create_task(self._auto_next_step(step.duration))
    
    def _show_overlay_step(self, step: GuideStepData):
        """显示遮罩步骤"""
        # 类似高亮步骤，但没有特定目标元素
        self._show_highlight_step(step)
    
    async def _auto_next_step(self, delay_ms: int):
        """自动进入下一步"""
        await asyncio.sleep(delay_ms / 1000)
        if self.is_tutorial_active:
            self._next_step()
    
    def _next_step(self):
        """下一步"""
        self._clear_overlay()
        next_step = self.tutorial_manager.next_step()
        
        if next_step:
            self._show_current_step()
        else:
            self._end_tutorial()
    
    def _skip_tutorial(self):
        """跳过教程"""
        self._clear_overlay()
        self.tutorial_manager.skip_tutorial()
        self._end_tutorial()
    
    def _end_tutorial(self):
        """结束教程"""
        self.is_tutorial_active = False
        self._clear_overlay()
        
        if self.on_tutorial_complete:
            self.on_tutorial_complete()
        
        self.logger.info("教程结束")
    
    def _clear_overlay(self):
        """清除遮罩"""
        if self.current_overlay:
            if self.current_overlay in self.page.overlay:
                self.page.overlay.remove(self.current_overlay)
            self.current_overlay = None
        
        if self.page.dialog:
            self.page.dialog.open = False
            self.page.dialog = None
        
        self.page.update()
    
    def _create_progress_indicator(self) -> ft.Control:
        """创建进度指示器"""
        progress = self.tutorial_manager.get_tutorial_progress()
        
        if not progress:
            return ft.Container()
        
        return ft.Column([
            ft.Text(f"步骤 {progress['current_step'] + 1} / {progress['total_steps']}", 
                   size=12, color=ft.colors.GREY_600),
            ft.ProgressBar(
                value=progress['progress'] / 100,
                bgcolor=ft.colors.GREY_300,
                color=ft.colors.BLUE
            )
        ], spacing=5)
    
    def show_help_dialog(self):
        """显示帮助对话框"""
        def on_close(e):
            self.help_dialog.open = False
            self.page.update()
            if self.on_help_close:
                self.on_help_close()
        
        def on_search(e):
            query = search_field.value
            results = self.content_manager.search_articles(query)
            self._update_article_list(results)
        
        def on_category_change(e):
            category = HelpCategory(category_dropdown.value)
            articles = self.content_manager.get_articles_by_category(category)
            self._update_article_list(articles)
        
        def on_article_select(article_id: str):
            article = self.content_manager.get_article(article_id)
            if article:
                self.content_manager.increment_view_count(article_id)
                self._show_article_content(article)
        
        # 搜索框
        search_field = ft.TextField(
            label="搜索帮助内容",
            prefix_icon=ft.icons.SEARCH,
            on_submit=on_search,
            expand=True
        )
        
        # 分类下拉框
        category_dropdown = ft.Dropdown(
            label="分类",
            options=[
                ft.dropdown.Option(cat.value, cat.value.replace('_', ' ').title())
                for cat in HelpCategory
            ],
            on_change=on_category_change,
            width=200
        )
        
        # 文章列表
        self.article_list = ft.ListView(
            expand=True,
            spacing=5
        )
        
        # 文章内容
        self.article_content = ft.Container(
            content=ft.Text("选择左侧文章查看详细内容", size=14),
            padding=20,
            expand=True
        )
        
        # 初始化文章列表
        all_articles = list(self.content_manager.articles.values())
        self._update_article_list(all_articles)
        
        # 创建对话框
        self.help_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("帮助中心", size=24, weight=ft.FontWeight.BOLD),
            content=ft.Container(
                content=ft.Column([
                    # 搜索和筛选
                    ft.Row([
                        search_field,
                        ft.Container(width=10),
                        category_dropdown
                    ]),
                    ft.Container(height=10),
                    # 主要内容
                    ft.Row([
                        # 左侧文章列表
                        ft.Container(
                            content=ft.Column([
                                ft.Text("文章列表", size=16, weight=ft.FontWeight.BOLD),
                                ft.Container(height=5),
                                self.article_list
                            ]),
                            width=300,
                            height=400
                        ),
                        ft.VerticalDivider(),
                        # 右侧文章内容
                        ft.Container(
                            content=ft.Column([
                                ft.Text("内容", size=16, weight=ft.FontWeight.BOLD),
                                ft.Container(height=5),
                                self.article_content
                            ]),
                            width=500,
                            height=400
                        )
                    ], expand=True)
                ]),
                width=850,
                height=500
            ),
            actions=[
                ft.TextButton("关闭", on_click=on_close)
            ]
        )
        
        self.page.dialog = self.help_dialog
        self.help_dialog.open = True
        self.page.update()
    
    def _update_article_list(self, articles: List[HelpArticle]):
        """更新文章列表"""
        self.article_list.controls.clear()
        
        for article in articles:
            def create_click_handler(article_id):
                return lambda e: self._show_article_content(
                    self.content_manager.get_article(article_id)
                )
            
            card = ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(article.title, size=14, weight=ft.FontWeight.BOLD),
                        ft.Text(
                            article.content[:100] + "..." if len(article.content) > 100 else article.content,
                            size=12,
                            color=ft.colors.GREY_600
                        ),
                        ft.Row([
                            ft.Chip(
                                label=ft.Text(article.category.value.replace('_', ' ').title(), size=10),
                                bgcolor=ft.colors.BLUE_100
                            ),
                            ft.Text(f"👁 {article.view_count}", size=10),
                            ft.Text(f"👍 {article.helpful_count}", size=10)
                        ], spacing=5)
                    ], spacing=5),
                    padding=10,
                    on_click=create_click_handler(article.id)
                )
            )
            
            self.article_list.controls.append(card)
        
        self.page.update()
    
    def _show_article_content(self, article: HelpArticle):
        """显示文章内容"""
        if not article:
            return
        
        def on_helpful(e):
            self.content_manager.increment_helpful_count(article.id)
            helpful_btn.text = f"👍 有用 ({article.helpful_count + 1})"
            self.page.update()
        
        # 转换Markdown为简单格式
        content_text = self._markdown_to_text(article.content)
        
        helpful_btn = ft.ElevatedButton(
            f"👍 有用 ({article.helpful_count})",
            on_click=on_helpful,
            icon=ft.icons.THUMB_UP
        )
        
        self.article_content.content = ft.Column([
            ft.Text(article.title, size=20, weight=ft.FontWeight.BOLD),
            ft.Container(height=10),
            ft.Text(content_text, size=12, selectable=True),
            ft.Container(height=20),
            ft.Row([
                helpful_btn,
                ft.Text(f"浏览次数: {article.view_count}", size=10, color=ft.colors.GREY_600)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
        ], scroll=ft.ScrollMode.AUTO)
        
        self.page.update()
    
    def _markdown_to_text(self, markdown_text: str) -> str:
        """简单的Markdown转文本"""
        # 移除Markdown标记
        text = markdown_text
        text = text.replace('# ', '').replace('## ', '').replace('### ', '')
        text = text.replace('**', '').replace('*', '')
        text = text.replace('`', '')
        return text
    
    def show_tutorial_menu(self):
        """显示教程菜单"""
        def on_tutorial_select(tutorial_id: str):
            tutorial_menu.open = False
            self.page.update()
            self.start_tutorial(tutorial_id)
        
        tutorials = [
            ("first_time", "新手入门", "适合第一次使用的用户"),
            ("ai_features", "AI功能详解", "深入了解AI写作助手"),
            ("character_management", "角色管理", "学习如何管理小说角色")
        ]
        
        tutorial_items = []
        for tutorial_id, title, description in tutorials:
            completed = self.tutorial_manager.is_tutorial_completed(tutorial_id)
            
            item = ft.ListTile(
                leading=ft.Icon(
                    ft.icons.CHECK_CIRCLE if completed else ft.icons.PLAY_CIRCLE,
                    color=ft.colors.GREEN if completed else ft.colors.BLUE
                ),
                title=ft.Text(title),
                subtitle=ft.Text(description),
                trailing=ft.Text("已完成" if completed else "开始", size=12),
                on_click=lambda e, tid=tutorial_id: on_tutorial_select(tid)
            )
            tutorial_items.append(item)
        
        tutorial_menu = ft.AlertDialog(
            title=ft.Text("选择教程", size=20, weight=ft.FontWeight.BOLD),
            content=ft.Container(
                content=ft.Column(tutorial_items, tight=True),
                width=400,
                height=300
            ),
            actions=[
                ft.TextButton("取消", on_click=lambda e: setattr(tutorial_menu, 'open', False) or self.page.update())
            ]
        )
        
        self.page.dialog = tutorial_menu
        tutorial_menu.open = True
        self.page.update()


# 便捷函数
def create_help_system(page: ft.Page) -> HelpSystemUI:
    """创建帮助系统实例"""
    return HelpSystemUI(page)


def show_quick_tip(page: ft.Page, title: str, content: str, duration: int = 3000):
    """显示快速提示"""
    tip = ft.SnackBar(
        content=ft.Row([
            ft.Icon(ft.icons.LIGHTBULB, color=ft.colors.YELLOW),
            ft.Column([
                ft.Text(title, weight=ft.FontWeight.BOLD, size=14),
                ft.Text(content, size=12)
            ], spacing=2)
        ]),
        bgcolor=ft.colors.BLUE_GREY_800,
        duration=duration
    )
    
    page.show_snack_bar(tip)


if __name__ == "__main__":
    # 示例用法
    def main(page: ft.Page):
        page.title = "帮助系统演示"
        page.theme_mode = ft.ThemeMode.LIGHT
        
        # 创建帮助系统
        help_system = create_help_system(page)
        
        # 创建测试按钮
        def show_welcome(e):
            help_system.show_welcome_guide()
        
        def show_help(e):
            help_system.show_help_dialog()
        
        def show_tutorials(e):
            help_system.show_tutorial_menu()
        
        def show_tip(e):
            show_quick_tip(page, "提示", "这是一个快速提示示例")
        
        page.add(
            ft.Column([
                ft.Text("帮助系统演示", size=24, weight=ft.FontWeight.BOLD),
                ft.Container(height=20),
                ft.ElevatedButton("显示欢迎引导", on_click=show_welcome),
                ft.ElevatedButton("打开帮助中心", on_click=show_help),
                ft.ElevatedButton("选择教程", on_click=show_tutorials),
                ft.ElevatedButton("显示快速提示", on_click=show_tip)
            ], spacing=10)
        )
    
    ft.app(target=main)