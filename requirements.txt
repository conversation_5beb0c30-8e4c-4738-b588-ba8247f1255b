# 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
flet==0.21.2

# 配置管理
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# 数据库
sqlalchemy==2.0.23
aiosqlite==0.19.0
psycopg2-binary==2.9.9
alembic==1.13.1

# AI服务客户端
openai==1.3.7
anthropic==0.7.7
dashscope==1.17.0
requests==2.31.0
httpx==0.25.2

# 异步支持
aiohttp==3.9.1
aiofiles==23.2.1

# 数据处理
pandas==2.1.4
numpy==1.25.2

# 文件处理
python-docx==1.1.0
openpyxl==3.1.2
PyPDF2==3.0.1

# 日志和监控
loguru==0.7.2

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
playwright==1.40.0
selenium==4.15.2

# 代码质量
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# 打包和部署
pyinstaller==6.2.0
cx-freeze==6.15.10
setuptools==69.0.2
wheel==0.42.0
auto-py-to-exe==2.41.0

# 开发工具
watchdog==3.0.0
psutil==5.9.6
memory-profiler==0.61.0
line-profiler==4.1.1

# 错误监控
sentry-sdk==1.38.0

# 性能监控
prometheus-client==0.19.0