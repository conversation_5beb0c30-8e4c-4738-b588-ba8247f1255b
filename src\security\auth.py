#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
身份验证和授权模块

提供用户认证、权限管理、会话管理、多因素认证等安全功能。
"""

import os
import json
import hashlib
import secrets
import hmac
import base64
from pathlib import Path
from typing import Dict, Any, Optional, List, Set, Union, Tuple
from dataclasses import dataclass, asdict, field
from datetime import datetime, timedelta
from enum import Enum
import uuid
import time

try:
    import jwt
except ImportError:
    print("Warning: PyJWT library not installed. Install with: pip install PyJWT")
    jwt = None

try:
    import pyotp
except ImportError:
    print("Warning: pyotp library not installed. Install with: pip install pyotp")
    pyotp = None

try:
    import qrcode
except ImportError:
    print("Warning: qrcode library not installed. Install with: pip install qrcode[pil]")
    qrcode = None

from ..utils.logger import LoggerManager
from ..utils.error_handler import ErrorHandler
from .encryption import SecureStorage, DataType


class UserRole(Enum):
    """用户角色"""
    GUEST = "guest"              # 访客
    USER = "user"                # 普通用户
    PREMIUM = "premium"          # 高级用户
    MODERATOR = "moderator"      # 版主
    ADMIN = "admin"              # 管理员
    SUPER_ADMIN = "super_admin"  # 超级管理员


class Permission(Enum):
    """权限类型"""
    # 基础权限
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    
    # 项目权限
    PROJECT_CREATE = "project_create"
    PROJECT_EDIT = "project_edit"
    PROJECT_DELETE = "project_delete"
    PROJECT_SHARE = "project_share"
    
    # AI功能权限
    AI_GENERATE = "ai_generate"
    AI_ADVANCED = "ai_advanced"
    AI_UNLIMITED = "ai_unlimited"
    
    # 系统权限
    USER_MANAGE = "user_manage"
    SYSTEM_CONFIG = "system_config"
    AUDIT_LOG = "audit_log"
    
    # 数据权限
    DATA_EXPORT = "data_export"
    DATA_IMPORT = "data_import"
    DATA_BACKUP = "data_backup"


class AuthMethod(Enum):
    """认证方法"""
    PASSWORD = "password"
    TOTP = "totp"              # 基于时间的一次性密码
    SMS = "sms"                # 短信验证
    EMAIL = "email"            # 邮箱验证
    BIOMETRIC = "biometric"    # 生物识别
    HARDWARE_KEY = "hardware_key"  # 硬件密钥


class SessionStatus(Enum):
    """会话状态"""
    ACTIVE = "active"
    EXPIRED = "expired"
    REVOKED = "revoked"
    SUSPENDED = "suspended"


@dataclass
class UserProfile:
    """用户档案"""
    user_id: str
    username: str
    email: str
    password_hash: str
    salt: str
    role: UserRole
    permissions: Set[Permission] = field(default_factory=set)
    created_at: datetime = field(default_factory=datetime.now)
    last_login: Optional[datetime] = None
    login_attempts: int = 0
    locked_until: Optional[datetime] = None
    mfa_enabled: bool = False
    mfa_secret: Optional[str] = None
    backup_codes: List[str] = field(default_factory=list)
    profile_data: Dict[str, Any] = field(default_factory=dict)
    preferences: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Session:
    """用户会话"""
    session_id: str
    user_id: str
    created_at: datetime
    last_accessed: datetime
    expires_at: datetime
    ip_address: str
    user_agent: str
    status: SessionStatus = SessionStatus.ACTIVE
    mfa_verified: bool = False
    permissions: Set[Permission] = field(default_factory=set)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AuthConfig:
    """认证配置"""
    # 密码策略
    min_password_length: int = 8
    require_uppercase: bool = True
    require_lowercase: bool = True
    require_numbers: bool = True
    require_special_chars: bool = True
    password_history_count: int = 5
    
    # 会话管理
    session_timeout_minutes: int = 60
    max_concurrent_sessions: int = 3
    remember_me_days: int = 30
    
    # 安全策略
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 15
    require_mfa_for_admin: bool = True
    
    # JWT设置
    jwt_secret_key: str = field(default_factory=lambda: secrets.token_urlsafe(32))
    jwt_algorithm: str = "HS256"
    jwt_expiration_hours: int = 24
    
    # 其他设置
    enable_audit_log: bool = True
    enable_rate_limiting: bool = True
    enable_ip_whitelist: bool = False
    allowed_ips: List[str] = field(default_factory=list)


class PasswordManager:
    """密码管理器"""
    
    def __init__(self, config: AuthConfig):
        self.config = config
        self.logger = LoggerManager().get_logger("security.password")
        self.error_handler = ErrorHandler()
    
    def hash_password(self, password: str, salt: Optional[str] = None) -> Tuple[str, str]:
        """哈希密码"""
        try:
            if salt is None:
                salt = secrets.token_hex(32)
            
            # 使用PBKDF2进行密码哈希
            password_hash = hashlib.pbkdf2_hmac(
                'sha256',
                password.encode('utf-8'),
                salt.encode('utf-8'),
                100000  # 迭代次数
            )
            
            return base64.b64encode(password_hash).decode('ascii'), salt
            
        except Exception as e:
            self.error_handler.handle_error(e, {"action": "hash_password"})
            raise
    
    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """验证密码"""
        try:
            # 重新计算哈希
            computed_hash = hashlib.pbkdf2_hmac(
                'sha256',
                password.encode('utf-8'),
                salt.encode('utf-8'),
                100000
            )
            
            stored_hash = base64.b64decode(password_hash.encode('ascii'))
            
            # 使用安全比较防止时序攻击
            return hmac.compare_digest(computed_hash, stored_hash)
            
        except Exception as e:
            self.error_handler.handle_error(e, {"action": "verify_password"})
            return False
    
    def validate_password_strength(self, password: str) -> Tuple[bool, List[str]]:
        """验证密码强度"""
        errors = []
        
        # 检查长度
        if len(password) < self.config.min_password_length:
            errors.append(f"密码长度至少需要{self.config.min_password_length}个字符")
        
        # 检查大写字母
        if self.config.require_uppercase and not any(c.isupper() for c in password):
            errors.append("密码必须包含至少一个大写字母")
        
        # 检查小写字母
        if self.config.require_lowercase and not any(c.islower() for c in password):
            errors.append("密码必须包含至少一个小写字母")
        
        # 检查数字
        if self.config.require_numbers and not any(c.isdigit() for c in password):
            errors.append("密码必须包含至少一个数字")
        
        # 检查特殊字符
        if self.config.require_special_chars:
            special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
            if not any(c in special_chars for c in password):
                errors.append("密码必须包含至少一个特殊字符")
        
        # 检查常见弱密码
        weak_passwords = [
            "password", "123456", "qwerty", "admin", "root",
            "password123", "123456789", "qwerty123"
        ]
        if password.lower() in weak_passwords:
            errors.append("密码过于简单，请选择更复杂的密码")
        
        return len(errors) == 0, errors
    
    def generate_secure_password(self, length: int = 12) -> str:
        """生成安全密码"""
        import string
        
        # 确保包含所有必需的字符类型
        chars = ""
        password_chars = []
        
        if self.config.require_lowercase:
            chars += string.ascii_lowercase
            password_chars.append(secrets.choice(string.ascii_lowercase))
        
        if self.config.require_uppercase:
            chars += string.ascii_uppercase
            password_chars.append(secrets.choice(string.ascii_uppercase))
        
        if self.config.require_numbers:
            chars += string.digits
            password_chars.append(secrets.choice(string.digits))
        
        if self.config.require_special_chars:
            special = "!@#$%^&*()_+-="
            chars += special
            password_chars.append(secrets.choice(special))
        
        # 填充剩余长度
        for _ in range(length - len(password_chars)):
            password_chars.append(secrets.choice(chars))
        
        # 随机打乱
        secrets.SystemRandom().shuffle(password_chars)
        
        return ''.join(password_chars)


class MFAManager:
    """多因素认证管理器"""
    
    def __init__(self, config: AuthConfig):
        self.config = config
        self.logger = LoggerManager().get_logger("security.mfa")
        self.error_handler = ErrorHandler()
    
    def generate_totp_secret(self) -> str:
        """生成TOTP密钥"""
        if pyotp is None:
            raise ImportError("pyotp库未安装")
        
        return pyotp.random_base32()
    
    def generate_qr_code(self, user_email: str, secret: str, 
                        issuer: str = "BambooFall AI") -> Optional[bytes]:
        """生成二维码"""
        try:
            if pyotp is None or qrcode is None:
                raise ImportError("pyotp或qrcode库未安装")
            
            # 创建TOTP URI
            totp = pyotp.TOTP(secret)
            provisioning_uri = totp.provisioning_uri(
                name=user_email,
                issuer_name=issuer
            )
            
            # 生成二维码
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(provisioning_uri)
            qr.make(fit=True)
            
            # 转换为图片
            img = qr.make_image(fill_color="black", back_color="white")
            
            # 转换为字节
            import io
            img_bytes = io.BytesIO()
            img.save(img_bytes, format='PNG')
            
            return img_bytes.getvalue()
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "generate_qr_code",
                "user_email": user_email
            })
            return None
    
    def verify_totp(self, secret: str, token: str, window: int = 1) -> bool:
        """验证TOTP令牌"""
        try:
            if pyotp is None:
                raise ImportError("pyotp库未安装")
            
            totp = pyotp.TOTP(secret)
            return totp.verify(token, valid_window=window)
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "verify_totp",
                "token_length": len(token) if token else 0
            })
            return False
    
    def generate_backup_codes(self, count: int = 10) -> List[str]:
        """生成备用代码"""
        codes = []
        for _ in range(count):
            # 生成8位数字代码
            code = ''.join([str(secrets.randbelow(10)) for _ in range(8)])
            # 格式化为 XXXX-XXXX
            formatted_code = f"{code[:4]}-{code[4:]}"
            codes.append(formatted_code)
        
        return codes
    
    def verify_backup_code(self, user_profile: UserProfile, code: str) -> bool:
        """验证备用代码"""
        try:
            if code in user_profile.backup_codes:
                # 使用后移除代码
                user_profile.backup_codes.remove(code)
                self.logger.info(f"备用代码已使用: {user_profile.user_id}")
                return True
            
            return False
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "verify_backup_code",
                "user_id": user_profile.user_id
            })
            return False


class SessionManager:
    """会话管理器"""
    
    def __init__(self, config: AuthConfig, storage: SecureStorage):
        self.config = config
        self.storage = storage
        self.logger = LoggerManager().get_logger("security.session")
        self.error_handler = ErrorHandler()
        self.active_sessions: Dict[str, Session] = {}
        self.user_sessions: Dict[str, Set[str]] = {}  # user_id -> session_ids
    
    def create_session(self, user_id: str, ip_address: str, 
                      user_agent: str, remember_me: bool = False) -> Session:
        """创建会话"""
        try:
            # 检查并清理过期会话
            self._cleanup_expired_sessions(user_id)
            
            # 检查并发会话限制
            if self._get_user_session_count(user_id) >= self.config.max_concurrent_sessions:
                # 移除最旧的会话
                self._remove_oldest_session(user_id)
            
            # 创建新会话
            session_id = str(uuid.uuid4())
            now = datetime.now()
            
            # 设置过期时间
            if remember_me:
                expires_at = now + timedelta(days=self.config.remember_me_days)
            else:
                expires_at = now + timedelta(minutes=self.config.session_timeout_minutes)
            
            session = Session(
                session_id=session_id,
                user_id=user_id,
                created_at=now,
                last_accessed=now,
                expires_at=expires_at,
                ip_address=ip_address,
                user_agent=user_agent,
                status=SessionStatus.ACTIVE
            )
            
            # 保存会话
            self.active_sessions[session_id] = session
            
            if user_id not in self.user_sessions:
                self.user_sessions[user_id] = set()
            self.user_sessions[user_id].add(session_id)
            
            # 持久化会话
            self._save_session(session)
            
            self.logger.info(f"会话创建成功: {session_id} for user {user_id}")
            return session
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "create_session",
                "user_id": user_id,
                "ip_address": ip_address
            })
            raise
    
    def get_session(self, session_id: str) -> Optional[Session]:
        """获取会话"""
        try:
            session = self.active_sessions.get(session_id)
            
            if session is None:
                # 尝试从存储加载
                session = self._load_session(session_id)
            
            if session and self._is_session_valid(session):
                # 更新最后访问时间
                session.last_accessed = datetime.now()
                self._save_session(session)
                return session
            
            return None
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "get_session",
                "session_id": session_id
            })
            return None
    
    def update_session(self, session_id: str, **kwargs) -> bool:
        """更新会话"""
        try:
            session = self.get_session(session_id)
            if session is None:
                return False
            
            # 更新会话属性
            for key, value in kwargs.items():
                if hasattr(session, key):
                    setattr(session, key, value)
            
            session.last_accessed = datetime.now()
            self._save_session(session)
            
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "update_session",
                "session_id": session_id
            })
            return False
    
    def revoke_session(self, session_id: str) -> bool:
        """撤销会话"""
        try:
            session = self.active_sessions.get(session_id)
            if session:
                session.status = SessionStatus.REVOKED
                self._save_session(session)
                
                # 从活跃会话中移除
                del self.active_sessions[session_id]
                
                # 从用户会话集合中移除
                if session.user_id in self.user_sessions:
                    self.user_sessions[session.user_id].discard(session_id)
                
                self.logger.info(f"会话已撤销: {session_id}")
                return True
            
            return False
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "revoke_session",
                "session_id": session_id
            })
            return False
    
    def revoke_all_user_sessions(self, user_id: str, except_session: Optional[str] = None) -> int:
        """撤销用户的所有会话"""
        revoked_count = 0
        
        try:
            if user_id in self.user_sessions:
                session_ids = list(self.user_sessions[user_id])
                
                for session_id in session_ids:
                    if session_id != except_session:
                        if self.revoke_session(session_id):
                            revoked_count += 1
            
            self.logger.info(f"撤销用户所有会话: {user_id}, 数量: {revoked_count}")
            return revoked_count
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "revoke_all_user_sessions",
                "user_id": user_id
            })
            return 0
    
    def _is_session_valid(self, session: Session) -> bool:
        """检查会话是否有效"""
        if session.status != SessionStatus.ACTIVE:
            return False
        
        if datetime.now() > session.expires_at:
            session.status = SessionStatus.EXPIRED
            return False
        
        return True
    
    def _cleanup_expired_sessions(self, user_id: Optional[str] = None):
        """清理过期会话"""
        expired_sessions = []
        
        for session_id, session in self.active_sessions.items():
            if user_id is None or session.user_id == user_id:
                if not self._is_session_valid(session):
                    expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.revoke_session(session_id)
    
    def _get_user_session_count(self, user_id: str) -> int:
        """获取用户活跃会话数量"""
        return len(self.user_sessions.get(user_id, set()))
    
    def _remove_oldest_session(self, user_id: str):
        """移除最旧的会话"""
        if user_id not in self.user_sessions:
            return
        
        oldest_session = None
        oldest_time = datetime.now()
        
        for session_id in self.user_sessions[user_id]:
            session = self.active_sessions.get(session_id)
            if session and session.created_at < oldest_time:
                oldest_time = session.created_at
                oldest_session = session_id
        
        if oldest_session:
            self.revoke_session(oldest_session)
    
    def _save_session(self, session: Session):
        """保存会话到存储"""
        try:
            session_data = {
                "session_id": session.session_id,
                "user_id": session.user_id,
                "created_at": session.created_at.isoformat(),
                "last_accessed": session.last_accessed.isoformat(),
                "expires_at": session.expires_at.isoformat(),
                "ip_address": session.ip_address,
                "user_agent": session.user_agent,
                "status": session.status.value,
                "mfa_verified": session.mfa_verified,
                "permissions": [p.value for p in session.permissions],
                "metadata": session.metadata
            }
            
            # 使用安全存储保存会话数据
            self.storage.store_user_data(f"session_{session.session_id}", session_data)
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "save_session",
                "session_id": session.session_id
            })
    
    def _load_session(self, session_id: str) -> Optional[Session]:
        """从存储加载会话"""
        try:
            session_data = self.storage.retrieve_user_data(f"session_{session_id}")
            
            if session_data:
                # 重建Session对象
                session = Session(
                    session_id=session_data["session_id"],
                    user_id=session_data["user_id"],
                    created_at=datetime.fromisoformat(session_data["created_at"]),
                    last_accessed=datetime.fromisoformat(session_data["last_accessed"]),
                    expires_at=datetime.fromisoformat(session_data["expires_at"]),
                    ip_address=session_data["ip_address"],
                    user_agent=session_data["user_agent"],
                    status=SessionStatus(session_data["status"]),
                    mfa_verified=session_data["mfa_verified"],
                    permissions={Permission(p) for p in session_data["permissions"]},
                    metadata=session_data["metadata"]
                )
                
                self.active_sessions[session_id] = session
                return session
            
            return None
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "load_session",
                "session_id": session_id
            })
            return None


class AuthenticationManager:
    """认证管理器"""
    
    def __init__(self, config: AuthConfig, storage: SecureStorage):
        self.config = config
        self.storage = storage
        self.logger = LoggerManager().get_logger("security.auth")
        self.error_handler = ErrorHandler()
        
        # 初始化子组件
        self.password_manager = PasswordManager(config)
        self.mfa_manager = MFAManager(config)
        self.session_manager = SessionManager(config, storage)
        
        # 用户数据
        self.users: Dict[str, UserProfile] = {}
        self.login_attempts: Dict[str, List[datetime]] = {}
        
        # 加载用户数据
        self._load_users()
    
    def register_user(self, username: str, email: str, password: str, 
                     role: UserRole = UserRole.USER) -> Tuple[bool, str, Optional[str]]:
        """注册用户"""
        try:
            # 验证用户名和邮箱唯一性
            if self._user_exists(username, email):
                return False, "用户名或邮箱已存在", None
            
            # 验证密码强度
            is_strong, errors = self.password_manager.validate_password_strength(password)
            if not is_strong:
                return False, "密码不符合要求: " + "; ".join(errors), None
            
            # 创建用户
            user_id = str(uuid.uuid4())
            password_hash, salt = self.password_manager.hash_password(password)
            
            user_profile = UserProfile(
                user_id=user_id,
                username=username,
                email=email,
                password_hash=password_hash,
                salt=salt,
                role=role,
                permissions=self._get_default_permissions(role)
            )
            
            # 保存用户
            self.users[user_id] = user_profile
            self._save_user(user_profile)
            
            self.logger.info(f"用户注册成功: {username} ({email})")
            return True, "注册成功", user_id
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "register_user",
                "username": username,
                "email": email
            })
            return False, "注册失败，请稍后重试", None
    
    def authenticate_user(self, username_or_email: str, password: str, 
                         ip_address: str, user_agent: str,
                         mfa_token: Optional[str] = None) -> Tuple[bool, str, Optional[Session]]:
        """认证用户"""
        try:
            # 查找用户
            user_profile = self._find_user(username_or_email)
            if user_profile is None:
                self._record_failed_attempt(username_or_email, ip_address)
                return False, "用户名或密码错误", None
            
            # 检查账户锁定
            if self._is_account_locked(user_profile):
                return False, f"账户已锁定，请在{user_profile.locked_until}后重试", None
            
            # 检查登录尝试次数
            if self._check_rate_limit(username_or_email, ip_address):
                return False, "登录尝试过于频繁，请稍后重试", None
            
            # 验证密码
            if not self.password_manager.verify_password(
                password, user_profile.password_hash, user_profile.salt
            ):
                self._record_failed_attempt(username_or_email, ip_address)
                user_profile.login_attempts += 1
                
                # 检查是否需要锁定账户
                if user_profile.login_attempts >= self.config.max_login_attempts:
                    user_profile.locked_until = (
                        datetime.now() + timedelta(minutes=self.config.lockout_duration_minutes)
                    )
                    self._save_user(user_profile)
                    return False, "登录失败次数过多，账户已被锁定", None
                
                self._save_user(user_profile)
                return False, "用户名或密码错误", None
            
            # 检查MFA
            if user_profile.mfa_enabled:
                if mfa_token is None:
                    return False, "需要多因素认证", None
                
                if not self._verify_mfa(user_profile, mfa_token):
                    self._record_failed_attempt(username_or_email, ip_address)
                    return False, "多因素认证失败", None
            
            # 认证成功，创建会话
            session = self.session_manager.create_session(
                user_profile.user_id, ip_address, user_agent
            )
            
            # 设置会话权限
            session.permissions = user_profile.permissions.copy()
            session.mfa_verified = user_profile.mfa_enabled
            
            # 更新用户信息
            user_profile.last_login = datetime.now()
            user_profile.login_attempts = 0
            user_profile.locked_until = None
            self._save_user(user_profile)
            
            # 清除失败尝试记录
            self._clear_failed_attempts(username_or_email, ip_address)
            
            self.logger.info(f"用户认证成功: {user_profile.username}")
            return True, "认证成功", session
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "authenticate_user",
                "username_or_email": username_or_email,
                "ip_address": ip_address
            })
            return False, "认证失败，请稍后重试", None
    
    def logout_user(self, session_id: str) -> bool:
        """用户登出"""
        try:
            session = self.session_manager.get_session(session_id)
            if session:
                self.session_manager.revoke_session(session_id)
                self.logger.info(f"用户登出: {session.user_id}")
                return True
            
            return False
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "logout_user",
                "session_id": session_id
            })
            return False
    
    def enable_mfa(self, user_id: str) -> Tuple[bool, str, Optional[str], Optional[bytes]]:
        """启用多因素认证"""
        try:
            user_profile = self.users.get(user_id)
            if user_profile is None:
                return False, "用户不存在", None, None
            
            # 生成TOTP密钥
            secret = self.mfa_manager.generate_totp_secret()
            
            # 生成二维码
            qr_code = self.mfa_manager.generate_qr_code(user_profile.email, secret)
            
            # 生成备用代码
            backup_codes = self.mfa_manager.generate_backup_codes()
            
            # 更新用户配置（暂时保存，等待验证）
            user_profile.mfa_secret = secret
            user_profile.backup_codes = backup_codes
            
            self._save_user(user_profile)
            
            return True, "MFA设置已准备就绪，请验证后启用", secret, qr_code
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "enable_mfa",
                "user_id": user_id
            })
            return False, "MFA启用失败", None, None
    
    def verify_and_enable_mfa(self, user_id: str, token: str) -> Tuple[bool, str]:
        """验证并启用MFA"""
        try:
            user_profile = self.users.get(user_id)
            if user_profile is None or user_profile.mfa_secret is None:
                return False, "MFA设置不完整"
            
            # 验证TOTP令牌
            if self.mfa_manager.verify_totp(user_profile.mfa_secret, token):
                user_profile.mfa_enabled = True
                self._save_user(user_profile)
                
                self.logger.info(f"MFA已启用: {user_profile.username}")
                return True, "多因素认证已成功启用"
            else:
                return False, "验证码错误，请重试"
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "verify_and_enable_mfa",
                "user_id": user_id
            })
            return False, "MFA验证失败"
    
    def disable_mfa(self, user_id: str, password: str) -> Tuple[bool, str]:
        """禁用多因素认证"""
        try:
            user_profile = self.users.get(user_id)
            if user_profile is None:
                return False, "用户不存在"
            
            # 验证密码
            if not self.password_manager.verify_password(
                password, user_profile.password_hash, user_profile.salt
            ):
                return False, "密码错误"
            
            # 禁用MFA
            user_profile.mfa_enabled = False
            user_profile.mfa_secret = None
            user_profile.backup_codes = []
            
            self._save_user(user_profile)
            
            self.logger.info(f"MFA已禁用: {user_profile.username}")
            return True, "多因素认证已禁用"
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "disable_mfa",
                "user_id": user_id
            })
            return False, "MFA禁用失败"
    
    def change_password(self, user_id: str, old_password: str, new_password: str) -> Tuple[bool, str]:
        """修改密码"""
        try:
            user_profile = self.users.get(user_id)
            if user_profile is None:
                return False, "用户不存在"
            
            # 验证旧密码
            if not self.password_manager.verify_password(
                old_password, user_profile.password_hash, user_profile.salt
            ):
                return False, "原密码错误"
            
            # 验证新密码强度
            is_strong, errors = self.password_manager.validate_password_strength(new_password)
            if not is_strong:
                return False, "新密码不符合要求: " + "; ".join(errors)
            
            # 更新密码
            new_hash, new_salt = self.password_manager.hash_password(new_password)
            user_profile.password_hash = new_hash
            user_profile.salt = new_salt
            
            self._save_user(user_profile)
            
            # 撤销所有现有会话（除当前会话外）
            self.session_manager.revoke_all_user_sessions(user_id)
            
            self.logger.info(f"密码已更改: {user_profile.username}")
            return True, "密码修改成功"
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "change_password",
                "user_id": user_id
            })
            return False, "密码修改失败"
    
    def _user_exists(self, username: str, email: str) -> bool:
        """检查用户是否存在"""
        for user in self.users.values():
            if user.username == username or user.email == email:
                return True
        return False
    
    def _find_user(self, username_or_email: str) -> Optional[UserProfile]:
        """查找用户"""
        for user in self.users.values():
            if user.username == username_or_email or user.email == username_or_email:
                return user
        return None
    
    def _is_account_locked(self, user_profile: UserProfile) -> bool:
        """检查账户是否锁定"""
        if user_profile.locked_until is None:
            return False
        
        if datetime.now() > user_profile.locked_until:
            # 锁定期已过，解锁账户
            user_profile.locked_until = None
            user_profile.login_attempts = 0
            self._save_user(user_profile)
            return False
        
        return True
    
    def _check_rate_limit(self, identifier: str, ip_address: str) -> bool:
        """检查速率限制"""
        if not self.config.enable_rate_limiting:
            return False
        
        now = datetime.now()
        window = timedelta(minutes=15)  # 15分钟窗口
        max_attempts = 10  # 最大尝试次数
        
        # 清理过期记录
        key = f"{identifier}:{ip_address}"
        if key in self.login_attempts:
            self.login_attempts[key] = [
                attempt for attempt in self.login_attempts[key]
                if now - attempt < window
            ]
        
        # 检查尝试次数
        attempts = len(self.login_attempts.get(key, []))
        return attempts >= max_attempts
    
    def _record_failed_attempt(self, identifier: str, ip_address: str):
        """记录失败尝试"""
        key = f"{identifier}:{ip_address}"
        if key not in self.login_attempts:
            self.login_attempts[key] = []
        
        self.login_attempts[key].append(datetime.now())
    
    def _clear_failed_attempts(self, identifier: str, ip_address: str):
        """清除失败尝试记录"""
        key = f"{identifier}:{ip_address}"
        if key in self.login_attempts:
            del self.login_attempts[key]
    
    def _verify_mfa(self, user_profile: UserProfile, token: str) -> bool:
        """验证MFA"""
        if user_profile.mfa_secret is None:
            return False
        
        # 尝试TOTP验证
        if self.mfa_manager.verify_totp(user_profile.mfa_secret, token):
            return True
        
        # 尝试备用代码验证
        if self.mfa_manager.verify_backup_code(user_profile, token):
            self._save_user(user_profile)  # 保存更新的备用代码列表
            return True
        
        return False
    
    def _get_default_permissions(self, role: UserRole) -> Set[Permission]:
        """获取角色默认权限"""
        permissions = set()
        
        if role == UserRole.GUEST:
            permissions.add(Permission.READ)
        
        elif role == UserRole.USER:
            permissions.update([
                Permission.READ, Permission.WRITE,
                Permission.PROJECT_CREATE, Permission.PROJECT_EDIT,
                Permission.AI_GENERATE, Permission.DATA_EXPORT
            ])
        
        elif role == UserRole.PREMIUM:
            permissions.update([
                Permission.READ, Permission.WRITE, Permission.DELETE,
                Permission.PROJECT_CREATE, Permission.PROJECT_EDIT, Permission.PROJECT_SHARE,
                Permission.AI_GENERATE, Permission.AI_ADVANCED,
                Permission.DATA_EXPORT, Permission.DATA_IMPORT
            ])
        
        elif role == UserRole.MODERATOR:
            permissions.update([
                Permission.READ, Permission.WRITE, Permission.DELETE,
                Permission.PROJECT_CREATE, Permission.PROJECT_EDIT, Permission.PROJECT_SHARE,
                Permission.AI_GENERATE, Permission.AI_ADVANCED,
                Permission.DATA_EXPORT, Permission.DATA_IMPORT,
                Permission.USER_MANAGE
            ])
        
        elif role in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
            permissions = set(Permission)  # 所有权限
        
        return permissions
    
    def _save_user(self, user_profile: UserProfile):
        """保存用户数据"""
        try:
            user_data = {
                "user_id": user_profile.user_id,
                "username": user_profile.username,
                "email": user_profile.email,
                "password_hash": user_profile.password_hash,
                "salt": user_profile.salt,
                "role": user_profile.role.value,
                "permissions": [p.value for p in user_profile.permissions],
                "created_at": user_profile.created_at.isoformat(),
                "last_login": user_profile.last_login.isoformat() if user_profile.last_login else None,
                "login_attempts": user_profile.login_attempts,
                "locked_until": user_profile.locked_until.isoformat() if user_profile.locked_until else None,
                "mfa_enabled": user_profile.mfa_enabled,
                "mfa_secret": user_profile.mfa_secret,
                "backup_codes": user_profile.backup_codes,
                "profile_data": user_profile.profile_data,
                "preferences": user_profile.preferences
            }
            
            self.storage.store_user_data(f"profile_{user_profile.user_id}", user_data)
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "save_user",
                "user_id": user_profile.user_id
            })
    
    def _load_users(self):
        """加载用户数据"""
        try:
            # 这里应该从存储中加载所有用户
            # 为了简化，暂时创建一个默认管理员用户
            admin_id = "admin_001"
            if admin_id not in self.users:
                password_hash, salt = self.password_manager.hash_password("admin123")
                
                admin_user = UserProfile(
                    user_id=admin_id,
                    username="admin",
                    email="<EMAIL>",
                    password_hash=password_hash,
                    salt=salt,
                    role=UserRole.SUPER_ADMIN,
                    permissions=set(Permission)
                )
                
                self.users[admin_id] = admin_user
                self._save_user(admin_user)
                
                self.logger.info("默认管理员用户已创建")
            
        except Exception as e:
            self.error_handler.handle_error(e, {"action": "load_users"})


# 便捷函数
def create_auth_manager(storage_path: Union[str, Path], 
                       config: Optional[AuthConfig] = None) -> AuthenticationManager:
    """创建认证管理器"""
    from .encryption import create_secure_storage, EncryptionConfig
    
    if config is None:
        config = AuthConfig()
    
    # 创建安全存储
    encryption_config = EncryptionConfig()
    storage = create_secure_storage(storage_path, encryption_config)
    
    return AuthenticationManager(config, storage)


if __name__ == "__main__":
    # 示例用法
    import tempfile
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建认证管理器
        auth_config = AuthConfig(
            min_password_length=8,
            require_mfa_for_admin=True,
            max_login_attempts=3
        )
        
        auth_manager = create_auth_manager(temp_dir, auth_config)
        
        # 注册用户
        success, message, user_id = auth_manager.register_user(
            "testuser", "<EMAIL>", "TestPass123!"
        )
        print(f"注册结果: {success}, {message}, {user_id}")
        
        if success:
            # 用户认证
            auth_success, auth_message, session = auth_manager.authenticate_user(
                "testuser", "TestPass123!", "127.0.0.1", "Test Client"
            )
            print(f"认证结果: {auth_success}, {auth_message}")
            
            if auth_success and session:
                print(f"会话ID: {session.session_id}")
                print(f"用户权限: {[p.value for p in session.permissions]}")
                
                # 启用MFA
                mfa_success, mfa_message, secret, qr_code = auth_manager.enable_mfa(user_id)
                print(f"MFA启用: {mfa_success}, {mfa_message}")
                
                if mfa_success and secret:
                    print(f"TOTP密钥: {secret}")
                    print(f"二维码大小: {len(qr_code) if qr_code else 0} bytes")
                
                # 登出
                logout_success = auth_manager.logout_user(session.session_id)
                print(f"登出结果: {logout_success}")