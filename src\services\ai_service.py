"""AI服务管理器

统一管理AI服务调用，包括模型选择、负载均衡、降级策略、
用量监控、成本控制和缓存机制。
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Union, AsyncGenerator
from dataclasses import dataclass, asdict
from contextlib import asynccontextmanager

import redis.asyncio as redis
from sqlalchemy.ext.asyncio import AsyncSession

from ..ai import (
    BaseAIAdapter, AIModelType, AIMessage, AIResponse,
    OpenAIAdapter, ClaudeAdapter,
    ZhipuAIAdapter, WenxinAdapter, TongyiAdapter,
    create_openai_adapter, create_claude_adapter,
    create_zhipu_adapter, create_wenxin_adapter, create_tongyi_adapter
)
from ..config.settings import get_settings

logger = logging.getLogger(__name__)


class ServicePriority(Enum):
    """服务优先级"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class LoadBalanceStrategy(Enum):
    """负载均衡策略"""
    ROUND_ROBIN = "round_robin"  # 轮询
    WEIGHTED = "weighted"  # 加权
    LEAST_COST = "least_cost"  # 最低成本
    FASTEST = "fastest"  # 最快响应


@dataclass
class ServiceConfig:
    """服务配置"""
    adapter_class: type
    priority: ServicePriority
    weight: float = 1.0
    max_requests_per_minute: int = 60
    cost_per_1k_tokens: float = 0.002
    enabled: bool = True
    fallback_models: List[AIModelType] = None

    def __post_init__(self):
        if self.fallback_models is None:
            self.fallback_models = []


@dataclass
class UsageStats:
    """使用统计"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_tokens: int = 0
    total_cost: float = 0.0
    avg_response_time: float = 0.0
    last_request_time: Optional[datetime] = None


@dataclass
class ServiceHealth:
    """服务健康状态"""
    is_healthy: bool = True
    last_check: Optional[datetime] = None
    error_count: int = 0
    consecutive_errors: int = 0
    response_time: float = 0.0


class AIService:
    """AI服务包装器"""
    
    def __init__(
        self,
        adapter: BaseAIAdapter,
        config: ServiceConfig,
        redis_client: Optional[redis.Redis] = None
    ):
        self.adapter = adapter
        self.config = config
        self.redis_client = redis_client
        self.stats = UsageStats()
        self.health = ServiceHealth()
        self._request_times = []
        self._last_requests = []
    
    async def generate(
        self,
        messages: List[AIMessage],
        model: AIModelType,
        **kwargs
    ) -> AIResponse:
        """生成响应"""
        start_time = time.time()
        
        try:
            # 检查速率限制
            if not await self._check_rate_limit():
                raise Exception(f"Rate limit exceeded for {self.adapter.__class__.__name__}")
            
            # 调用适配器
            response = await self.adapter.generate(messages, model, **kwargs)
            
            # 更新统计信息
            await self._update_stats(start_time, response, success=True)
            
            return response
            
        except Exception as e:
            # 更新错误统计
            await self._update_stats(start_time, None, success=False, error=str(e))
            raise
    
    async def generate_stream(
        self,
        messages: List[AIMessage],
        model: AIModelType,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """流式生成响应"""
        start_time = time.time()
        
        try:
            # 检查速率限制
            if not await self._check_rate_limit():
                raise Exception(f"Rate limit exceeded for {self.adapter.__class__.__name__}")
            
            # 流式调用适配器
            async for chunk in self.adapter.generate_stream(messages, model, **kwargs):
                yield chunk
            
            # 更新统计信息（流式响应无法获取完整响应信息）
            await self._update_stats(start_time, None, success=True)
            
        except Exception as e:
            # 更新错误统计
            await self._update_stats(start_time, None, success=False, error=str(e))
            raise
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            start_time = time.time()
            is_healthy = await self.adapter.health_check()
            response_time = time.time() - start_time
            
            self.health.is_healthy = is_healthy
            self.health.last_check = datetime.now()
            self.health.response_time = response_time
            
            if is_healthy:
                self.health.consecutive_errors = 0
            else:
                self.health.consecutive_errors += 1
                self.health.error_count += 1
            
            return is_healthy
            
        except Exception as e:
            logger.error(f"Health check failed for {self.adapter.__class__.__name__}: {e}")
            self.health.is_healthy = False
            self.health.consecutive_errors += 1
            self.health.error_count += 1
            return False
    
    async def _check_rate_limit(self) -> bool:
        """检查速率限制"""
        if not self.redis_client:
            # 简单的内存限制检查
            now = time.time()
            # 清理1分钟前的请求记录
            self._last_requests = [t for t in self._last_requests if now - t < 60]
            
            if len(self._last_requests) >= self.config.max_requests_per_minute:
                return False
            
            self._last_requests.append(now)
            return True
        
        # 使用Redis进行分布式速率限制
        key = f"rate_limit:{self.adapter.__class__.__name__}"
        current = await self.redis_client.get(key)
        
        if current is None:
            await self.redis_client.setex(key, 60, 1)
            return True
        
        if int(current) >= self.config.max_requests_per_minute:
            return False
        
        await self.redis_client.incr(key)
        return True
    
    async def _update_stats(
        self,
        start_time: float,
        response: Optional[AIResponse],
        success: bool,
        error: Optional[str] = None
    ):
        """更新统计信息"""
        response_time = time.time() - start_time
        
        self.stats.total_requests += 1
        self.stats.last_request_time = datetime.now()
        
        if success:
            self.stats.successful_requests += 1
            if response:
                self.stats.total_tokens += response.usage.total_tokens
                self.stats.total_cost += response.cost
        else:
            self.stats.failed_requests += 1
            logger.error(f"Request failed for {self.adapter.__class__.__name__}: {error}")
        
        # 更新平均响应时间
        self._request_times.append(response_time)
        if len(self._request_times) > 100:  # 只保留最近100次请求的时间
            self._request_times.pop(0)
        
        self.stats.avg_response_time = sum(self._request_times) / len(self._request_times)


class AIServiceManager:
    """AI服务管理器"""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.services: Dict[str, AIService] = {}
        self.redis_client: Optional[redis.Redis] = None
        self.load_balance_strategy = LoadBalanceStrategy.WEIGHTED
        self._round_robin_index = 0
        
        # 初始化Redis客户端
        if redis_url:
            self.redis_client = redis.from_url(redis_url)
    
    async def initialize(self):
        """初始化服务管理器"""
        settings = get_settings()
        
        # 配置各种AI服务
        service_configs = {
            "openai": ServiceConfig(
                adapter_class=OpenAIAdapter,
                priority=ServicePriority.HIGH,
                weight=3.0,
                max_requests_per_minute=100,
                cost_per_1k_tokens=0.002,
                enabled=bool(settings.OPENAI_API_KEY)
            ),
            "claude": ServiceConfig(
                adapter_class=ClaudeAdapter,
                priority=ServicePriority.HIGH,
                weight=2.5,
                max_requests_per_minute=80,
                cost_per_1k_tokens=0.003,
                enabled=bool(settings.ANTHROPIC_API_KEY)
            ),
            "zhipu": ServiceConfig(
                adapter_class=ZhipuAIAdapter,
                priority=ServicePriority.MEDIUM,
                weight=2.0,
                max_requests_per_minute=120,
                cost_per_1k_tokens=0.001,
                enabled=bool(getattr(settings, 'ZHIPU_API_KEY', None))
            ),
            "wenxin": ServiceConfig(
                adapter_class=WenxinAdapter,
                priority=ServicePriority.MEDIUM,
                weight=1.8,
                max_requests_per_minute=100,
                cost_per_1k_tokens=0.0008,
                enabled=bool(getattr(settings, 'WENXIN_API_KEY', None))
            ),
            "tongyi": ServiceConfig(
                adapter_class=TongyiAdapter,
                priority=ServicePriority.MEDIUM,
                weight=1.5,
                max_requests_per_minute=90,
                cost_per_1k_tokens=0.0012,
                enabled=bool(getattr(settings, 'TONGYI_API_KEY', None))
            )
        }
        
        # 创建服务实例
        for name, config in service_configs.items():
            if config.enabled:
                try:
                    adapter = await self._create_adapter(name, config.adapter_class)
                    service = AIService(adapter, config, self.redis_client)
                    self.services[name] = service
                    logger.info(f"Initialized AI service: {name}")
                except Exception as e:
                    logger.error(f"Failed to initialize AI service {name}: {e}")
        
        # 启动健康检查
        asyncio.create_task(self._health_check_loop())
    
    async def _create_adapter(self, name: str, adapter_class: type) -> BaseAIAdapter:
        """创建适配器实例"""
        if adapter_class == OpenAIAdapter:
            return await create_openai_adapter()
        elif adapter_class == ClaudeAdapter:
            return await create_claude_adapter()
        elif adapter_class == ZhipuAIAdapter:
            return await create_zhipu_adapter()
        elif adapter_class == WenxinAdapter:
            return await create_wenxin_adapter()
        elif adapter_class == TongyiAdapter:
            return await create_tongyi_adapter()
        else:
            raise ValueError(f"Unknown adapter class: {adapter_class}")
    
    async def generate(
        self,
        messages: List[AIMessage],
        model: Optional[AIModelType] = None,
        preferred_service: Optional[str] = None,
        **kwargs
    ) -> AIResponse:
        """生成响应"""
        # 选择服务
        service = await self._select_service(model, preferred_service)
        if not service:
            raise Exception("No available AI service")
        
        # 确定模型
        if not model:
            model = await self._get_default_model(service)
        
        try:
            # 尝试从缓存获取
            cached_response = await self._get_cached_response(messages, model, **kwargs)
            if cached_response:
                return cached_response
            
            # 生成响应
            response = await service.generate(messages, model, **kwargs)
            
            # 缓存响应
            await self._cache_response(messages, model, response, **kwargs)
            
            return response
            
        except Exception as e:
            logger.error(f"Generation failed with {service.adapter.__class__.__name__}: {e}")
            
            # 尝试降级到其他服务
            fallback_service = await self._get_fallback_service(service)
            if fallback_service:
                logger.info(f"Falling back to {fallback_service.adapter.__class__.__name__}")
                return await fallback_service.generate(messages, model, **kwargs)
            
            raise
    
    async def generate_stream(
        self,
        messages: List[AIMessage],
        model: Optional[AIModelType] = None,
        preferred_service: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """流式生成响应"""
        # 选择服务
        service = await self._select_service(model, preferred_service)
        if not service:
            raise Exception("No available AI service")
        
        # 确定模型
        if not model:
            model = await self._get_default_model(service)
        
        try:
            async for chunk in service.generate_stream(messages, model, **kwargs):
                yield chunk
        except Exception as e:
            logger.error(f"Stream generation failed with {service.adapter.__class__.__name__}: {e}")
            
            # 尝试降级到其他服务
            fallback_service = await self._get_fallback_service(service)
            if fallback_service:
                logger.info(f"Falling back to {fallback_service.adapter.__class__.__name__}")
                async for chunk in fallback_service.generate_stream(messages, model, **kwargs):
                    yield chunk
            else:
                raise
    
    async def _select_service(
        self,
        model: Optional[AIModelType] = None,
        preferred_service: Optional[str] = None
    ) -> Optional[AIService]:
        """选择服务"""
        # 过滤可用服务
        available_services = {
            name: service for name, service in self.services.items()
            if service.config.enabled and service.health.is_healthy
        }
        
        if not available_services:
            return None
        
        # 如果指定了首选服务
        if preferred_service and preferred_service in available_services:
            return available_services[preferred_service]
        
        # 如果指定了模型，过滤支持该模型的服务
        if model:
            model_services = {}
            for name, service in available_services.items():
                supported_models = await service.adapter.get_supported_models()
                if model in supported_models:
                    model_services[name] = service
            available_services = model_services or available_services
        
        # 根据负载均衡策略选择服务
        if self.load_balance_strategy == LoadBalanceStrategy.ROUND_ROBIN:
            return self._round_robin_select(list(available_services.values()))
        elif self.load_balance_strategy == LoadBalanceStrategy.WEIGHTED:
            return self._weighted_select(available_services)
        elif self.load_balance_strategy == LoadBalanceStrategy.LEAST_COST:
            return self._least_cost_select(available_services)
        elif self.load_balance_strategy == LoadBalanceStrategy.FASTEST:
            return self._fastest_select(available_services)
        
        # 默认返回第一个可用服务
        return next(iter(available_services.values()))
    
    def _round_robin_select(self, services: List[AIService]) -> AIService:
        """轮询选择"""
        if not services:
            return None
        
        service = services[self._round_robin_index % len(services)]
        self._round_robin_index += 1
        return service
    
    def _weighted_select(self, services: Dict[str, AIService]) -> AIService:
        """加权选择"""
        if not services:
            return None
        
        # 计算权重总和
        total_weight = sum(service.config.weight for service in services.values())
        
        # 根据权重选择
        import random
        rand = random.uniform(0, total_weight)
        current_weight = 0
        
        for service in services.values():
            current_weight += service.config.weight
            if rand <= current_weight:
                return service
        
        # 默认返回第一个
        return next(iter(services.values()))
    
    def _least_cost_select(self, services: Dict[str, AIService]) -> AIService:
        """最低成本选择"""
        if not services:
            return None
        
        return min(services.values(), key=lambda s: s.config.cost_per_1k_tokens)
    
    def _fastest_select(self, services: Dict[str, AIService]) -> AIService:
        """最快响应选择"""
        if not services:
            return None
        
        return min(services.values(), key=lambda s: s.stats.avg_response_time or float('inf'))
    
    async def _get_default_model(self, service: AIService) -> AIModelType:
        """获取默认模型"""
        supported_models = await service.adapter.get_supported_models()
        return supported_models[0] if supported_models else AIModelType.GPT_3_5_TURBO
    
    async def _get_fallback_service(self, failed_service: AIService) -> Optional[AIService]:
        """获取降级服务"""
        available_services = [
            service for service in self.services.values()
            if (service != failed_service and 
                service.config.enabled and 
                service.health.is_healthy)
        ]
        
        if not available_services:
            return None
        
        # 选择优先级最高的服务
        return max(available_services, key=lambda s: s.config.priority.value)
    
    async def _get_cached_response(
        self,
        messages: List[AIMessage],
        model: AIModelType,
        **kwargs
    ) -> Optional[AIResponse]:
        """获取缓存响应"""
        if not self.redis_client:
            return None
        
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(messages, model, **kwargs)
            
            # 从Redis获取缓存
            cached_data = await self.redis_client.get(cache_key)
            if cached_data:
                data = json.loads(cached_data)
                return AIResponse(**data)
            
        except Exception as e:
            logger.warning(f"Failed to get cached response: {e}")
        
        return None
    
    async def _cache_response(
        self,
        messages: List[AIMessage],
        model: AIModelType,
        response: AIResponse,
        **kwargs
    ):
        """缓存响应"""
        if not self.redis_client:
            return
        
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(messages, model, **kwargs)
            
            # 序列化响应
            response_data = asdict(response)
            
            # 缓存到Redis（1小时过期）
            await self.redis_client.setex(
                cache_key,
                3600,
                json.dumps(response_data, default=str)
            )
            
        except Exception as e:
            logger.warning(f"Failed to cache response: {e}")
    
    def _generate_cache_key(
        self,
        messages: List[AIMessage],
        model: AIModelType,
        **kwargs
    ) -> str:
        """生成缓存键"""
        import hashlib
        
        # 创建唯一标识
        content = {
            "messages": [asdict(msg) for msg in messages],
            "model": model.value,
            "kwargs": kwargs
        }
        
        content_str = json.dumps(content, sort_keys=True)
        hash_obj = hashlib.md5(content_str.encode())
        
        return f"ai_response:{hash_obj.hexdigest()}"
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                for name, service in self.services.items():
                    await service.health_check()
                    logger.debug(f"Health check for {name}: {service.health.is_healthy}")
                
                # 每30秒检查一次
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"Health check loop error: {e}")
                await asyncio.sleep(60)  # 出错时等待更长时间
    
    async def get_service_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取服务统计信息"""
        stats = {}
        
        for name, service in self.services.items():
            stats[name] = {
                "config": asdict(service.config),
                "stats": asdict(service.stats),
                "health": asdict(service.health)
            }
        
        return stats
    
    async def set_load_balance_strategy(self, strategy: LoadBalanceStrategy):
        """设置负载均衡策略"""
        self.load_balance_strategy = strategy
        logger.info(f"Load balance strategy set to: {strategy.value}")
    
    async def enable_service(self, service_name: str):
        """启用服务"""
        if service_name in self.services:
            self.services[service_name].config.enabled = True
            logger.info(f"Service {service_name} enabled")
    
    async def disable_service(self, service_name: str):
        """禁用服务"""
        if service_name in self.services:
            self.services[service_name].config.enabled = False
            logger.info(f"Service {service_name} disabled")
    
    async def close(self):
        """关闭服务管理器"""
        if self.redis_client:
            await self.redis_client.close()
        
        for service in self.services.values():
            if hasattr(service.adapter, 'close'):
                await service.adapter.close()
        
        logger.info("AI Service Manager closed")


# 全局服务管理器实例
_service_manager: Optional[AIServiceManager] = None


async def get_ai_service_manager() -> AIServiceManager:
    """获取AI服务管理器实例"""
    global _service_manager
    
    if _service_manager is None:
        settings = get_settings()
        redis_url = getattr(settings, 'REDIS_URL', None)
        
        _service_manager = AIServiceManager(redis_url)
        await _service_manager.initialize()
    
    return _service_manager


@asynccontextmanager
async def ai_service_context():
    """AI服务上下文管理器"""
    manager = await get_ai_service_manager()
    try:
        yield manager
    finally:
        # 这里可以添加清理逻辑
        pass