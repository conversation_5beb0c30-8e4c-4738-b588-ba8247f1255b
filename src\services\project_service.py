"""项目管理服务

提供完整的项目管理业务逻辑，包括：
- 项目CRUD操作
- 项目统计分析
- 项目模板管理
- 项目导入导出
- 项目备份恢复
"""

import os
import json
import shutil
import zipfile
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union
from pathlib import Path
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from fastapi import HTTPException, status

from ..models.project import (
    Project, ProjectStatus, ProjectGenre,
    ProjectCreateSchema, ProjectUpdateSchema, ProjectResponseSchema,
    ProjectListSchema, ProjectStatisticsSchema
)
from ..core.database import get_db
from ..core.config import get_settings
from ..utils.file_utils import ensure_directory, safe_filename
from ..utils.logger import get_logger

logger = get_logger(__name__)
settings = get_settings()

class ProjectService:
    """项目管理服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.projects_dir = Path(settings.PROJECTS_DIR)
        self.templates_dir = Path(settings.TEMPLATES_DIR)
        self.backups_dir = Path(settings.BACKUPS_DIR)
        
        # 确保目录存在
        ensure_directory(self.projects_dir)
        ensure_directory(self.templates_dir)
        ensure_directory(self.backups_dir)
    
    # CRUD操作
    
    def create_project(self, project_data: ProjectCreateSchema, user_id: UUID) -> ProjectResponseSchema:
        """创建新项目"""
        try:
            # 创建项目实例
            project = Project(
                **project_data.dict(exclude_unset=True),
                created_by=user_id
            )
            
            # 设置默认设置
            if not project.settings:
                project.settings = project.get_default_settings()
            
            # 创建项目目录
            if not project.project_path:
                project_dir = self.projects_dir / safe_filename(project.title)
                project_dir = self._ensure_unique_path(project_dir)
                project.project_path = str(project_dir)
            
            ensure_directory(Path(project.project_path))
            
            # 保存到数据库
            self.db.add(project)
            self.db.commit()
            self.db.refresh(project)
            
            # 创建项目结构
            self._create_project_structure(project)
            
            logger.info(f"Created project: {project.title} (ID: {project.id})")
            return ProjectResponseSchema.from_orm(project)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create project: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建项目失败: {str(e)}"
            )
    
    def get_project(self, project_id: UUID, user_id: UUID) -> Optional[ProjectResponseSchema]:
        """获取单个项目"""
        project = self.db.query(Project).filter(
            and_(
                Project.id == project_id,
                Project.created_by == user_id,
                Project.deleted_at.is_(None)
            )
        ).first()
        
        if not project:
            return None
        
        # 更新统计信息
        project.update_statistics()
        project.progress_percentage = project.calculate_progress()
        self.db.commit()
        
        return ProjectResponseSchema.from_orm(project)
    
    def get_projects(
        self,
        user_id: UUID,
        skip: int = 0,
        limit: int = 20,
        status: Optional[ProjectStatus] = None,
        genre: Optional[ProjectGenre] = None,
        search: Optional[str] = None,
        sort_by: str = "updated_at",
        sort_order: str = "desc"
    ) -> List[ProjectListSchema]:
        """获取项目列表"""
        query = self.db.query(Project).filter(
            and_(
                Project.created_by == user_id,
                Project.deleted_at.is_(None)
            )
        )
        
        # 状态筛选
        if status:
            query = query.filter(Project.status == status)
        
        # 类型筛选
        if genre:
            query = query.filter(Project.genre == genre)
        
        # 搜索
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    Project.title.ilike(search_term),
                    Project.description.ilike(search_term)
                )
            )
        
        # 排序
        if hasattr(Project, sort_by):
            order_func = desc if sort_order.lower() == "desc" else asc
            query = query.order_by(order_func(getattr(Project, sort_by)))
        
        # 分页
        projects = query.offset(skip).limit(limit).all()
        
        return [ProjectListSchema.from_orm(project) for project in projects]
    
    def update_project(
        self,
        project_id: UUID,
        project_data: ProjectUpdateSchema,
        user_id: UUID
    ) -> Optional[ProjectResponseSchema]:
        """更新项目"""
        project = self.db.query(Project).filter(
            and_(
                Project.id == project_id,
                Project.created_by == user_id,
                Project.deleted_at.is_(None)
            )
        ).first()
        
        if not project:
            return None
        
        try:
            # 更新字段
            update_data = project_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(project, field, value)
            
            # 更新最后编辑时间
            project.update_last_edited()
            
            self.db.commit()
            self.db.refresh(project)
            
            logger.info(f"Updated project: {project.title} (ID: {project.id})")
            return ProjectResponseSchema.from_orm(project)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to update project {project_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"更新项目失败: {str(e)}"
            )
    
    def delete_project(self, project_id: UUID, user_id: UUID) -> bool:
        """删除项目（软删除）"""
        project = self.db.query(Project).filter(
            and_(
                Project.id == project_id,
                Project.created_by == user_id,
                Project.deleted_at.is_(None)
            )
        ).first()
        
        if not project:
            return False
        
        try:
            # 软删除
            project.deleted_at = datetime.utcnow()
            self.db.commit()
            
            logger.info(f"Deleted project: {project.title} (ID: {project.id})")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to delete project {project_id}: {str(e)}")
            return False
    
    # 统计分析
    
    def get_project_statistics(self, user_id: UUID) -> Dict[str, Any]:
        """获取用户项目统计信息"""
        projects = self.db.query(Project).filter(
            and_(
                Project.created_by == user_id,
                Project.deleted_at.is_(None)
            )
        ).all()
        
        total_projects = len(projects)
        total_words = sum(p.total_words for p in projects)
        total_chapters = sum(p.total_chapters for p in projects)
        
        # 按状态统计
        status_stats = {}
        for status in ProjectStatus:
            count = len([p for p in projects if p.status == status])
            status_stats[status.value] = count
        
        # 按类型统计
        genre_stats = {}
        for genre in ProjectGenre:
            count = len([p for p in projects if p.genre == genre])
            genre_stats[genre.value] = count
        
        # 最近活跃项目
        recent_projects = sorted(
            [p for p in projects if p.last_edited_at],
            key=lambda x: x.last_edited_at,
            reverse=True
        )[:5]
        
        return {
            "total_projects": total_projects,
            "total_words": total_words,
            "total_chapters": total_chapters,
            "status_distribution": status_stats,
            "genre_distribution": genre_stats,
            "recent_projects": [
                ProjectListSchema.from_orm(p) for p in recent_projects
            ],
            "average_words_per_project": total_words / total_projects if total_projects > 0 else 0,
            "completion_rate": len([p for p in projects if p.status == ProjectStatus.COMPLETED]) / total_projects * 100 if total_projects > 0 else 0
        }
    
    def get_writing_progress(self, user_id: UUID, days: int = 30) -> Dict[str, Any]:
        """获取写作进度统计"""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # 这里需要根据实际的章节或写作记录表来统计
        # 暂时返回模拟数据
        return {
            "period_days": days,
            "start_date": start_date,
            "end_date": end_date,
            "daily_word_counts": [],  # 需要从写作记录中获取
            "total_words_written": 0,
            "average_daily_words": 0,
            "most_productive_day": None,
            "writing_streak": 0
        }
    
    # 模板管理
    
    def create_template(self, project_id: UUID, user_id: UUID, template_name: str) -> bool:
        """将项目创建为模板"""
        project = self.db.query(Project).filter(
            and_(
                Project.id == project_id,
                Project.created_by == user_id,
                Project.deleted_at.is_(None)
            )
        ).first()
        
        if not project:
            return False
        
        try:
            # 创建模板项目副本
            template_project = Project(
                title=template_name,
                description=f"基于项目 '{project.title}' 创建的模板",
                genre=project.genre,
                status=ProjectStatus.DRAFT,
                tags=project.tags,
                target_words=project.target_words,
                target_chapters=project.target_chapters,
                settings=project.settings,
                is_template=True,
                created_by=user_id
            )
            
            # 创建模板目录
            template_dir = self.templates_dir / safe_filename(template_name)
            template_dir = self._ensure_unique_path(template_dir)
            template_project.project_path = str(template_dir)
            
            # 复制项目文件
            if os.path.exists(project.project_path):
                shutil.copytree(project.project_path, template_dir)
            
            self.db.add(template_project)
            self.db.commit()
            
            logger.info(f"Created template: {template_name} from project {project.title}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create template: {str(e)}")
            return False
    
    def get_templates(self, user_id: UUID) -> List[ProjectListSchema]:
        """获取用户模板列表"""
        templates = self.db.query(Project).filter(
            and_(
                Project.created_by == user_id,
                Project.is_template == True,
                Project.deleted_at.is_(None)
            )
        ).order_by(desc(Project.created_at)).all()
        
        return [ProjectListSchema.from_orm(template) for template in templates]
    
    def create_from_template(
        self,
        template_id: UUID,
        project_name: str,
        user_id: UUID
    ) -> Optional[ProjectResponseSchema]:
        """从模板创建项目"""
        template = self.db.query(Project).filter(
            and_(
                Project.id == template_id,
                Project.is_template == True,
                Project.deleted_at.is_(None)
            )
        ).first()
        
        if not template:
            return None
        
        try:
            # 创建新项目
            new_project = Project(
                title=project_name,
                description=template.description,
                genre=template.genre,
                status=ProjectStatus.DRAFT,
                tags=template.tags,
                target_words=template.target_words,
                target_chapters=template.target_chapters,
                settings=template.settings,
                is_template=False,
                created_by=user_id
            )
            
            # 创建项目目录
            project_dir = self.projects_dir / safe_filename(project_name)
            project_dir = self._ensure_unique_path(project_dir)
            new_project.project_path = str(project_dir)
            
            # 复制模板文件
            if os.path.exists(template.project_path):
                shutil.copytree(template.project_path, project_dir)
            
            self.db.add(new_project)
            self.db.commit()
            self.db.refresh(new_project)
            
            logger.info(f"Created project from template: {project_name}")
            return ProjectResponseSchema.from_orm(new_project)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create project from template: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"从模板创建项目失败: {str(e)}"
            )
    
    # 导入导出
    
    def export_project(
        self,
        project_id: UUID,
        user_id: UUID,
        export_format: str = "zip"
    ) -> Optional[str]:
        """导出项目"""
        project = self.db.query(Project).filter(
            and_(
                Project.id == project_id,
                Project.created_by == user_id,
                Project.deleted_at.is_(None)
            )
        ).first()
        
        if not project:
            return None
        
        try:
            export_dir = Path(settings.EXPORTS_DIR)
            ensure_directory(export_dir)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_filename = f"{safe_filename(project.title)}_{timestamp}.{export_format}"
            export_path = export_dir / export_filename
            
            if export_format == "zip":
                with zipfile.ZipFile(export_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    # 添加项目元数据
                    project_info = {
                        "id": str(project.id),
                        "title": project.title,
                        "description": project.description,
                        "genre": project.genre.value,
                        "status": project.status.value,
                        "tags": project.tags,
                        "settings": project.settings,
                        "exported_at": datetime.utcnow().isoformat()
                    }
                    zipf.writestr("project_info.json", json.dumps(project_info, indent=2, ensure_ascii=False))
                    
                    # 添加项目文件
                    if os.path.exists(project.project_path):
                        for root, dirs, files in os.walk(project.project_path):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arc_path = os.path.relpath(file_path, project.project_path)
                                zipf.write(file_path, arc_path)
            
            logger.info(f"Exported project: {project.title} to {export_path}")
            return str(export_path)
            
        except Exception as e:
            logger.error(f"Failed to export project {project_id}: {str(e)}")
            return None
    
    def import_project(self, import_file: str, user_id: UUID) -> Optional[ProjectResponseSchema]:
        """导入项目"""
        try:
            import_path = Path(import_file)
            if not import_path.exists():
                return None
            
            with zipfile.ZipFile(import_path, 'r') as zipf:
                # 读取项目信息
                project_info_data = zipf.read("project_info.json")
                project_info = json.loads(project_info_data)
                
                # 创建新项目
                new_project = Project(
                    title=f"{project_info['title']}_imported",
                    description=project_info.get('description'),
                    genre=ProjectGenre(project_info.get('genre', 'other')),
                    status=ProjectStatus.DRAFT,
                    tags=project_info.get('tags'),
                    settings=project_info.get('settings'),
                    created_by=user_id
                )
                
                # 创建项目目录
                project_dir = self.projects_dir / safe_filename(new_project.title)
                project_dir = self._ensure_unique_path(project_dir)
                new_project.project_path = str(project_dir)
                
                # 解压文件
                zipf.extractall(project_dir)
                
                self.db.add(new_project)
                self.db.commit()
                self.db.refresh(new_project)
                
                logger.info(f"Imported project: {new_project.title}")
                return ProjectResponseSchema.from_orm(new_project)
                
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to import project: {str(e)}")
            return None
    
    # 备份恢复
    
    def backup_project(self, project_id: UUID, user_id: UUID) -> Optional[str]:
        """备份项目"""
        project = self.db.query(Project).filter(
            and_(
                Project.id == project_id,
                Project.created_by == user_id,
                Project.deleted_at.is_(None)
            )
        ).first()
        
        if not project:
            return None
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{safe_filename(project.title)}_backup_{timestamp}.zip"
            backup_path = self.backups_dir / backup_filename
            
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 备份数据库记录
                project_data = {
                    "project": ProjectResponseSchema.from_orm(project).dict(),
                    "backup_created_at": datetime.utcnow().isoformat()
                }
                zipf.writestr("project_data.json", json.dumps(project_data, indent=2, ensure_ascii=False, default=str))
                
                # 备份项目文件
                if os.path.exists(project.project_path):
                    for root, dirs, files in os.walk(project.project_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arc_path = os.path.relpath(file_path, project.project_path)
                            zipf.write(file_path, f"files/{arc_path}")
            
            logger.info(f"Backed up project: {project.title} to {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            logger.error(f"Failed to backup project {project_id}: {str(e)}")
            return None
    
    def restore_project(self, backup_file: str, user_id: UUID) -> Optional[ProjectResponseSchema]:
        """从备份恢复项目"""
        try:
            backup_path = Path(backup_file)
            if not backup_path.exists():
                return None
            
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # 读取项目数据
                project_data_raw = zipf.read("project_data.json")
                backup_data = json.loads(project_data_raw)
                project_data = backup_data["project"]
                
                # 创建恢复的项目
                restored_project = Project(
                    title=f"{project_data['title']}_restored",
                    description=project_data.get('description'),
                    genre=ProjectGenre(project_data.get('genre', 'other')),
                    status=ProjectStatus(project_data.get('status', 'draft')),
                    tags=project_data.get('tags'),
                    target_words=project_data.get('target_words'),
                    target_chapters=project_data.get('target_chapters'),
                    settings=project_data.get('settings'),
                    created_by=user_id
                )
                
                # 创建项目目录
                project_dir = self.projects_dir / safe_filename(restored_project.title)
                project_dir = self._ensure_unique_path(project_dir)
                restored_project.project_path = str(project_dir)
                
                # 恢复文件
                for member in zipf.namelist():
                    if member.startswith("files/"):
                        zipf.extract(member, project_dir)
                        # 移动文件到正确位置
                        src = project_dir / member
                        dst = project_dir / member[6:]  # 去掉 "files/" 前缀
                        dst.parent.mkdir(parents=True, exist_ok=True)
                        src.rename(dst)
                
                # 清理临时目录
                files_dir = project_dir / "files"
                if files_dir.exists():
                    shutil.rmtree(files_dir)
                
                self.db.add(restored_project)
                self.db.commit()
                self.db.refresh(restored_project)
                
                logger.info(f"Restored project: {restored_project.title}")
                return ProjectResponseSchema.from_orm(restored_project)
                
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to restore project: {str(e)}")
            return None
    
    # 辅助方法
    
    def _ensure_unique_path(self, path: Path) -> Path:
        """确保路径唯一"""
        if not path.exists():
            return path
        
        counter = 1
        while True:
            new_path = path.parent / f"{path.name}_{counter}"
            if not new_path.exists():
                return new_path
            counter += 1
    
    def _create_project_structure(self, project: Project):
        """创建项目目录结构"""
        project_path = Path(project.project_path)
        
        # 创建基本目录结构
        directories = [
            "chapters",      # 章节文件
            "characters",    # 角色档案
            "scenes",        # 场景描述
            "outlines",      # 大纲文件
            "notes",         # 笔记
            "resources",     # 资源文件
            "exports",       # 导出文件
            "backups"        # 本地备份
        ]
        
        for directory in directories:
            ensure_directory(project_path / directory)
        
        # 创建项目配置文件
        config = {
            "project_id": str(project.id),
            "title": project.title,
            "created_at": project.created_at.isoformat() if project.created_at else None,
            "structure_version": "1.0"
        }
        
        config_file = project_path / "project.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

# 服务实例获取函数
def get_project_service(db: Session = None) -> ProjectService:
    """获取项目服务实例"""
    if db is None:
        db = next(get_db())
    return ProjectService(db)

# 导出的类和函数
__all__ = [
    "ProjectService",
    "get_project_service"
]