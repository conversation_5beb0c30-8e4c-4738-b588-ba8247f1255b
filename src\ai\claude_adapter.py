"""Claude适配器实现

提供Anthropic Claude API的适配器实现，支持Claude-3等模型。
实现长文本处理和对话生成功能，作为OpenAI的备选方案。
"""

import asyncio
import logging
from typing import Dict, List, Optional, AsyncGenerator, Any
from decimal import Decimal

try:
    import anthropic
    from anthropic import AsyncAnthropic
except ImportError:
    anthropic = None
    AsyncAnthropic = None

from .base_adapter import (
    BaseAIAdapter,
    AIModelType,
    AITaskType,
    AIMessage,
    AIRequest,
    AIResponse,
    AIResponseStatus,
    AIUsageStats,
    AIServiceError,
    AIRateLimitError,
    AIQuotaExceededError,
    AIModelError,
    AINetworkError
)

logger = logging.getLogger(__name__)


class ClaudeAdapter(BaseAIAdapter):
    """Anthropic Claude API适配器
    
    支持Claude-3-haiku、Claude-3-sonnet、Claude-3-opus等模型的API调用。
    """
    
    # Claude模型配置
    MODEL_CONFIGS = {
        AIModelType.CLAUDE_3_HAIKU: {
            "name": "claude-3-haiku-20240307",
            "max_tokens": 4096,
            "context_window": 200000,
            "cost_per_1k_input": Decimal("0.00025"),
            "cost_per_1k_output": Decimal("0.00125")
        },
        AIModelType.CLAUDE_3_SONNET: {
            "name": "claude-3-sonnet-20240229",
            "max_tokens": 4096,
            "context_window": 200000,
            "cost_per_1k_input": Decimal("0.003"),
            "cost_per_1k_output": Decimal("0.015")
        },
        AIModelType.CLAUDE_3_OPUS: {
            "name": "claude-3-opus-20240229",
            "max_tokens": 4096,
            "context_window": 200000,
            "cost_per_1k_input": Decimal("0.015"),
            "cost_per_1k_output": Decimal("0.075")
        },
        AIModelType.CLAUDE_3_5_SONNET: {
            "name": "claude-3-5-sonnet-20241022",
            "max_tokens": 8192,
            "context_window": 200000,
            "cost_per_1k_input": Decimal("0.003"),
            "cost_per_1k_output": Decimal("0.015")
        }
    }
    
    def __init__(self, api_key: str, base_url: Optional[str] = None, **kwargs):
        """初始化Claude适配器
        
        Args:
            api_key: Anthropic API密钥
            base_url: 自定义API基础URL
            **kwargs: 其他配置参数
        """
        if not anthropic or not AsyncAnthropic:
            raise ImportError("请安装anthropic库: pip install anthropic")
            
        super().__init__(**kwargs)
        
        self.api_key = api_key
        self.base_url = base_url
        
        # 初始化Anthropic客户端
        client_kwargs = {
            "api_key": api_key,
        }
        if base_url:
            client_kwargs["base_url"] = base_url
            
        self.client = AsyncAnthropic(**client_kwargs)
        
        logger.info(f"Claude适配器初始化完成，支持模型: {list(self.MODEL_CONFIGS.keys())}")
    
    @property
    def supported_models(self) -> List[AIModelType]:
        """获取支持的模型列表"""
        return list(self.MODEL_CONFIGS.keys())
    
    @property
    def supported_tasks(self) -> List[AITaskType]:
        """获取支持的任务类型"""
        return [
            AITaskType.TEXT_GENERATION,
            AITaskType.CREATIVE_WRITING,
            AITaskType.DIALOGUE_GENERATION,
            AITaskType.CONTENT_OPTIMIZATION,
            AITaskType.CONTENT_EXPANSION,
            AITaskType.CONTENT_SUMMARIZATION,
            AITaskType.STYLE_TRANSFER,
            AITaskType.GRAMMAR_CHECK,
            AITaskType.TRANSLATION,
            AITaskType.BRAINSTORMING,
            AITaskType.QUESTION_ANSWERING,
            AITaskType.CODE_GENERATION,
            AITaskType.ANALYSIS
        ]
    
    def _get_model_config(self, model_type: AIModelType) -> Dict[str, Any]:
        """获取模型配置"""
        if model_type not in self.MODEL_CONFIGS:
            raise AIModelError(f"不支持的模型类型: {model_type}")
        return self.MODEL_CONFIGS[model_type]
    
    def _convert_messages(self, messages: List[AIMessage]) -> tuple:
        """转换消息格式为Claude格式
        
        Claude API需要将system消息单独处理，其他消息转换为messages列表
        """
        system_message = None
        claude_messages = []
        
        for msg in messages:
            if msg.role == "system":
                system_message = msg.content
            else:
                role_mapping = {
                    "user": "user",
                    "assistant": "assistant"
                }
                
                claude_messages.append({
                    "role": role_mapping.get(msg.role, "user"),
                    "content": msg.content
                })
        
        return system_message, claude_messages
    
    def _handle_claude_error(self, error: Exception) -> AIServiceError:
        """处理Claude API错误"""
        error_message = str(error)
        
        if "rate_limit" in error_message.lower() or "429" in error_message:
            return AIRateLimitError(f"Claude API限流: {error_message}")
        elif "quota" in error_message.lower() or "billing" in error_message.lower():
            return AIQuotaExceededError(f"Claude配额不足: {error_message}")
        elif "model" in error_message.lower() or "invalid" in error_message.lower():
            return AIModelError(f"Claude模型错误: {error_message}")
        elif "network" in error_message.lower() or "connection" in error_message.lower():
            return AINetworkError(f"Claude网络错误: {error_message}")
        else:
            return AIServiceError(f"Claude服务错误: {error_message}")
    
    async def _make_request(self, request: AIRequest) -> AIResponse:
        """发送请求到Claude API"""
        try:
            model_config = self._get_model_config(request.model_type)
            system_message, claude_messages = self._convert_messages(request.messages)
            
            # 构建请求参数
            request_params = {
                "model": model_config["name"],
                "messages": claude_messages,
                "max_tokens": min(request.max_tokens or model_config["max_tokens"], 
                                model_config["max_tokens"]),
                "temperature": request.temperature,
                "top_p": request.top_p,
                "stream": request.stream
            }
            
            # 添加system消息
            if system_message:
                request_params["system"] = system_message
            
            # 添加其他参数
            if request.stop_sequences:
                request_params["stop_sequences"] = request.stop_sequences
            
            logger.debug(f"发送Claude请求: {request_params['model']}, tokens: {request_params['max_tokens']}")
            
            # 发送请求
            if request.stream:
                return await self._handle_stream_response(request_params, model_config)
            else:
                response = await self.client.messages.create(**request_params)
                return self._parse_response(response, model_config)
                
        except Exception as e:
            logger.error(f"Claude请求失败: {e}")
            raise self._handle_claude_error(e)
    
    async def _handle_stream_response(self, request_params: Dict[str, Any], 
                                    model_config: Dict[str, Any]) -> AIResponse:
        """处理流式响应"""
        try:
            stream = await self.client.messages.create(**request_params)
            
            content_parts = []
            usage_stats = AIUsageStats()
            
            async for chunk in stream:
                if chunk.type == "content_block_delta" and hasattr(chunk.delta, 'text'):
                    content_parts.append(chunk.delta.text)
                    yield chunk.delta.text
                elif chunk.type == "message_delta" and hasattr(chunk, 'usage'):
                    # 更新使用统计
                    if hasattr(chunk.usage, 'output_tokens'):
                        usage_stats.output_tokens = chunk.usage.output_tokens
            
            # 构建完整响应
            full_content = "".join(content_parts)
            
            # 估算输入tokens（流式响应可能不返回准确的输入token计数）
            if usage_stats.input_tokens == 0:
                estimated_input_tokens = sum(len(msg.get("content", "")) for msg in request_params["messages"]) // 4
                if "system" in request_params:
                    estimated_input_tokens += len(request_params["system"]) // 4
                usage_stats.input_tokens = estimated_input_tokens
            
            if usage_stats.output_tokens == 0:
                usage_stats.output_tokens = len(full_content) // 4
            
            usage_stats.total_tokens = usage_stats.input_tokens + usage_stats.output_tokens
            
            # 计算成本
            input_cost = (Decimal(usage_stats.input_tokens) / 1000) * model_config["cost_per_1k_input"]
            output_cost = (Decimal(usage_stats.output_tokens) / 1000) * model_config["cost_per_1k_output"]
            usage_stats.total_cost = input_cost + output_cost
            
            return AIResponse(
                content=full_content,
                status=AIResponseStatus.SUCCESS,
                model_type=request_params.get("model_type"),
                usage_stats=usage_stats,
                metadata={
                    "model": request_params["model"],
                    "stream": True,
                    "estimated_input_tokens": usage_stats.input_tokens == len(full_content) // 4
                }
            )
            
        except Exception as e:
            logger.error(f"Claude流式响应处理失败: {e}")
            raise self._handle_claude_error(e)
    
    def _parse_response(self, response, model_config: Dict[str, Any]) -> AIResponse:
        """解析Claude响应"""
        try:
            # Claude响应格式
            content = ""
            if response.content and len(response.content) > 0:
                content = response.content[0].text
            
            # 解析使用统计
            usage_stats = AIUsageStats()
            if hasattr(response, 'usage') and response.usage:
                usage_stats.input_tokens = response.usage.input_tokens
                usage_stats.output_tokens = response.usage.output_tokens
                usage_stats.total_tokens = usage_stats.input_tokens + usage_stats.output_tokens
                
                # 计算成本
                input_cost = (Decimal(usage_stats.input_tokens) / 1000) * model_config["cost_per_1k_input"]
                output_cost = (Decimal(usage_stats.output_tokens) / 1000) * model_config["cost_per_1k_output"]
                usage_stats.total_cost = input_cost + output_cost
            
            return AIResponse(
                content=content,
                status=AIResponseStatus.SUCCESS,
                model_type=response.model,
                usage_stats=usage_stats,
                metadata={
                    "model": response.model,
                    "stop_reason": response.stop_reason,
                    "response_id": response.id
                }
            )
            
        except Exception as e:
            logger.error(f"解析Claude响应失败: {e}")
            raise AIServiceError(f"响应解析错误: {e}")
    
    async def generate_stream(self, request: AIRequest) -> AsyncGenerator[str, None]:
        """生成流式文本"""
        try:
            model_config = self._get_model_config(request.model_type)
            system_message, claude_messages = self._convert_messages(request.messages)
            
            request_params = {
                "model": model_config["name"],
                "messages": claude_messages,
                "max_tokens": min(request.max_tokens or model_config["max_tokens"], 
                                model_config["max_tokens"]),
                "temperature": request.temperature,
                "top_p": request.top_p,
                "stream": True
            }
            
            if system_message:
                request_params["system"] = system_message
            
            if request.stop_sequences:
                request_params["stop_sequences"] = request.stop_sequences
            
            logger.debug(f"开始Claude流式生成: {request_params['model']}")
            
            stream = await self.client.messages.create(**request_params)
            
            async for chunk in stream:
                if chunk.type == "content_block_delta" and hasattr(chunk.delta, 'text'):
                    yield chunk.delta.text
                    
        except Exception as e:
            logger.error(f"Claude流式生成失败: {e}")
            raise self._handle_claude_error(e)
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            # 发送简单的测试请求
            test_request = AIRequest(
                messages=[AIMessage(role="user", content="Hello")],
                model_type=AIModelType.CLAUDE_3_HAIKU,
                max_tokens=5,
                temperature=0.1
            )
            
            response = await self.generate(test_request)
            return response.status == AIResponseStatus.SUCCESS
            
        except Exception as e:
            logger.warning(f"Claude健康检查失败: {e}")
            return False
    
    def get_model_info(self, model_type: AIModelType) -> Dict[str, Any]:
        """获取模型信息"""
        if model_type not in self.MODEL_CONFIGS:
            raise AIModelError(f"不支持的模型类型: {model_type}")
        
        config = self.MODEL_CONFIGS[model_type]
        return {
            "name": config["name"],
            "max_tokens": config["max_tokens"],
            "context_window": config["context_window"],
            "cost_per_1k_input": float(config["cost_per_1k_input"]),
            "cost_per_1k_output": float(config["cost_per_1k_output"]),
            "provider": "Anthropic",
            "type": "chat",
            "strengths": ["长文本处理", "推理能力", "安全性", "创意写作"]
        }


# 创建默认实例（需要在使用时提供API密钥）
def create_claude_adapter(api_key: str, **kwargs) -> ClaudeAdapter:
    """创建Claude适配器实例
    
    Args:
        api_key: Anthropic API密钥
        **kwargs: 其他配置参数
    
    Returns:
        Claude适配器实例
    """
    return ClaudeAdapter(api_key=api_key, **kwargs)