# 笔落App UI设计文档

## 一、项目概述

笔落App是一个用于作者和AI大模型合作进行编写小说的应用，旨在将作者的创意落地成小说成品，减少作者的写作复杂度。

## 二、界面需求分析

### 2.1 核心界面识别

根据需求文档分析，笔落App需要以下主要界面：

1. **主界面** - 应用启动页面
2. **项目新建界面** - 创建新项目的对话框
3. **创作界面** - 核心工作区域
4. **设置界面** - 应用配置页面

### 2.2 功能模块分析

**核心功能模块：**
- 小说大纲管理
- 小说章节管理  
- 小说角色管理
- 小说场景管理
- 小说事件管理
- 小说章节内容生成和修改
- 小说内容输出
- AI功能辅助

## 三、详细界面设计规范

### 3.1 主界面设计

#### 功能描述
- 应用的启动页面和项目管理中心
- 提供项目创建、打开和应用设置功能

#### 布局结构
```
┌─────────────────────────────────────────┐
│                 标题栏                    │
├─────────────────────────────────────────┤
│                                         │
│            意境背景图像区域               │
│                                         │
│    ┌─────────────┐  ┌─────────────┐    │
│    │  创建项目   │  │  打开项目   │    │
│    └─────────────┘  └─────────────┘    │
│                                         │
│              ┌─────────────┐            │
│              │    设置     │            │
│              └─────────────┘            │
│                                         │
└─────────────────────────────────────────┘
```

#### 控件清单
1. **背景图像** - 全屏意境背景，符合"笔落"寓意
2. **创建项目按钮** - 主要操作按钮，居中偏左
3. **打开项目按钮** - 主要操作按钮，居中偏右
4. **设置按钮** - 次要操作按钮，底部居中
5. **应用标题** - 顶部显示应用名称

#### 交互说明
- **创建项目按钮**：点击后弹出项目新建界面
- **打开项目按钮**：点击后打开文件选择对话框，选择项目文件夹
- **设置按钮**：点击后进入设置界面
- **按钮悬停效果**：鼠标悬停时按钮高亮显示

#### 视觉样式建议
- **背景**：深色调意境图像，营造文学创作氛围
- **按钮样式**：圆角矩形，渐变色彩，阴影效果
- **字体**：优雅的中文字体，如思源黑体
- **颜色方案**：
  - 主色调：深蓝色 (#2C3E50)
  - 辅助色：金色 (#F39C12)
  - 文字色：白色 (#FFFFFF)

### 3.2 项目新建界面设计

#### 功能描述
- 创建新的小说项目
- 设置项目基本信息

#### 布局结构
```
┌─────────────────────────────────────────┐
│              新建项目                    │
├─────────────────────────────────────────┤
│  项目名称: [________________]           │
│                                         │
│  项目描述: [________________]           │
│           [________________]           │
│           [________________]           │
│                                         │
│  保存位置: [________________] [浏览]    │
│                                         │
│  项目类型: ○ 长篇小说  ○ 短篇小说      │
│           ○ 散文集    ○ 其他          │
│                                         │
│        [取消]           [创建]          │
└─────────────────────────────────────────┘
```

#### 控件清单
1. **项目名称输入框** - 单行文本输入
2. **项目描述输入框** - 多行文本输入
3. **保存位置输入框** - 路径显示和选择
4. **浏览按钮** - 文件夹选择
5. **项目类型单选按钮组** - 四个选项
6. **取消按钮** - 次要操作
7. **创建按钮** - 主要操作

#### 交互说明
- **输入验证**：项目名称不能为空，路径必须有效
- **浏览按钮**：打开文件夹选择对话框
- **创建按钮**：验证输入后创建项目文件夹和配置文件
- **取消按钮**：关闭对话框返回主界面

### 3.3 创作界面设计

#### 功能描述
- 应用的核心工作区域
- 集成所有创作相关功能模块

#### 布局结构
```
┌─────────────────────────────────────────────────────────────────┐
│  文件  编辑  视图  工具  帮助                    项目: [项目名]  │
├─────────────────────────────────────────────────────────────────┤
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│ │  大纲   │ │  章节   │ │  角色   │ │  场景   │ │  事件   │   │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────┘   │
├─────────────────────────────────────────────────────────────────┤
│ ┌───────────┐                                   ┌───────────┐ │
│ │           │                                   │           │ │
│ │  管理面板  │            内容编辑区域            │  AI助手   │ │
│ │           │                                   │           │ │
│ │  - 列表   │  ┌─────────────────────────────┐  │  - 生成   │ │
│ │  - 添加   │  │                             │  │  - 优化   │ │
│ │  - 编辑   │  │        章节内容编辑器        │  │  - 建议   │ │
│ │  - 删除   │  │                             │  │           │ │
│ │           │  │                             │  │  [生成]   │ │
│ │           │  │                             │  │  [优化]   │ │
│ │           │  └─────────────────────────────┘  │  [导出]   │ │
│ └───────────┘                                   └───────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  状态栏: 字数统计 | AI模型状态 | 保存状态                        │
└─────────────────────────────────────────────────────────────────┘
```

#### 控件清单
1. **菜单栏** - 文件、编辑、视图、工具、帮助
2. **功能标签页** - 大纲、章节、角色、场景、事件
3. **左侧管理面板** - 当前选中模块的管理界面
4. **中央内容编辑区** - 富文本编辑器
5. **右侧AI助手面板** - AI功能操作区
6. **状态栏** - 显示各种状态信息

#### 交互说明
- **标签页切换**：点击不同标签切换管理模块
- **管理面板**：显示当前模块的列表和操作按钮
- **内容编辑**：支持富文本编辑，实时保存
- **AI助手**：提供内容生成、优化等AI功能
- **拖拽支持**：支持章节、角色等元素的拖拽排序

### 3.4 设置界面设计

#### 功能描述
- 应用配置和个性化设置
- AI模型配置和优化选项

#### 布局结构
```
┌─────────────────────────────────────────────────────────────────┐
│                           设置                                   │
├─────────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────────────────────────────────────┐ │
│ │             │ │                                             │ │
│ │  通用设置   │ │  主题设置                                   │ │
│ │             │ │  ○ 浅色主题  ○ 深色主题  ○ 自动            │ │
│ │  AI模型设置 │ │                                             │ │
│ │             │ │  字体设置                                   │ │
│ │  内容优化   │ │  字体: [下拉选择] 大小: [滑块] 14px        │ │
│ │             │ │                                             │ │
│ │  导入导出   │ │  背景颜色                                   │ │
│ │             │ │  [颜色选择器]                               │ │
│ │  关于       │ │                                             │ │
│ │             │ │  ──────────────────────────────────────── │ │
│ │             │ │                                             │ │
│ │             │ │  AI厂商: [下拉选择]                        │ │
│ │             │ │  API密钥: [________________]               │ │
│ │             │ │  模型选择: [下拉选择]                       │ │
│ │             │ │                                             │ │
│ └─────────────┘ └─────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                    [恢复默认]  [取消]  [保存]                   │
└─────────────────────────────────────────────────────────────────┘
```

#### 控件清单
1. **左侧导航** - 设置分类列表
2. **主题设置** - 单选按钮组
3. **字体设置** - 下拉框和滑块
4. **颜色选择器** - 背景颜色设置
5. **AI配置** - 厂商选择、API输入、模型选择
6. **操作按钮** - 恢复默认、取消、保存

## 四、界面导航流程

### 4.1 用户流程图

```
主界面
├── 创建项目 → 项目新建界面 → 创作界面
├── 打开项目 → 文件选择 → 创作界面
└── 设置 → 设置界面

创作界面
├── 大纲管理 ↔ 章节管理 ↔ 角色管理 ↔ 场景管理 ↔ 事件管理
├── 内容编辑 ↔ AI助手
└── 文件菜单 → 导出/保存
```

### 4.2 导航关系说明

1. **主界面** 是应用的入口点
2. **创作界面** 是核心工作区，各模块间可自由切换
3. **设置界面** 可从主界面或创作界面访问
4. **项目新建界面** 是模态对话框，完成后进入创作界面

## 五、响应式设计考虑

### 5.1 窗口尺寸适配

- **最小窗口尺寸**：1024x768
- **推荐窗口尺寸**：1440x900
- **布局自适应**：支持窗口缩放，面板可折叠

### 5.2 组件自适应

- **侧边栏**：可折叠隐藏，节省空间
- **编辑区**：自动调整宽度，保持可读性
- **按钮和控件**：保持合适的点击区域

## 六、可访问性设计

### 6.1 键盘导航

- 支持Tab键在控件间切换
- 支持快捷键操作主要功能
- 支持方向键在列表中导航

### 6.2 视觉辅助

- 高对比度模式支持
- 字体大小可调节
- 色盲友好的颜色方案

## 七、性能优化建议

### 7.1 界面渲染

- 使用虚拟滚动处理大量数据
- 延迟加载非关键内容
- 优化重绘和回流

### 7.2 用户体验

- 添加加载状态指示
- 实现自动保存功能
- 提供操作撤销/重做

## 八、技术实现建议

### 8.1 前端框架

- 推荐使用 Electron + React/Vue
- 或者 Tauri + React/Vue
- 支持跨平台部署

### 8.2 UI组件库

- Ant Design 或 Element Plus
- 自定义主题配色
- 响应式栅格系统

### 8.3 状态管理

- Redux/Vuex 管理应用状态
- 本地存储项目数据
- 实时同步编辑状态

---

**文档版本**: 1.0  
**创建日期**: 2024年1月  
**最后更新**: 2024年1月