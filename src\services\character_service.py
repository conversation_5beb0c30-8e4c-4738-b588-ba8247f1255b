"""角色管理服务

提供完整的角色管理功能，包括：
- 角色信息的CRUD操作
- 角色关系图谱管理
- 角色一致性检查
- AI辅助角色生成和分析
- 角色对话风格分析
- 角色发展轨迹跟踪
"""

import json
from datetime import datetime
from typing import List, Optional, Dict, Any, Union, Tuple
from uuid import UUID
from collections import defaultdict

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from fastapi import HTTPException, status
from pydantic import BaseModel, Field

from ..models.story_elements import (
    Character, CharacterType, CharacterGender,
    CharacterCreateSchema, CharacterUpdateSchema, CharacterResponseSchema
)
from ..models.project import Project
from ..services.content_service import ContentService, ContentType, GenerationRequest
from ..core.database import get_db
from ..utils.logger import get_logger

logger = get_logger(__name__)

class CharacterRelationship(BaseModel):
    """角色关系模型"""
    character_id: UUID = Field(..., description="角色ID")
    related_character_id: UUID = Field(..., description="关联角色ID")
    relationship_type: str = Field(..., description="关系类型")
    relationship_description: str = Field(..., description="关系描述")
    strength: int = Field(5, ge=1, le=10, description="关系强度（1-10）")
    is_mutual: bool = Field(True, description="是否互相关系")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

class CharacterArc(BaseModel):
    """角色发展轨迹模型"""
    character_id: UUID = Field(..., description="角色ID")
    arc_name: str = Field(..., description="发展轨迹名称")
    description: str = Field(..., description="轨迹描述")
    start_state: str = Field(..., description="初始状态")
    end_state: str = Field(..., description="结束状态")
    key_events: List[str] = Field(default_factory=list, description="关键事件")
    milestones: List[Dict[str, Any]] = Field(default_factory=list, description="里程碑")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

class DialogueStyle(BaseModel):
    """对话风格模型"""
    character_id: UUID = Field(..., description="角色ID")
    vocabulary_level: str = Field("medium", description="词汇水平：simple, medium, complex")
    sentence_structure: str = Field("medium", description="句式结构：simple, medium, complex")
    tone: str = Field("neutral", description="语调：formal, casual, neutral, etc.")
    speech_patterns: List[str] = Field(default_factory=list, description="说话模式")
    catchphrases: List[str] = Field(default_factory=list, description="口头禅")
    emotional_expressions: Dict[str, str] = Field(default_factory=dict, description="情感表达方式")
    cultural_background: Optional[str] = Field(None, description="文化背景影响")
    education_level: Optional[str] = Field(None, description="教育水平")
    regional_accent: Optional[str] = Field(None, description="地方口音")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

class CharacterGenerationRequest(BaseModel):
    """角色生成请求"""
    project_id: UUID = Field(..., description="项目ID")
    character_type: CharacterType = Field(..., description="角色类型")
    name: Optional[str] = Field(None, description="指定姓名")
    age_range: Optional[Tuple[int, int]] = Field(None, description="年龄范围")
    gender: Optional[CharacterGender] = Field(None, description="性别")
    occupation: Optional[str] = Field(None, description="职业")
    personality_traits: List[str] = Field(default_factory=list, description="性格特征")
    background_elements: List[str] = Field(default_factory=list, description="背景要素")
    story_role: str = Field(..., description="在故事中的作用")
    conflict_source: Optional[str] = Field(None, description="冲突来源")
    special_requirements: Optional[str] = Field(None, description="特殊要求")

class CharacterConsistencyCheck(BaseModel):
    """角色一致性检查结果"""
    character_id: UUID = Field(..., description="角色ID")
    is_consistent: bool = Field(..., description="是否一致")
    issues: List[Dict[str, Any]] = Field(default_factory=list, description="一致性问题")
    suggestions: List[str] = Field(default_factory=list, description="改进建议")
    score: float = Field(..., ge=0, le=100, description="一致性评分")
    checked_at: datetime = Field(default_factory=datetime.utcnow, description="检查时间")

class CharacterService:
    """角色管理服务类"""
    
    def __init__(self, db: Session, content_service: ContentService = None):
        self.db = db
        self.content_service = content_service
    
    # 角色CRUD操作
    
    def create_character(self, character_data: CharacterCreateSchema) -> CharacterResponseSchema:
        """创建新角色"""
        try:
            # 验证项目存在
            project = self.db.query(Project).filter(
                and_(
                    Project.id == character_data.project_id,
                    Project.deleted_at.is_(None)
                )
            ).first()
            
            if not project:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="项目不存在"
                )
            
            # 检查角色名称是否重复
            existing_character = self.db.query(Character).filter(
                and_(
                    Character.project_id == character_data.project_id,
                    Character.name == character_data.name,
                    Character.deleted_at.is_(None)
                )
            ).first()
            
            if existing_character:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="角色名称已存在"
                )
            
            # 创建角色
            character = Character(**character_data.dict())
            self.db.add(character)
            self.db.commit()
            self.db.refresh(character)
            
            logger.info(f"Created character: {character.name} for project {character_data.project_id}")
            return CharacterResponseSchema.from_orm(character)
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create character: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建角色失败: {str(e)}"
            )
    
    def get_character(self, character_id: UUID) -> Optional[CharacterResponseSchema]:
        """获取角色信息"""
        character = self.db.query(Character).filter(
            and_(
                Character.id == character_id,
                Character.deleted_at.is_(None)
            )
        ).first()
        
        if character:
            return CharacterResponseSchema.from_orm(character)
        return None
    
    def get_characters_by_project(
        self,
        project_id: UUID,
        character_type: Optional[CharacterType] = None,
        importance_level: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[CharacterResponseSchema]:
        """获取项目的角色列表"""
        query = self.db.query(Character).filter(
            and_(
                Character.project_id == project_id,
                Character.deleted_at.is_(None)
            )
        )
        
        if character_type:
            query = query.filter(Character.character_type == character_type.value)
        
        if importance_level:
            query = query.filter(Character.importance_level >= importance_level)
        
        characters = query.order_by(
            desc(Character.importance_level),
            asc(Character.name)
        ).offset(skip).limit(limit).all()
        
        return [CharacterResponseSchema.from_orm(char) for char in characters]
    
    def update_character(
        self,
        character_id: UUID,
        character_data: CharacterUpdateSchema
    ) -> Optional[CharacterResponseSchema]:
        """更新角色信息"""
        try:
            character = self.db.query(Character).filter(
                and_(
                    Character.id == character_id,
                    Character.deleted_at.is_(None)
                )
            ).first()
            
            if not character:
                return None
            
            # 检查名称重复（如果更新了名称）
            if character_data.name and character_data.name != character.name:
                existing_character = self.db.query(Character).filter(
                    and_(
                        Character.project_id == character.project_id,
                        Character.name == character_data.name,
                        Character.id != character_id,
                        Character.deleted_at.is_(None)
                    )
                ).first()
                
                if existing_character:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="角色名称已存在"
                    )
            
            # 更新角色数据
            update_data = character_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(character, field, value)
            
            character.updated_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(character)
            
            logger.info(f"Updated character: {character.name}")
            return CharacterResponseSchema.from_orm(character)
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to update character: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"更新角色失败: {str(e)}"
            )
    
    def delete_character(self, character_id: UUID) -> bool:
        """删除角色（软删除）"""
        try:
            character = self.db.query(Character).filter(
                and_(
                    Character.id == character_id,
                    Character.deleted_at.is_(None)
                )
            ).first()
            
            if not character:
                return False
            
            character.deleted_at = datetime.utcnow()
            self.db.commit()
            
            logger.info(f"Deleted character: {character.name}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to delete character: {str(e)}")
            return False
    
    # 角色关系管理
    
    def add_character_relationship(
        self,
        character_id: UUID,
        related_character_id: UUID,
        relationship_type: str,
        relationship_description: str,
        strength: int = 5,
        is_mutual: bool = True
    ) -> CharacterRelationship:
        """添加角色关系"""
        try:
            # 验证角色存在
            character = self.get_character(character_id)
            related_character = self.get_character(related_character_id)
            
            if not character or not related_character:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="角色不存在"
                )
            
            # 创建关系
            relationship = CharacterRelationship(
                character_id=character_id,
                related_character_id=related_character_id,
                relationship_type=relationship_type,
                relationship_description=relationship_description,
                strength=strength,
                is_mutual=is_mutual
            )
            
            # 保存到角色的关系数据中
            self._save_character_relationship(relationship)
            
            # 如果是互相关系，也为对方添加关系
            if is_mutual:
                reverse_relationship = CharacterRelationship(
                    character_id=related_character_id,
                    related_character_id=character_id,
                    relationship_type=self._get_reverse_relationship_type(relationship_type),
                    relationship_description=relationship_description,
                    strength=strength,
                    is_mutual=True
                )
                self._save_character_relationship(reverse_relationship)
            
            logger.info(f"Added relationship between {character_id} and {related_character_id}")
            return relationship
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to add character relationship: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"添加角色关系失败: {str(e)}"
            )
    
    def get_character_relationships(self, character_id: UUID) -> List[CharacterRelationship]:
        """获取角色的所有关系"""
        character = self.get_character(character_id)
        if not character:
            return []
        
        return self._load_character_relationships(character_id)
    
    def update_character_relationship(
        self,
        character_id: UUID,
        related_character_id: UUID,
        relationship_data: Dict[str, Any]
    ) -> Optional[CharacterRelationship]:
        """更新角色关系"""
        relationships = self.get_character_relationships(character_id)
        
        for relationship in relationships:
            if relationship.related_character_id == related_character_id:
                # 更新关系数据
                for key, value in relationship_data.items():
                    if hasattr(relationship, key):
                        setattr(relationship, key, value)
                
                relationship.updated_at = datetime.utcnow()
                
                # 保存更新
                self._save_character_relationship(relationship)
                
                logger.info(f"Updated relationship between {character_id} and {related_character_id}")
                return relationship
        
        return None
    
    def remove_character_relationship(
        self,
        character_id: UUID,
        related_character_id: UUID
    ) -> bool:
        """移除角色关系"""
        try:
            relationships = self.get_character_relationships(character_id)
            updated_relationships = [
                rel for rel in relationships 
                if rel.related_character_id != related_character_id
            ]
            
            if len(updated_relationships) != len(relationships):
                self._save_all_character_relationships(character_id, updated_relationships)
                
                # 如果是互相关系，也移除对方的关系
                reverse_relationships = self.get_character_relationships(related_character_id)
                updated_reverse = [
                    rel for rel in reverse_relationships 
                    if rel.related_character_id != character_id
                ]
                
                if len(updated_reverse) != len(reverse_relationships):
                    self._save_all_character_relationships(related_character_id, updated_reverse)
                
                logger.info(f"Removed relationship between {character_id} and {related_character_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to remove character relationship: {str(e)}")
            return False
    
    def get_relationship_graph(self, project_id: UUID) -> Dict[str, Any]:
        """获取项目的角色关系图谱"""
        characters = self.get_characters_by_project(project_id)
        
        nodes = []
        edges = []
        
        # 构建节点
        for character in characters:
            nodes.append({
                "id": str(character.id),
                "name": character.name,
                "type": character.character_type.value,
                "importance": character.importance_level,
                "avatar": character.avatar
            })
        
        # 构建边（关系）
        processed_pairs = set()
        
        for character in characters:
            relationships = self.get_character_relationships(character.id)
            
            for relationship in relationships:
                # 避免重复边
                pair = tuple(sorted([str(character.id), str(relationship.related_character_id)]))
                if pair not in processed_pairs:
                    edges.append({
                        "source": str(character.id),
                        "target": str(relationship.related_character_id),
                        "type": relationship.relationship_type,
                        "description": relationship.relationship_description,
                        "strength": relationship.strength
                    })
                    processed_pairs.add(pair)
        
        return {
            "nodes": nodes,
            "edges": edges,
            "statistics": {
                "total_characters": len(nodes),
                "total_relationships": len(edges),
                "character_types": self._count_character_types(characters),
                "relationship_types": self._count_relationship_types(edges)
            }
        }
    
    # AI辅助功能
    
    def generate_character_with_ai(
        self,
        request: CharacterGenerationRequest
    ) -> CharacterResponseSchema:
        """使用AI生成角色"""
        if not self.content_service:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="AI服务不可用"
            )
        
        try:
            # 构建生成提示
            prompt = self._build_character_generation_prompt(request)
            
            # 调用AI生成内容
            generation_request = GenerationRequest(
                content_type=ContentType.CHARACTER_PROFILE,
                prompt=prompt,
                context={"request": request.dict()}
            )
            
            ai_response = self.content_service.generate_content(generation_request)
            
            # 解析AI响应为角色数据
            character_data = self._parse_ai_character_response(ai_response, request)
            
            # 创建角色
            character = self.create_character(character_data)
            
            logger.info(f"Generated character with AI: {character.name}")
            return character
            
        except Exception as e:
            logger.error(f"Failed to generate character with AI: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"AI生成角色失败: {str(e)}"
            )
    
    def enhance_character_with_ai(
        self,
        character_id: UUID,
        enhancement_type: str = "personality"
    ) -> Optional[CharacterResponseSchema]:
        """使用AI增强角色信息"""
        if not self.content_service:
            return None
        
        character = self.get_character(character_id)
        if not character:
            return None
        
        try:
            # 构建增强提示
            prompt = self._build_character_enhancement_prompt(character, enhancement_type)
            
            # 调用AI生成内容
            generation_request = GenerationRequest(
                content_type=ContentType.CHARACTER_PROFILE,
                prompt=prompt,
                context={"character": character.dict()}
            )
            
            ai_response = self.content_service.generate_content(generation_request)
            
            # 更新角色信息
            update_data = self._parse_ai_enhancement_response(ai_response, enhancement_type)
            
            if update_data:
                updated_character = self.update_character(
                    character_id,
                    CharacterUpdateSchema(**update_data)
                )
                
                logger.info(f"Enhanced character with AI: {character.name}")
                return updated_character
            
            return character
            
        except Exception as e:
            logger.error(f"Failed to enhance character with AI: {str(e)}")
            return None
    
    def analyze_dialogue_style(
        self,
        character_id: UUID,
        dialogue_samples: List[str]
    ) -> DialogueStyle:
        """分析角色对话风格"""
        character = self.get_character(character_id)
        if not character:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="角色不存在"
            )
        
        try:
            # 基础分析
            style = DialogueStyle(character_id=character_id)
            
            if dialogue_samples:
                # 分析词汇水平
                style.vocabulary_level = self._analyze_vocabulary_level(dialogue_samples)
                
                # 分析句式结构
                style.sentence_structure = self._analyze_sentence_structure(dialogue_samples)
                
                # 分析语调
                style.tone = self._analyze_tone(dialogue_samples)
                
                # 提取说话模式
                style.speech_patterns = self._extract_speech_patterns(dialogue_samples)
                
                # 提取口头禅
                style.catchphrases = self._extract_catchphrases(dialogue_samples)
            
            # 如果有AI服务，进行更深入的分析
            if self.content_service:
                ai_analysis = self._ai_analyze_dialogue_style(character, dialogue_samples)
                if ai_analysis:
                    # 合并AI分析结果
                    for key, value in ai_analysis.items():
                        if hasattr(style, key) and value:
                            setattr(style, key, value)
            
            # 保存对话风格
            self._save_dialogue_style(style)
            
            logger.info(f"Analyzed dialogue style for character: {character.name}")
            return style
            
        except Exception as e:
            logger.error(f"Failed to analyze dialogue style: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"分析对话风格失败: {str(e)}"
            )
    
    def get_dialogue_style(self, character_id: UUID) -> Optional[DialogueStyle]:
        """获取角色对话风格"""
        return self._load_dialogue_style(character_id)
    
    # 角色一致性检查
    
    def check_character_consistency(
        self,
        character_id: UUID,
        content_samples: List[str] = None
    ) -> CharacterConsistencyCheck:
        """检查角色一致性"""
        character = self.get_character(character_id)
        if not character:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="角色不存在"
            )
        
        try:
            issues = []
            suggestions = []
            score = 100.0
            
            # 基础信息完整性检查
            if not character.personality:
                issues.append({
                    "type": "missing_info",
                    "field": "personality",
                    "message": "缺少性格描述",
                    "severity": "medium"
                })
                score -= 10
            
            if not character.background:
                issues.append({
                    "type": "missing_info",
                    "field": "background",
                    "message": "缺少背景故事",
                    "severity": "medium"
                })
                score -= 10
            
            if not character.goals:
                issues.append({
                    "type": "missing_info",
                    "field": "goals",
                    "message": "缺少角色目标",
                    "severity": "low"
                })
                score -= 5
            
            # 逻辑一致性检查
            if character.age and character.age < 0:
                issues.append({
                    "type": "logic_error",
                    "field": "age",
                    "message": "年龄不能为负数",
                    "severity": "high"
                })
                score -= 20
            
            # 角色类型与重要程度匹配检查
            if character.character_type == CharacterType.PROTAGONIST and character.importance_level < 8:
                issues.append({
                    "type": "inconsistency",
                    "field": "importance_level",
                    "message": "主角的重要程度应该较高",
                    "severity": "medium"
                })
                score -= 10
            
            # 内容样本一致性检查（如果提供）
            if content_samples and self.content_service:
                content_issues = self._check_content_consistency(character, content_samples)
                issues.extend(content_issues)
                score -= len(content_issues) * 5
            
            # 生成改进建议
            if not character.personality:
                suggestions.append("添加详细的性格描述，包括主要性格特征和行为模式")
            
            if not character.background:
                suggestions.append("完善角色背景故事，包括成长经历和重要事件")
            
            if character.character_type == CharacterType.PROTAGONIST:
                suggestions.append("确保主角有明确的目标、动机和成长轨迹")
            
            # 确保评分不低于0
            score = max(0, score)
            
            result = CharacterConsistencyCheck(
                character_id=character_id,
                is_consistent=len([i for i in issues if i["severity"] == "high"]) == 0,
                issues=issues,
                suggestions=suggestions,
                score=score
            )
            
            logger.info(f"Checked consistency for character: {character.name}, score: {score}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to check character consistency: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"检查角色一致性失败: {str(e)}"
            )
    
    # 角色发展轨迹
    
    def create_character_arc(
        self,
        character_id: UUID,
        arc_data: Dict[str, Any]
    ) -> CharacterArc:
        """创建角色发展轨迹"""
        character = self.get_character(character_id)
        if not character:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="角色不存在"
            )
        
        try:
            arc = CharacterArc(
                character_id=character_id,
                **arc_data
            )
            
            # 保存角色轨迹
            self._save_character_arc(arc)
            
            logger.info(f"Created character arc: {arc.arc_name} for {character.name}")
            return arc
            
        except Exception as e:
            logger.error(f"Failed to create character arc: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建角色轨迹失败: {str(e)}"
            )
    
    def get_character_arcs(self, character_id: UUID) -> List[CharacterArc]:
        """获取角色的所有发展轨迹"""
        return self._load_character_arcs(character_id)
    
    # 统计和分析
    
    def get_character_statistics(self, project_id: UUID) -> Dict[str, Any]:
        """获取角色统计信息"""
        characters = self.get_characters_by_project(project_id)
        
        if not characters:
            return {
                "total_characters": 0,
                "by_type": {},
                "by_gender": {},
                "by_importance": {},
                "average_importance": 0,
                "completion_rate": 0
            }
        
        # 按类型统计
        by_type = defaultdict(int)
        for char in characters:
            by_type[char.character_type.value] += 1
        
        # 按性别统计
        by_gender = defaultdict(int)
        for char in characters:
            by_gender[char.gender.value] += 1
        
        # 按重要程度统计
        by_importance = defaultdict(int)
        for char in characters:
            by_importance[char.importance_level] += 1
        
        # 计算完整度
        complete_characters = sum(
            1 for char in characters
            if char.personality and char.background and char.goals
        )
        
        return {
            "total_characters": len(characters),
            "by_type": dict(by_type),
            "by_gender": dict(by_gender),
            "by_importance": dict(by_importance),
            "average_importance": sum(char.importance_level for char in characters) / len(characters),
            "completion_rate": (complete_characters / len(characters)) * 100 if characters else 0
        }
    
    # 私有辅助方法
    
    def _save_character_relationship(self, relationship: CharacterRelationship):
        """保存角色关系到数据库"""
        # 获取角色的当前关系数据
        character = self.db.query(Character).filter(Character.id == relationship.character_id).first()
        if not character:
            return
        
        relationships_data = character.relationships or {}
        relationships_list = relationships_data.get("relationships", [])
        
        # 更新或添加关系
        found = False
        for i, rel_data in enumerate(relationships_list):
            if rel_data.get("related_character_id") == str(relationship.related_character_id):
                relationships_list[i] = relationship.dict()
                found = True
                break
        
        if not found:
            relationships_list.append(relationship.dict())
        
        relationships_data["relationships"] = relationships_list
        character.relationships = relationships_data
        character.updated_at = datetime.utcnow()
        
        self.db.commit()
    
    def _load_character_relationships(self, character_id: UUID) -> List[CharacterRelationship]:
        """从数据库加载角色关系"""
        character = self.db.query(Character).filter(Character.id == character_id).first()
        if not character or not character.relationships:
            return []
        
        relationships_data = character.relationships.get("relationships", [])
        relationships = []
        
        for rel_data in relationships_data:
            try:
                # 转换字符串ID为UUID
                rel_data["character_id"] = UUID(rel_data["character_id"])
                rel_data["related_character_id"] = UUID(rel_data["related_character_id"])
                
                # 转换时间字符串为datetime对象
                if isinstance(rel_data.get("created_at"), str):
                    rel_data["created_at"] = datetime.fromisoformat(rel_data["created_at"])
                if isinstance(rel_data.get("updated_at"), str):
                    rel_data["updated_at"] = datetime.fromisoformat(rel_data["updated_at"])
                
                relationships.append(CharacterRelationship(**rel_data))
            except Exception as e:
                logger.warning(f"Failed to parse relationship data: {str(e)}")
                continue
        
        return relationships
    
    def _save_all_character_relationships(self, character_id: UUID, relationships: List[CharacterRelationship]):
        """保存角色的所有关系"""
        character = self.db.query(Character).filter(Character.id == character_id).first()
        if not character:
            return
        
        relationships_data = {
            "relationships": [rel.dict() for rel in relationships]
        }
        
        character.relationships = relationships_data
        character.updated_at = datetime.utcnow()
        
        self.db.commit()
    
    def _get_reverse_relationship_type(self, relationship_type: str) -> str:
        """获取反向关系类型"""
        reverse_map = {
            "父亲": "儿子/女儿",
            "母亲": "儿子/女儿",
            "儿子": "父亲/母亲",
            "女儿": "父亲/母亲",
            "丈夫": "妻子",
            "妻子": "丈夫",
            "老板": "员工",
            "员工": "老板",
            "老师": "学生",
            "学生": "老师",
            "朋友": "朋友",
            "敌人": "敌人",
            "恋人": "恋人"
        }
        
        return reverse_map.get(relationship_type, relationship_type)
    
    def _count_character_types(self, characters: List[CharacterResponseSchema]) -> Dict[str, int]:
        """统计角色类型"""
        counts = defaultdict(int)
        for char in characters:
            counts[char.character_type.value] += 1
        return dict(counts)
    
    def _count_relationship_types(self, edges: List[Dict[str, Any]]) -> Dict[str, int]:
        """统计关系类型"""
        counts = defaultdict(int)
        for edge in edges:
            counts[edge["type"]] += 1
        return dict(counts)
    
    def _build_character_generation_prompt(self, request: CharacterGenerationRequest) -> str:
        """构建角色生成提示"""
        prompt = f"""请为以下小说创建一个{request.character_type.value}角色：

角色类型：{request.character_type.value}
故事作用：{request.story_role}
"""
        
        if request.name:
            prompt += f"指定姓名：{request.name}\n"
        
        if request.age_range:
            prompt += f"年龄范围：{request.age_range[0]}-{request.age_range[1]}岁\n"
        
        if request.gender:
            prompt += f"性别：{request.gender.value}\n"
        
        if request.occupation:
            prompt += f"职业：{request.occupation}\n"
        
        if request.personality_traits:
            prompt += f"性格特征：{', '.join(request.personality_traits)}\n"
        
        if request.background_elements:
            prompt += f"背景要素：{', '.join(request.background_elements)}\n"
        
        if request.conflict_source:
            prompt += f"冲突来源：{request.conflict_source}\n"
        
        if request.special_requirements:
            prompt += f"特殊要求：{request.special_requirements}\n"
        
        prompt += "\n请提供详细的角色信息，包括姓名、外貌、性格、背景、目标、动机和弱点。"
        
        return prompt
    
    def _parse_ai_character_response(self, ai_response: str, request: CharacterGenerationRequest) -> CharacterCreateSchema:
        """解析AI角色生成响应"""
        # 这里需要实现AI响应的解析逻辑
        # 暂时返回一个基本的角色数据
        return CharacterCreateSchema(
            project_id=request.project_id,
            name=request.name or "AI生成角色",
            character_type=request.character_type,
            gender=request.gender or CharacterGender.UNKNOWN,
            occupation=request.occupation,
            personality="AI生成的性格描述",
            background="AI生成的背景故事",
            goals="AI生成的角色目标",
            motivations="AI生成的角色动机"
        )
    
    def _build_character_enhancement_prompt(self, character: CharacterResponseSchema, enhancement_type: str) -> str:
        """构建角色增强提示"""
        if enhancement_type == "personality":
            return f"""请为以下角色扩展性格描述：
角色名称：{character.name}
当前性格：{character.personality or '无'}
角色类型：{character.character_type.value}
请提供更详细和丰富的性格描述。"""
        elif enhancement_type == "background":
            return f"""请为以下角色扩展背景故事：
角色名称：{character.name}
当前背景：{character.background or '无'}
职业：{character.occupation or '无'}
请提供更详细的背景故事。"""
        else:
            return f"""请优化以下角色信息：
角色名称：{character.name}
性格：{character.personality or '无'}
背景：{character.background or '无'}
请提供改进建议。"""
    
    def _parse_ai_enhancement_response(self, ai_response: str, enhancement_type: str) -> Dict[str, Any]:
        """解析AI增强响应"""
        # 这里需要实现AI响应的解析逻辑
        if enhancement_type == "personality":
            return {"personality": ai_response}
        elif enhancement_type == "background":
            return {"background": ai_response}
        else:
            return {}
    
    def _analyze_vocabulary_level(self, dialogue_samples: List[str]) -> str:
        """分析词汇水平"""
        # 简单的词汇复杂度分析
        total_words = 0
        complex_words = 0
        
        for dialogue in dialogue_samples:
            words = dialogue.split()
            total_words += len(words)
            # 简单判断：长度超过6个字符的词认为是复杂词汇
            complex_words += sum(1 for word in words if len(word) > 6)
        
        if total_words == 0:
            return "medium"
        
        complexity_ratio = complex_words / total_words
        
        if complexity_ratio > 0.3:
            return "complex"
        elif complexity_ratio > 0.1:
            return "medium"
        else:
            return "simple"
    
    def _analyze_sentence_structure(self, dialogue_samples: List[str]) -> str:
        """分析句式结构"""
        # 简单的句式复杂度分析
        total_sentences = len(dialogue_samples)
        complex_sentences = 0
        
        for dialogue in dialogue_samples:
            # 简单判断：包含多个标点符号的句子认为是复杂句式
            punctuation_count = sum(1 for char in dialogue if char in '，。！？；：')
            if punctuation_count > 2:
                complex_sentences += 1
        
        if total_sentences == 0:
            return "medium"
        
        complexity_ratio = complex_sentences / total_sentences
        
        if complexity_ratio > 0.5:
            return "complex"
        elif complexity_ratio > 0.2:
            return "medium"
        else:
            return "simple"
    
    def _analyze_tone(self, dialogue_samples: List[str]) -> str:
        """分析语调"""
        # 简单的语调分析
        formal_indicators = ['您', '请', '谢谢', '不好意思', '打扰']
        casual_indicators = ['哎', '嗯', '哈哈', '呵呵', '嘿']
        
        formal_count = 0
        casual_count = 0
        
        for dialogue in dialogue_samples:
            for indicator in formal_indicators:
                if indicator in dialogue:
                    formal_count += 1
            for indicator in casual_indicators:
                if indicator in dialogue:
                    casual_count += 1
        
        if formal_count > casual_count:
            return "formal"
        elif casual_count > formal_count:
            return "casual"
        else:
            return "neutral"
    
    def _extract_speech_patterns(self, dialogue_samples: List[str]) -> List[str]:
        """提取说话模式"""
        patterns = []
        
        # 检查常见的说话模式
        for dialogue in dialogue_samples:
            if dialogue.endswith('呢'):
                patterns.append("句尾常用'呢'")
            if dialogue.startswith('那个'):
                patterns.append("句首常用'那个'")
            if '就是说' in dialogue:
                patterns.append("常用'就是说'作为连接词")
        
        return list(set(patterns))  # 去重
    
    def _extract_catchphrases(self, dialogue_samples: List[str]) -> List[str]:
        """提取口头禅"""
        # 简单的口头禅提取
        word_count = defaultdict(int)
        
        for dialogue in dialogue_samples:
            words = dialogue.split()
            for word in words:
                if len(word) >= 2:  # 只考虑长度>=2的词
                    word_count[word] += 1
        
        # 返回出现频率较高的词作为口头禅
        catchphrases = []
        for word, count in word_count.items():
            if count >= 2:  # 至少出现2次
                catchphrases.append(word)
        
        return catchphrases[:5]  # 最多返回5个
    
    def _ai_analyze_dialogue_style(self, character: CharacterResponseSchema, dialogue_samples: List[str]) -> Optional[Dict[str, Any]]:
        """使用AI分析对话风格"""
        if not self.content_service or not dialogue_samples:
            return None
        
        try:
            prompt = f"""请分析以下角色的对话风格：
角色：{character.name}
性格：{character.personality or '未知'}
背景：{character.background or '未知'}

对话样本：
{chr(10).join(dialogue_samples)}

请分析角色的说话特点，包括用词习惯、句式特点、情感表达方式等。"""
            
            generation_request = GenerationRequest(
                content_type=ContentType.CHARACTER_PROFILE,
                prompt=prompt,
                context={"character": character.dict()}
            )
            
            ai_response = self.content_service.generate_content(generation_request)
            
            # 这里需要解析AI响应，提取结构化的对话风格信息
            # 暂时返回空字典
            return {}
            
        except Exception as e:
            logger.error(f"Failed to analyze dialogue style with AI: {str(e)}")
            return None
    
    def _save_dialogue_style(self, style: DialogueStyle):
        """保存对话风格"""
        # 将对话风格保存到角色的扩展信息中
        character = self.db.query(Character).filter(Character.id == style.character_id).first()
        if not character:
            return
        
        # 更新角色的扩展信息
        if not hasattr(character, 'dialogue_style') or not character.dialogue_style:
            character.dialogue_style = {}
        
        character.dialogue_style = style.dict()
        character.updated_at = datetime.utcnow()
        
        self.db.commit()
    
    def _load_dialogue_style(self, character_id: UUID) -> Optional[DialogueStyle]:
        """加载对话风格"""
        character = self.db.query(Character).filter(Character.id == character_id).first()
        if not character or not hasattr(character, 'dialogue_style') or not character.dialogue_style:
            return None
        
        try:
            style_data = character.dialogue_style
            # 转换时间字符串为datetime对象
            if isinstance(style_data.get("created_at"), str):
                style_data["created_at"] = datetime.fromisoformat(style_data["created_at"])
            if isinstance(style_data.get("updated_at"), str):
                style_data["updated_at"] = datetime.fromisoformat(style_data["updated_at"])
            
            return DialogueStyle(**style_data)
        except Exception as e:
            logger.warning(f"Failed to parse dialogue style data: {str(e)}")
            return None
    
    def _check_content_consistency(self, character: CharacterResponseSchema, content_samples: List[str]) -> List[Dict[str, Any]]:
        """检查内容一致性"""
        issues = []
        
        # 这里可以实现更复杂的一致性检查逻辑
        # 例如检查角色在不同内容中的行为是否一致
        
        return issues
    
    def _save_character_arc(self, arc: CharacterArc):
        """保存角色发展轨迹"""
        character = self.db.query(Character).filter(Character.id == arc.character_id).first()
        if not character:
            return
        
        # 获取现有的角色轨迹数据
        arcs_data = getattr(character, 'character_arcs', None) or {}
        arcs_list = arcs_data.get('arcs', [])
        
        # 添加新轨迹
        arcs_list.append(arc.dict())
        
        arcs_data['arcs'] = arcs_list
        character.character_arcs = arcs_data
        character.updated_at = datetime.utcnow()
        
        self.db.commit()
    
    def _load_character_arcs(self, character_id: UUID) -> List[CharacterArc]:
        """加载角色发展轨迹"""
        character = self.db.query(Character).filter(Character.id == character_id).first()
        if not character or not hasattr(character, 'character_arcs') or not character.character_arcs:
            return []
        
        arcs_data = character.character_arcs.get('arcs', [])
        arcs = []
        
        for arc_data in arcs_data:
            try:
                # 转换时间字符串为datetime对象
                if isinstance(arc_data.get("created_at"), str):
                    arc_data["created_at"] = datetime.fromisoformat(arc_data["created_at"])
                if isinstance(arc_data.get("updated_at"), str):
                    arc_data["updated_at"] = datetime.fromisoformat(arc_data["updated_at"])
                
                arcs.append(CharacterArc(**arc_data))
            except Exception as e:
                logger.warning(f"Failed to parse character arc data: {str(e)}")
                continue
        
        return arcs

# 服务实例获取函数
def get_character_service(db: Session = None, content_service: ContentService = None) -> CharacterService:
    """获取角色服务实例"""
    if db is None:
        db = next(get_db())
    return CharacterService(db, content_service)

# 导出的类和函数
__all__ = [
    "CharacterRelationship",
    "CharacterArc",
    "DialogueStyle",
    "CharacterGenerationRequest",
    "CharacterConsistencyCheck",
    "CharacterService",
    "get_character_service"
]