"""响应式布局组件

提供响应式设计支持，根据屏幕尺寸自动调整布局。
"""

import flet as ft
from typing import Dict, List, Optional, Union, Callable
from enum import Enum
from dataclasses import dataclass
from ..components.base import BaseComponent, theme_manager

class DeviceType(str, Enum):
    """设备类型枚举"""
    MOBILE = "mobile"
    TABLET = "tablet"
    DESKTOP = "desktop"
    LARGE_DESKTOP = "large_desktop"

@dataclass
class Breakpoint:
    """断点配置"""
    name: str
    min_width: int
    max_width: Optional[int] = None
    device_type: DeviceType = DeviceType.DESKTOP
    
    def matches(self, width: int) -> bool:
        """检查宽度是否匹配此断点"""
        if width < self.min_width:
            return False
        if self.max_width is not None and width > self.max_width:
            return False
        return True

@dataclass
class ResponsiveConfig:
    """响应式配置"""
    breakpoints: List[Breakpoint]
    default_breakpoint: str = "desktop"
    
    def get_current_breakpoint(self, width: int) -> Breakpoint:
        """根据宽度获取当前断点"""
        for breakpoint in reversed(self.breakpoints):
            if breakpoint.matches(width):
                return breakpoint
        
        # 如果没有匹配的断点，返回默认断点
        for breakpoint in self.breakpoints:
            if breakpoint.name == self.default_breakpoint:
                return breakpoint
        
        # 如果默认断点也不存在，返回第一个断点
        return self.breakpoints[0] if self.breakpoints else Breakpoint("default", 0)

class ResponsiveLayout(BaseComponent):
    """响应式布局组件"""
    
    # 默认断点配置
    DEFAULT_BREAKPOINTS = [
        Breakpoint("mobile", 0, 767, DeviceType.MOBILE),
        Breakpoint("tablet", 768, 1023, DeviceType.TABLET),
        Breakpoint("desktop", 1024, 1439, DeviceType.DESKTOP),
        Breakpoint("large_desktop", 1440, None, DeviceType.LARGE_DESKTOP),
    ]
    
    def __init__(
        self,
        children: Dict[str, ft.Control],  # 断点名称 -> 控件
        config: Optional[ResponsiveConfig] = None,
        on_breakpoint_change: Optional[Callable[[Breakpoint], None]] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.children = children
        self.config = config or ResponsiveConfig(self.DEFAULT_BREAKPOINTS)
        self.on_breakpoint_change = on_breakpoint_change
        self._current_breakpoint: Optional[Breakpoint] = None
        self._current_width = 1024  # 默认宽度
    
    def _update_theme_styles(self):
        """更新主题样式"""
        # 响应式布局的样式更新由子组件处理
        pass
    
    def _on_page_resize(self, e):
        """页面尺寸变化回调"""
        if hasattr(e, 'width'):
            self._update_layout(e.width)
    
    def _update_layout(self, width: int):
        """更新布局"""
        self._current_width = width
        new_breakpoint = self.config.get_current_breakpoint(width)
        
        if self._current_breakpoint != new_breakpoint:
            self._current_breakpoint = new_breakpoint
            
            # 通知断点变化
            if self.on_breakpoint_change:
                self.on_breakpoint_change(new_breakpoint)
            
            # 更新显示的内容
            self._update_content()
    
    def _update_content(self):
        """更新显示内容"""
        if not self._control or not self._current_breakpoint:
            return
        
        # 查找匹配的内容
        content = None
        
        # 首先尝试精确匹配断点名称
        if self._current_breakpoint.name in self.children:
            content = self.children[self._current_breakpoint.name]
        # 然后尝试匹配设备类型
        elif self._current_breakpoint.device_type.value in self.children:
            content = self.children[self._current_breakpoint.device_type.value]
        # 最后使用默认内容
        elif "default" in self.children:
            content = self.children["default"]
        # 如果都没有，使用第一个可用的内容
        elif self.children:
            content = next(iter(self.children.values()))
        
        if content and hasattr(self._control, 'content'):
            self._control.content = content
            self.update()
    
    def build(self) -> ft.Control:
        """构建响应式布局"""
        # 获取初始断点
        self._current_breakpoint = self.config.get_current_breakpoint(self._current_width)
        
        # 获取初始内容
        initial_content = None
        if self._current_breakpoint.name in self.children:
            initial_content = self.children[self._current_breakpoint.name]
        elif "default" in self.children:
            initial_content = self.children["default"]
        elif self.children:
            initial_content = next(iter(self.children.values()))
        
        container = ft.Container(
            content=initial_content,
            expand=True,
        )
        
        return container
    
    def add_breakpoint_content(self, breakpoint_name: str, content: ft.Control):
        """添加断点内容"""
        self.children[breakpoint_name] = content
        self._update_content()
    
    def remove_breakpoint_content(self, breakpoint_name: str):
        """移除断点内容"""
        if breakpoint_name in self.children:
            del self.children[breakpoint_name]
            self._update_content()
    
    def get_current_breakpoint(self) -> Optional[Breakpoint]:
        """获取当前断点"""
        return self._current_breakpoint
    
    def get_current_device_type(self) -> Optional[DeviceType]:
        """获取当前设备类型"""
        return self._current_breakpoint.device_type if self._current_breakpoint else None
    
    def is_mobile(self) -> bool:
        """是否为移动设备"""
        return self.get_current_device_type() == DeviceType.MOBILE
    
    def is_tablet(self) -> bool:
        """是否为平板设备"""
        return self.get_current_device_type() == DeviceType.TABLET
    
    def is_desktop(self) -> bool:
        """是否为桌面设备"""
        device_type = self.get_current_device_type()
        return device_type in [DeviceType.DESKTOP, DeviceType.LARGE_DESKTOP]
    
    def set_page_resize_handler(self, page: ft.Page):
        """设置页面尺寸变化处理器"""
        page.on_resize = self._on_page_resize
        # 初始化当前尺寸
        if hasattr(page, 'width') and page.width:
            self._update_layout(page.width)

# 响应式工具函数

def create_responsive_row(
    mobile_content: List[ft.Control],
    tablet_content: Optional[List[ft.Control]] = None,
    desktop_content: Optional[List[ft.Control]] = None,
    spacing: Optional[int] = None
) -> ResponsiveLayout:
    """创建响应式行布局"""
    theme = theme_manager.get_current_theme()
    default_spacing = theme.spacing.md if theme else 16
    
    children = {
        "mobile": ft.Column(
            mobile_content,
            spacing=spacing or default_spacing,
            tight=True
        )
    }
    
    if tablet_content:
        children["tablet"] = ft.Row(
            tablet_content,
            spacing=spacing or default_spacing,
            wrap=True
        )
    
    if desktop_content:
        children["desktop"] = ft.Row(
            desktop_content,
            spacing=spacing or default_spacing
        )
    
    return ResponsiveLayout(children=children)

def create_responsive_grid(
    items: List[ft.Control],
    mobile_columns: int = 1,
    tablet_columns: int = 2,
    desktop_columns: int = 3,
    spacing: Optional[int] = None
) -> ResponsiveLayout:
    """创建响应式网格布局"""
    theme = theme_manager.get_current_theme()
    default_spacing = theme.spacing.md if theme else 16
    
    def create_grid(columns: int) -> ft.Control:
        rows = []
        for i in range(0, len(items), columns):
            row_items = items[i:i + columns]
            # 填充空白项以保持网格对齐
            while len(row_items) < columns:
                row_items.append(ft.Container())
            
            rows.append(
                ft.Row(
                    row_items,
                    spacing=spacing or default_spacing,
                    alignment=ft.MainAxisAlignment.START
                )
            )
        
        return ft.Column(
            rows,
            spacing=spacing or default_spacing,
            tight=True
        )
    
    children = {
        "mobile": create_grid(mobile_columns),
        "tablet": create_grid(tablet_columns),
        "desktop": create_grid(desktop_columns),
    }
    
    return ResponsiveLayout(children=children)

def create_responsive_sidebar(
    sidebar_content: ft.Control,
    main_content: ft.Control,
    sidebar_width: int = 250,
    collapse_on_mobile: bool = True
) -> ResponsiveLayout:
    """创建响应式侧边栏布局"""
    # 桌面布局：侧边栏 + 主内容
    desktop_layout = ft.Row(
        [
            ft.Container(
                content=sidebar_content,
                width=sidebar_width,
                bgcolor=theme_manager.get_current_theme().colors.surface if theme_manager.get_current_theme() else None
            ),
            ft.Container(
                content=main_content,
                expand=True
            )
        ],
        spacing=0,
        expand=True
    )
    
    # 移动布局：仅主内容（侧边栏折叠）
    mobile_layout = main_content if collapse_on_mobile else ft.Column(
        [sidebar_content, main_content],
        spacing=0,
        expand=True
    )
    
    children = {
        "mobile": mobile_layout,
        "tablet": desktop_layout,
        "desktop": desktop_layout,
    }
    
    return ResponsiveLayout(children=children)

# 导出
__all__ = [
    'DeviceType',
    'Breakpoint',
    'ResponsiveConfig',
    'ResponsiveLayout',
    'create_responsive_row',
    'create_responsive_grid',
    'create_responsive_sidebar',
]