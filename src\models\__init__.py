"""数据模型模块

提供应用程序的数据模型定义，包括：
- 基础模型类和混入
- 项目相关模型
- 用户和角色模型
- AI服务相关模型
- 数据库会话管理
"""

from .base import (
    Base,
    BaseTable,
    BaseSchema,
    BaseResponseSchema,
    BaseCreateSchema,
    BaseUpdateSchema,
    TimestampMixin,
    SoftDeleteMixin,
    UserTrackingMixin
)

# 导出基础类
__all__ = [
    "Base",
    "BaseTable",
    "BaseSchema",
    "BaseResponseSchema",
    "BaseCreateSchema",
    "BaseUpdateSchema",
    "TimestampMixin",
    "SoftDeleteMixin",
    "UserTrackingMixin"
]

# 版本信息
__version__ = "1.0.0"