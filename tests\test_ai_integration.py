"""AI服务集成测试

测试AI服务的实际集成、错误处理和性能。
"""

import pytest
import asyncio
import time
import json
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import responses
import aiohttp
from aioresponses import aioresponses

# 导入AI服务相关模块
from src.services.ai_service import AIService
from src.adapters.openai_adapter import OpenAIAdapter
from src.adapters.claude_adapter import ClaudeAdapter
from src.adapters.local_ai_adapter import LocalAIAdapter
from src.models.ai_service import (
    AIRequest, AIResponse, AIFunction, ProcessStatus,
    AIModel, AIProvider, AIConfig
)
from src.core.exceptions import (
    AIServiceError, RateLimitError, AuthenticationError,
    ModelNotAvailableError, QuotaExceededError
)


class TestAIAdapterIntegration:
    """AI适配器集成测试"""
    
    @pytest.fixture
    def openai_config(self):
        """OpenAI配置"""
        return AIConfig(
            provider=AIProvider.OPENAI,
            api_key="test-api-key",
            model=AIModel.GPT_3_5_TURBO,
            base_url="https://api.openai.com/v1",
            timeout=30,
            max_retries=3
        )
    
    @pytest.fixture
    def claude_config(self):
        """Claude配置"""
        return AIConfig(
            provider=AIProvider.CLAUDE,
            api_key="test-claude-key",
            model=AIModel.CLAUDE_3_SONNET,
            base_url="https://api.anthropic.com",
            timeout=30,
            max_retries=3
        )
    
    @pytest.fixture
    def local_config(self):
        """本地AI配置"""
        return AIConfig(
            provider=AIProvider.LOCAL,
            model=AIModel.LOCAL_LLAMA,
            base_url="http://localhost:11434",
            timeout=60,
            max_retries=2
        )
    
    @responses.activate
    def test_openai_adapter_success(self, openai_config):
        """测试OpenAI适配器成功调用"""
        # 模拟OpenAI API响应
        responses.add(
            responses.POST,
            "https://api.openai.com/v1/chat/completions",
            json={
                "id": "chatcmpl-123",
                "object": "chat.completion",
                "created": **********,
                "model": "gpt-3.5-turbo",
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": "这是AI生成的小说内容，充满了想象力和创意。"
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": 50,
                    "completion_tokens": 100,
                    "total_tokens": 150
                }
            },
            status=200
        )
        
        adapter = OpenAIAdapter(openai_config)
        
        request = AIRequest(
            function=AIFunction.CONTINUE_WRITING,
            prompt="请继续写这个科幻小说",
            context="在遥远的未来，人类已经掌握了星际旅行技术...",
            parameters={
                "temperature": 0.7,
                "max_tokens": 1000
            }
        )
        
        response = adapter.generate_text(request)
        
        assert isinstance(response, AIResponse)
        assert "AI生成的小说内容" in response.content
        assert response.tokens_used == 150
        assert response.model == "gpt-3.5-turbo"
        assert response.status == ProcessStatus.COMPLETED
    
    @responses.activate
    def test_openai_adapter_rate_limit(self, openai_config):
        """测试OpenAI适配器速率限制处理"""
        # 模拟速率限制响应
        responses.add(
            responses.POST,
            "https://api.openai.com/v1/chat/completions",
            json={
                "error": {
                    "message": "Rate limit reached",
                    "type": "rate_limit_error",
                    "code": "rate_limit_exceeded"
                }
            },
            status=429,
            headers={"Retry-After": "60"}
        )
        
        adapter = OpenAIAdapter(openai_config)
        
        request = AIRequest(
            function=AIFunction.GENERATE_OUTLINE,
            prompt="生成小说大纲"
        )
        
        with pytest.raises(RateLimitError) as exc_info:
            adapter.generate_text(request)
        
        assert "Rate limit reached" in str(exc_info.value)
        assert exc_info.value.retry_after == 60
    
    @responses.activate
    def test_openai_adapter_authentication_error(self, openai_config):
        """测试OpenAI适配器认证错误"""
        # 模拟认证错误响应
        responses.add(
            responses.POST,
            "https://api.openai.com/v1/chat/completions",
            json={
                "error": {
                    "message": "Invalid API key",
                    "type": "invalid_request_error",
                    "code": "invalid_api_key"
                }
            },
            status=401
        )
        
        adapter = OpenAIAdapter(openai_config)
        
        request = AIRequest(
            function=AIFunction.IMPROVE_TEXT,
            prompt="改进这段文字",
            context="原始文字内容"
        )
        
        with pytest.raises(AuthenticationError) as exc_info:
            adapter.generate_text(request)
        
        assert "Invalid API key" in str(exc_info.value)
    
    @responses.activate
    def test_claude_adapter_success(self, claude_config):
        """测试Claude适配器成功调用"""
        # 模拟Claude API响应
        responses.add(
            responses.POST,
            "https://api.anthropic.com/v1/messages",
            json={
                "id": "msg_123",
                "type": "message",
                "role": "assistant",
                "content": [{
                    "type": "text",
                    "text": "这是Claude生成的优质小说内容，具有深度和文学性。"
                }],
                "model": "claude-3-sonnet-20240229",
                "stop_reason": "end_turn",
                "usage": {
                    "input_tokens": 45,
                    "output_tokens": 120
                }
            },
            status=200
        )
        
        adapter = ClaudeAdapter(claude_config)
        
        request = AIRequest(
            function=AIFunction.CHARACTER_DEVELOPMENT,
            prompt="为这个角色创建详细的背景故事",
            context="角色：李明，25岁程序员",
            parameters={
                "temperature": 0.8,
                "max_tokens": 1500
            }
        )
        
        response = adapter.generate_text(request)
        
        assert isinstance(response, AIResponse)
        assert "Claude生成的优质小说内容" in response.content
        assert response.tokens_used == 165  # input + output
        assert response.model == "claude-3-sonnet-20240229"
        assert response.status == ProcessStatus.COMPLETED
    
    @responses.activate
    def test_local_ai_adapter_success(self, local_config):
        """测试本地AI适配器成功调用"""
        # 模拟本地AI API响应
        responses.add(
            responses.POST,
            "http://localhost:11434/api/generate",
            json={
                "model": "llama2",
                "created_at": "2024-01-01T12:00:00Z",
                "response": "这是本地AI模型生成的内容，虽然可能不如商业模型，但提供了隐私保护。",
                "done": True,
                "context": [1, 2, 3, 4, 5],
                "total_duration": 5000000000,
                "load_duration": 1000000000,
                "prompt_eval_count": 30,
                "prompt_eval_duration": 2000000000,
                "eval_count": 80,
                "eval_duration": 2000000000
            },
            status=200
        )
        
        adapter = LocalAIAdapter(local_config)
        
        request = AIRequest(
            function=AIFunction.SCENE_DESCRIPTION,
            prompt="描述一个神秘的森林场景",
            parameters={
                "temperature": 0.6,
                "max_tokens": 800
            }
        )
        
        response = adapter.generate_text(request)
        
        assert isinstance(response, AIResponse)
        assert "本地AI模型生成的内容" in response.content
        assert response.model == "llama2"
        assert response.status == ProcessStatus.COMPLETED
        assert response.processing_time > 0


class TestAIServiceIntegration:
    """AI服务集成测试"""
    
    @pytest.fixture
    def ai_service(self):
        """AI服务实例"""
        return AIService()
    
    @pytest.fixture
    def mock_adapters(self):
        """模拟适配器"""
        openai_adapter = Mock(spec=OpenAIAdapter)
        claude_adapter = Mock(spec=ClaudeAdapter)
        local_adapter = Mock(spec=LocalAIAdapter)
        
        return {
            AIProvider.OPENAI: openai_adapter,
            AIProvider.CLAUDE: claude_adapter,
            AIProvider.LOCAL: local_adapter
        }
    
    @pytest.mark.asyncio
    async def test_multi_provider_fallback(self, ai_service, mock_adapters):
        """测试多提供商故障转移"""
        # 配置模拟适配器
        mock_adapters[AIProvider.OPENAI].generate_text.side_effect = RateLimitError("Rate limit")
        mock_adapters[AIProvider.CLAUDE].generate_text.return_value = AIResponse(
            content="Claude生成的内容",
            tokens_used=100,
            model="claude-3-sonnet",
            status=ProcessStatus.COMPLETED
        )
        
        with patch.object(ai_service, '_get_adapters', return_value=mock_adapters):
            request = AIRequest(
                function=AIFunction.CONTINUE_WRITING,
                prompt="继续写作",
                providers=[AIProvider.OPENAI, AIProvider.CLAUDE]  # 设置故障转移顺序
            )
            
            response = await ai_service.process_request_with_fallback(request)
            
            assert response.content == "Claude生成的内容"
            assert response.provider == AIProvider.CLAUDE
            
            # 验证调用顺序
            mock_adapters[AIProvider.OPENAI].generate_text.assert_called_once()
            mock_adapters[AIProvider.CLAUDE].generate_text.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, ai_service, mock_adapters):
        """测试并发请求处理"""
        # 配置模拟适配器
        async def mock_generate(request):
            await asyncio.sleep(0.1)  # 模拟处理时间
            return AIResponse(
                content=f"生成的内容 - {request.prompt}",
                tokens_used=50,
                model="test-model",
                status=ProcessStatus.COMPLETED
            )
        
        mock_adapters[AIProvider.OPENAI].generate_text = AsyncMock(side_effect=mock_generate)
        
        with patch.object(ai_service, '_get_adapters', return_value=mock_adapters):
            # 创建多个请求
            requests = [
                AIRequest(
                    function=AIFunction.CONTINUE_WRITING,
                    prompt=f"请求 {i}",
                    provider=AIProvider.OPENAI
                )
                for i in range(5)
            ]
            
            # 并发处理
            start_time = time.time()
            tasks = [ai_service.process_request(req.id) for req in requests]
            responses = await asyncio.gather(*tasks)
            end_time = time.time()
            
            # 验证结果
            assert len(responses) == 5
            for i, response in enumerate(responses):
                assert f"请求 {i}" in response.content
            
            # 验证并发性能（应该比串行快）
            assert end_time - start_time < 0.3  # 应该远小于 5 * 0.1 = 0.5秒
    
    @pytest.mark.asyncio
    async def test_request_timeout_handling(self, ai_service, mock_adapters):
        """测试请求超时处理"""
        # 配置超时的模拟适配器
        async def timeout_generate(request):
            await asyncio.sleep(10)  # 模拟长时间处理
            return AIResponse(content="不应该返回", tokens_used=0)
        
        mock_adapters[AIProvider.OPENAI].generate_text = AsyncMock(side_effect=timeout_generate)
        
        with patch.object(ai_service, '_get_adapters', return_value=mock_adapters):
            request = AIRequest(
                function=AIFunction.GENERATE_OUTLINE,
                prompt="生成大纲",
                provider=AIProvider.OPENAI,
                timeout=1  # 1秒超时
            )
            
            with pytest.raises(asyncio.TimeoutError):
                await ai_service.process_request(request.id)
    
    def test_request_queue_management(self, ai_service):
        """测试请求队列管理"""
        # 创建多个请求
        requests = []
        for i in range(10):
            request = ai_service.create_request(
                function=AIFunction.CONTINUE_WRITING,
                prompt=f"请求 {i}",
                priority=i % 3  # 不同优先级
            )
            requests.append(request)
        
        # 获取队列状态
        queue_status = ai_service.get_queue_status()
        
        assert queue_status["total_requests"] >= 10
        assert queue_status["pending_requests"] >= 10
        assert "high_priority" in queue_status
        assert "medium_priority" in queue_status
        assert "low_priority" in queue_status
    
    def test_request_history_and_caching(self, ai_service):
        """测试请求历史和缓存"""
        # 创建相同的请求
        request1 = ai_service.create_request(
            function=AIFunction.IMPROVE_TEXT,
            prompt="改进这段文字",
            context="原始文字"
        )
        
        request2 = ai_service.create_request(
            function=AIFunction.IMPROVE_TEXT,
            prompt="改进这段文字",
            context="原始文字"
        )
        
        # 检查是否识别为重复请求
        is_duplicate = ai_service.is_duplicate_request(request2)
        assert is_duplicate is True
        
        # 获取历史记录
        history = ai_service.get_request_history(limit=20)
        assert len(history) >= 2
        
        # 测试缓存功能
        cached_response = ai_service.get_cached_response(request1.get_cache_key())
        # 首次请求应该没有缓存
        assert cached_response is None


class TestAIPerformanceBenchmarks:
    """AI性能基准测试"""
    
    @pytest.fixture
    def performance_ai_service(self):
        """性能测试用AI服务"""
        return AIService()
    
    @pytest.mark.asyncio
    async def test_throughput_benchmark(self, performance_ai_service):
        """测试吞吐量基准"""
        # 模拟快速响应的适配器
        mock_adapter = AsyncMock()
        mock_adapter.generate_text.return_value = AIResponse(
            content="基准测试内容",
            tokens_used=100,
            model="benchmark-model",
            status=ProcessStatus.COMPLETED
        )
        
        with patch.object(performance_ai_service, '_get_adapter', return_value=mock_adapter):
            # 创建大量请求
            num_requests = 100
            requests = [
                AIRequest(
                    function=AIFunction.CONTINUE_WRITING,
                    prompt=f"基准测试请求 {i}"
                )
                for i in range(num_requests)
            ]
            
            # 测量处理时间
            start_time = time.time()
            
            # 批量处理
            tasks = [performance_ai_service.process_request(req.id) for req in requests]
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            
            # 计算性能指标
            total_time = end_time - start_time
            throughput = num_requests / total_time
            
            # 验证性能
            assert len(responses) == num_requests
            assert throughput > 50  # 每秒至少处理50个请求
            
            # 记录性能指标
            print(f"\n性能基准测试结果:")
            print(f"总请求数: {num_requests}")
            print(f"总时间: {total_time:.2f}秒")
            print(f"吞吐量: {throughput:.2f} 请求/秒")
    
    @pytest.mark.asyncio
    async def test_memory_usage_benchmark(self, performance_ai_service):
        """测试内存使用基准"""
        import psutil
        import os
        
        # 获取初始内存使用
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建大量请求和响应
        requests = []
        for i in range(1000):
            request = performance_ai_service.create_request(
                function=AIFunction.CONTINUE_WRITING,
                prompt=f"内存测试请求 {i}" * 100,  # 较长的提示
                context="上下文内容" * 200  # 较长的上下文
            )
            requests.append(request)
        
        # 获取峰值内存使用
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 清理请求
        for request in requests:
            performance_ai_service.cancel_request(request.id)
        
        # 强制垃圾回收
        import gc
        gc.collect()
        
        # 获取清理后内存使用
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 验证内存使用
        memory_increase = peak_memory - initial_memory
        memory_cleanup = peak_memory - final_memory
        
        print(f"\n内存使用基准测试结果:")
        print(f"初始内存: {initial_memory:.2f} MB")
        print(f"峰值内存: {peak_memory:.2f} MB")
        print(f"最终内存: {final_memory:.2f} MB")
        print(f"内存增长: {memory_increase:.2f} MB")
        print(f"内存清理: {memory_cleanup:.2f} MB")
        
        # 验证内存没有严重泄漏
        assert memory_increase < 500  # 内存增长不超过500MB
        assert memory_cleanup > memory_increase * 0.8  # 至少清理80%的内存
    
    @pytest.mark.asyncio
    async def test_error_recovery_benchmark(self, performance_ai_service):
        """测试错误恢复基准"""
        error_count = 0
        success_count = 0
        recovery_times = []
        
        # 模拟不稳定的适配器
        mock_adapter = AsyncMock()
        
        async def unstable_generate(request):
            nonlocal error_count, success_count
            
            # 30%的概率出错
            if error_count < 30 and (error_count + success_count) % 3 == 0:
                error_count += 1
                raise AIServiceError("模拟的服务错误")
            else:
                success_count += 1
                return AIResponse(
                    content="恢复测试内容",
                    tokens_used=50,
                    model="recovery-model",
                    status=ProcessStatus.COMPLETED
                )
        
        mock_adapter.generate_text.side_effect = unstable_generate
        
        with patch.object(performance_ai_service, '_get_adapter', return_value=mock_adapter):
            # 创建请求并测试恢复
            num_requests = 100
            start_time = time.time()
            
            for i in range(num_requests):
                request = AIRequest(
                    function=AIFunction.CONTINUE_WRITING,
                    prompt=f"恢复测试请求 {i}"
                )
                
                try:
                    recovery_start = time.time()
                    await performance_ai_service.process_request_with_retry(request.id, max_retries=3)
                    recovery_time = time.time() - recovery_start
                    recovery_times.append(recovery_time)
                except Exception:
                    pass  # 记录但继续测试
            
            end_time = time.time()
            
            # 计算恢复性能指标
            total_time = end_time - start_time
            avg_recovery_time = sum(recovery_times) / len(recovery_times) if recovery_times else 0
            success_rate = success_count / (success_count + error_count) * 100
            
            print(f"\n错误恢复基准测试结果:")
            print(f"总请求数: {num_requests}")
            print(f"成功次数: {success_count}")
            print(f"错误次数: {error_count}")
            print(f"成功率: {success_rate:.2f}%")
            print(f"平均恢复时间: {avg_recovery_time:.3f}秒")
            print(f"总测试时间: {total_time:.2f}秒")
            
            # 验证恢复性能
            assert success_rate > 70  # 成功率应该超过70%
            assert avg_recovery_time < 2.0  # 平均恢复时间应该小于2秒


class TestAIServiceReliability:
    """AI服务可靠性测试"""
    
    @pytest.fixture
    def reliable_ai_service(self):
        """可靠性测试用AI服务"""
        return AIService()
    
    @pytest.mark.asyncio
    async def test_long_running_stability(self, reliable_ai_service):
        """测试长时间运行稳定性"""
        # 模拟长时间运行的场景
        mock_adapter = AsyncMock()
        request_count = 0
        
        async def stable_generate(request):
            nonlocal request_count
            request_count += 1
            
            # 模拟偶发的网络延迟
            if request_count % 10 == 0:
                await asyncio.sleep(0.5)
            
            return AIResponse(
                content=f"稳定性测试内容 {request_count}",
                tokens_used=75,
                model="stability-model",
                status=ProcessStatus.COMPLETED
            )
        
        mock_adapter.generate_text.side_effect = stable_generate
        
        with patch.object(reliable_ai_service, '_get_adapter', return_value=mock_adapter):
            # 运行长时间测试（模拟1小时的请求）
            test_duration = 10  # 实际测试10秒，模拟长时间
            start_time = time.time()
            
            successful_requests = 0
            failed_requests = 0
            
            while time.time() - start_time < test_duration:
                try:
                    request = AIRequest(
                        function=AIFunction.CONTINUE_WRITING,
                        prompt="稳定性测试"
                    )
                    
                    response = await reliable_ai_service.process_request(request.id)
                    if response.status == ProcessStatus.COMPLETED:
                        successful_requests += 1
                    else:
                        failed_requests += 1
                        
                except Exception:
                    failed_requests += 1
                
                # 短暂休息模拟真实使用
                await asyncio.sleep(0.1)
            
            # 验证稳定性
            total_requests = successful_requests + failed_requests
            success_rate = successful_requests / total_requests * 100 if total_requests > 0 else 0
            
            print(f"\n长时间运行稳定性测试结果:")
            print(f"测试时长: {test_duration}秒")
            print(f"总请求数: {total_requests}")
            print(f"成功请求: {successful_requests}")
            print(f"失败请求: {failed_requests}")
            print(f"成功率: {success_rate:.2f}%")
            
            assert success_rate > 95  # 稳定性应该超过95%
            assert total_requests > 50  # 应该处理足够多的请求
    
    def test_resource_cleanup(self, reliable_ai_service):
        """测试资源清理"""
        # 创建大量请求
        requests = []
        for i in range(100):
            request = reliable_ai_service.create_request(
                function=AIFunction.CONTINUE_WRITING,
                prompt=f"资源测试 {i}"
            )
            requests.append(request)
        
        # 验证请求已创建
        initial_count = len(reliable_ai_service.get_all_requests())
        assert initial_count >= 100
        
        # 清理一半请求
        for i in range(0, 50):
            reliable_ai_service.cancel_request(requests[i].id)
        
        # 运行清理
        cleaned_count = reliable_ai_service.cleanup_completed_requests()
        
        # 验证清理效果
        remaining_count = len(reliable_ai_service.get_all_requests())
        
        print(f"\n资源清理测试结果:")
        print(f"初始请求数: {initial_count}")
        print(f"清理的请求数: {cleaned_count}")
        print(f"剩余请求数: {remaining_count}")
        
        assert remaining_count < initial_count
        assert cleaned_count > 0
    
    @pytest.mark.asyncio
    async def test_concurrent_load_handling(self, reliable_ai_service):
        """测试并发负载处理"""
        # 模拟高并发场景
        mock_adapter = AsyncMock()
        
        async def concurrent_generate(request):
            # 模拟处理时间
            await asyncio.sleep(0.05 + (hash(request.prompt) % 10) * 0.01)
            return AIResponse(
                content=f"并发测试内容 - {request.prompt}",
                tokens_used=60,
                model="concurrent-model",
                status=ProcessStatus.COMPLETED
            )
        
        mock_adapter.generate_text.side_effect = concurrent_generate
        
        with patch.object(reliable_ai_service, '_get_adapter', return_value=mock_adapter):
            # 创建高并发请求
            concurrent_requests = 50
            
            async def create_and_process_request(i):
                request = AIRequest(
                    function=AIFunction.CONTINUE_WRITING,
                    prompt=f"并发请求 {i}"
                )
                return await reliable_ai_service.process_request(request.id)
            
            # 并发执行
            start_time = time.time()
            tasks = [create_and_process_request(i) for i in range(concurrent_requests)]
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # 分析结果
            successful_responses = [r for r in responses if isinstance(r, AIResponse)]
            failed_responses = [r for r in responses if isinstance(r, Exception)]
            
            total_time = end_time - start_time
            throughput = len(successful_responses) / total_time
            
            print(f"\n并发负载处理测试结果:")
            print(f"并发请求数: {concurrent_requests}")
            print(f"成功响应: {len(successful_responses)}")
            print(f"失败响应: {len(failed_responses)}")
            print(f"总处理时间: {total_time:.2f}秒")
            print(f"吞吐量: {throughput:.2f} 请求/秒")
            
            # 验证并发处理能力
            success_rate = len(successful_responses) / concurrent_requests * 100
            assert success_rate > 90  # 并发成功率应该超过90%
            assert throughput > 20  # 并发吞吐量应该超过20请求/秒


if __name__ == "__main__":
    # 运行所有测试
    pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "-x",  # 遇到第一个失败就停止
        "--durations=10"  # 显示最慢的10个测试
    ])