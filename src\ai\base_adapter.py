"""AI服务抽象接口

定义AI服务适配器基类和标准接口，为多AI模型集成提供统一接口。
支持通用的错误处理、重试机制、成本计算等功能。
"""

import asyncio
import time
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union, AsyncGenerator, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta

# 配置日志
logger = logging.getLogger(__name__)

class AIModelType(str, Enum):
    """AI模型类型枚举"""
    # OpenAI模型
    GPT_3_5_TURBO = "gpt-3.5-turbo"
    GPT_4 = "gpt-4"
    GPT_4_TURBO = "gpt-4-turbo"
    GPT_4O = "gpt-4o"
    GPT_4O_MINI = "gpt-4o-mini"
    
    # Claude模型
    CLAUDE_3_HAIKU = "claude-3-haiku"
    CLAUDE_3_SONNET = "claude-3-sonnet"
    CLAUDE_3_OPUS = "claude-3-opus"
    CLAUDE_3_5_SONNET = "claude-3-5-sonnet"
    
    # Google模型
    GEMINI_PRO = "gemini-pro"
    GEMINI_ULTRA = "gemini-ultra"
    
    # 智谱AI模型
    GLM_4 = "glm-4"
    GLM_3_TURBO = "glm-3-turbo"
    CHATGLM_3 = "chatglm-3"
    
    # 百度文心模型
    ERNIE_4 = "ernie-4.0"
    ERNIE_3_5 = "ernie-3.5"
    
    # 阿里通义模型
    QWEN_MAX = "qwen-max"
    QWEN_PLUS = "qwen-plus"
    QWEN_TURBO = "qwen-turbo"
    
    # 百川模型
    BAICHUAN_2 = "baichuan-2"
    
    # 自定义模型
    CUSTOM = "custom"

class AITaskType(str, Enum):
    """AI任务类型枚举"""
    TEXT_GENERATION = "text_generation"      # 文本生成
    TEXT_COMPLETION = "text_completion"      # 文本补全
    TEXT_EDITING = "text_editing"            # 文本编辑
    SUMMARIZATION = "summarization"          # 文本摘要
    TRANSLATION = "translation"              # 翻译
    ANALYSIS = "analysis"                    # 文本分析
    CREATIVE_WRITING = "creative_writing"    # 创意写作
    DIALOGUE_GENERATION = "dialogue_generation"  # 对话生成
    CHARACTER_DEVELOPMENT = "character_development"  # 角色发展
    PLOT_DEVELOPMENT = "plot_development"    # 情节发展
    STYLE_ADAPTATION = "style_adaptation"    # 风格适应
    GRAMMAR_CHECK = "grammar_check"          # 语法检查
    CONTENT_OPTIMIZATION = "content_optimization"  # 内容优化

class AIResponseStatus(str, Enum):
    """AI响应状态枚举"""
    SUCCESS = "success"          # 成功
    PARTIAL_SUCCESS = "partial_success"  # 部分成功
    FAILED = "failed"            # 失败
    TIMEOUT = "timeout"          # 超时
    RATE_LIMITED = "rate_limited"  # 限流
    QUOTA_EXCEEDED = "quota_exceeded"  # 配额超限
    INVALID_REQUEST = "invalid_request"  # 无效请求
    MODEL_ERROR = "model_error"  # 模型错误
    NETWORK_ERROR = "network_error"  # 网络错误

@dataclass
class AIMessage:
    """AI消息数据类"""
    role: str  # system, user, assistant
    content: str
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AIRequest:
    """AI请求数据类"""
    task_type: AITaskType
    messages: List[AIMessage]
    model: AIModelType
    parameters: Dict[str, Any] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    frequency_penalty: Optional[float] = None
    presence_penalty: Optional[float] = None
    stop_sequences: Optional[List[str]] = None
    stream: bool = False
    timeout: int = 30
    retry_count: int = 3
    retry_delay: float = 1.0

@dataclass
class AIResponse:
    """AI响应数据类"""
    status: AIResponseStatus
    content: str
    model: AIModelType
    task_type: AITaskType
    usage: Dict[str, Any] = field(default_factory=dict)
    cost: float = 0.0
    latency: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    raw_response: Optional[Dict[str, Any]] = None

@dataclass
class AIUsageStats:
    """AI使用统计数据类"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_tokens: int = 0
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_cost: float = 0.0
    average_latency: float = 0.0
    last_request_time: Optional[datetime] = None
    rate_limit_hits: int = 0
    quota_exceeded_count: int = 0

class AIServiceError(Exception):
    """AI服务异常基类"""
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.error_code = error_code
        self.details = details or {}

class AIRateLimitError(AIServiceError):
    """AI服务限流异常"""
    pass

class AIQuotaExceededError(AIServiceError):
    """AI服务配额超限异常"""
    pass

class AIModelError(AIServiceError):
    """AI模型异常"""
    pass

class AINetworkError(AIServiceError):
    """AI网络异常"""
    pass

class BaseAIAdapter(ABC):
    """AI服务适配器基类
    
    定义所有AI服务适配器必须实现的标准接口。
    """
    
    def __init__(self, 
                 api_key: str,
                 base_url: Optional[str] = None,
                 timeout: int = 30,
                 max_retries: int = 3,
                 retry_delay: float = 1.0):
        """初始化AI适配器
        
        Args:
            api_key: API密钥
            base_url: API基础URL
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间（秒）
        """
        self.api_key = api_key
        self.base_url = base_url
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.usage_stats = AIUsageStats()
        self._rate_limiter = None
        self._cost_calculator = None
    
    @property
    @abstractmethod
    def supported_models(self) -> List[AIModelType]:
        """获取支持的模型列表"""
        pass
    
    @property
    @abstractmethod
    def supported_tasks(self) -> List[AITaskType]:
        """获取支持的任务类型列表"""
        pass
    
    @property
    @abstractmethod
    def provider_name(self) -> str:
        """获取服务提供商名称"""
        pass
    
    @abstractmethod
    async def generate_text(self, request: AIRequest) -> AIResponse:
        """生成文本
        
        Args:
            request: AI请求对象
            
        Returns:
            AIResponse: AI响应对象
        """
        pass
    
    @abstractmethod
    async def generate_text_stream(self, request: AIRequest) -> AsyncGenerator[str, None]:
        """流式生成文本
        
        Args:
            request: AI请求对象
            
        Yields:
            str: 生成的文本片段
        """
        pass
    
    @abstractmethod
    async def validate_api_key(self) -> bool:
        """验证API密钥是否有效
        
        Returns:
            bool: 密钥有效返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def calculate_cost(self, usage: Dict[str, Any], model: AIModelType) -> float:
        """计算请求成本
        
        Args:
            usage: 使用量信息
            model: 使用的模型
            
        Returns:
            float: 成本（美元）
        """
        pass
    
    @abstractmethod
    def get_model_limits(self, model: AIModelType) -> Dict[str, Any]:
        """获取模型限制信息
        
        Args:
            model: 模型类型
            
        Returns:
            Dict[str, Any]: 模型限制信息
        """
        pass
    
    async def execute_with_retry(self, 
                                request: AIRequest,
                                operation: Callable) -> AIResponse:
        """带重试机制的执行操作
        
        Args:
            request: AI请求对象
            operation: 要执行的操作
            
        Returns:
            AIResponse: AI响应对象
        """
        last_error = None
        retry_count = min(request.retry_count, self.max_retries)
        
        for attempt in range(retry_count + 1):
            try:
                start_time = time.time()
                
                # 执行操作
                response = await operation(request)
                
                # 计算延迟
                response.latency = time.time() - start_time
                
                # 更新统计信息
                self._update_usage_stats(response, success=True)
                
                return response
                
            except AIRateLimitError as e:
                last_error = e
                self.usage_stats.rate_limit_hits += 1
                
                if attempt < retry_count:
                    # 指数退避
                    delay = request.retry_delay * (2 ** attempt)
                    logger.warning(f"Rate limit hit, retrying in {delay}s (attempt {attempt + 1}/{retry_count + 1})")
                    await asyncio.sleep(delay)
                else:
                    break
                    
            except AIQuotaExceededError as e:
                last_error = e
                self.usage_stats.quota_exceeded_count += 1
                # 配额超限不重试
                break
                
            except (AINetworkError, AIServiceError) as e:
                last_error = e
                
                if attempt < retry_count:
                    delay = request.retry_delay * (1.5 ** attempt)
                    logger.warning(f"Request failed, retrying in {delay}s (attempt {attempt + 1}/{retry_count + 1}): {e}")
                    await asyncio.sleep(delay)
                else:
                    break
                    
            except Exception as e:
                last_error = AIServiceError(f"Unexpected error: {str(e)}")
                break
        
        # 所有重试都失败了
        error_response = AIResponse(
            status=self._get_error_status(last_error),
            content="",
            model=request.model,
            task_type=request.task_type,
            error_message=str(last_error),
            timestamp=datetime.now()
        )
        
        self._update_usage_stats(error_response, success=False)
        return error_response
    
    def _get_error_status(self, error: Exception) -> AIResponseStatus:
        """根据异常类型获取响应状态"""
        if isinstance(error, AIRateLimitError):
            return AIResponseStatus.RATE_LIMITED
        elif isinstance(error, AIQuotaExceededError):
            return AIResponseStatus.QUOTA_EXCEEDED
        elif isinstance(error, AIModelError):
            return AIResponseStatus.MODEL_ERROR
        elif isinstance(error, AINetworkError):
            return AIResponseStatus.NETWORK_ERROR
        else:
            return AIResponseStatus.FAILED
    
    def _update_usage_stats(self, response: AIResponse, success: bool):
        """更新使用统计信息"""
        self.usage_stats.total_requests += 1
        self.usage_stats.last_request_time = datetime.now()
        
        if success:
            self.usage_stats.successful_requests += 1
            
            # 更新token统计
            if 'prompt_tokens' in response.usage:
                self.usage_stats.prompt_tokens += response.usage['prompt_tokens']
            if 'completion_tokens' in response.usage:
                self.usage_stats.completion_tokens += response.usage['completion_tokens']
            if 'total_tokens' in response.usage:
                self.usage_stats.total_tokens += response.usage['total_tokens']
            
            # 更新成本统计
            self.usage_stats.total_cost += response.cost
            
            # 更新延迟统计
            if self.usage_stats.successful_requests == 1:
                self.usage_stats.average_latency = response.latency
            else:
                # 计算移动平均
                self.usage_stats.average_latency = (
                    (self.usage_stats.average_latency * (self.usage_stats.successful_requests - 1) + response.latency) /
                    self.usage_stats.successful_requests
                )
        else:
            self.usage_stats.failed_requests += 1
    
    def validate_request(self, request: AIRequest) -> bool:
        """验证请求参数
        
        Args:
            request: AI请求对象
            
        Returns:
            bool: 请求有效返回True，否则返回False
        """
        # 检查模型是否支持
        if request.model not in self.supported_models:
            raise AIServiceError(f"Model {request.model} not supported by {self.provider_name}")
        
        # 检查任务类型是否支持
        if request.task_type not in self.supported_tasks:
            raise AIServiceError(f"Task type {request.task_type} not supported by {self.provider_name}")
        
        # 检查消息格式
        if not request.messages:
            raise AIServiceError("Messages cannot be empty")
        
        # 检查模型限制
        limits = self.get_model_limits(request.model)
        
        if request.max_tokens and request.max_tokens > limits.get('max_tokens', float('inf')):
            raise AIServiceError(f"max_tokens {request.max_tokens} exceeds model limit {limits.get('max_tokens')}")
        
        return True
    
    def get_usage_stats(self) -> AIUsageStats:
        """获取使用统计信息
        
        Returns:
            AIUsageStats: 使用统计信息
        """
        return self.usage_stats
    
    def reset_usage_stats(self):
        """重置使用统计信息"""
        self.usage_stats = AIUsageStats()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        try:
            # 验证API密钥
            api_valid = await self.validate_api_key()
            
            return {
                'provider': self.provider_name,
                'status': 'healthy' if api_valid else 'unhealthy',
                'api_key_valid': api_valid,
                'supported_models': [model.value for model in self.supported_models],
                'supported_tasks': [task.value for task in self.supported_tasks],
                'usage_stats': {
                    'total_requests': self.usage_stats.total_requests,
                    'success_rate': (
                        self.usage_stats.successful_requests / self.usage_stats.total_requests
                        if self.usage_stats.total_requests > 0 else 0
                    ),
                    'average_latency': self.usage_stats.average_latency,
                    'total_cost': self.usage_stats.total_cost
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'provider': self.provider_name,
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

class AIAdapterManager:
    """AI适配器管理器
    
    管理多个AI服务适配器，提供统一的访问接口。
    """
    
    def __init__(self):
        self._adapters: Dict[str, BaseAIAdapter] = {}
        self._default_adapter: Optional[str] = None
    
    def register_adapter(self, name: str, adapter: BaseAIAdapter, set_as_default: bool = False):
        """注册AI适配器
        
        Args:
            name: 适配器名称
            adapter: AI适配器实例
            set_as_default: 是否设为默认适配器
        """
        self._adapters[name] = adapter
        
        if set_as_default or not self._default_adapter:
            self._default_adapter = name
        
        logger.info(f"Registered AI adapter: {name} ({adapter.provider_name})")
    
    def get_adapter(self, name: Optional[str] = None) -> BaseAIAdapter:
        """获取AI适配器
        
        Args:
            name: 适配器名称，如果不指定则返回默认适配器
            
        Returns:
            BaseAIAdapter: AI适配器实例
        """
        adapter_name = name or self._default_adapter
        
        if not adapter_name:
            raise AIServiceError("No adapter specified and no default adapter set")
        
        if adapter_name not in self._adapters:
            raise AIServiceError(f"Adapter '{adapter_name}' not found")
        
        return self._adapters[adapter_name]
    
    def list_adapters(self) -> List[str]:
        """列出所有已注册的适配器
        
        Returns:
            List[str]: 适配器名称列表
        """
        return list(self._adapters.keys())
    
    def get_supported_models(self, adapter_name: Optional[str] = None) -> List[AIModelType]:
        """获取支持的模型列表
        
        Args:
            adapter_name: 适配器名称
            
        Returns:
            List[AIModelType]: 支持的模型列表
        """
        adapter = self.get_adapter(adapter_name)
        return adapter.supported_models
    
    async def generate_text(self, request: AIRequest, adapter_name: Optional[str] = None) -> AIResponse:
        """生成文本
        
        Args:
            request: AI请求对象
            adapter_name: 适配器名称
            
        Returns:
            AIResponse: AI响应对象
        """
        adapter = self.get_adapter(adapter_name)
        return await adapter.generate_text(request)
    
    async def generate_text_stream(self, request: AIRequest, adapter_name: Optional[str] = None) -> AsyncGenerator[str, None]:
        """流式生成文本
        
        Args:
            request: AI请求对象
            adapter_name: 适配器名称
            
        Yields:
            str: 生成的文本片段
        """
        adapter = self.get_adapter(adapter_name)
        async for chunk in adapter.generate_text_stream(request):
            yield chunk
    
    async def health_check_all(self) -> Dict[str, Dict[str, Any]]:
        """检查所有适配器的健康状态
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有适配器的健康状态
        """
        results = {}
        
        for name, adapter in self._adapters.items():
            try:
                results[name] = await adapter.health_check()
            except Exception as e:
                results[name] = {
                    'provider': adapter.provider_name,
                    'status': 'error',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
        
        return results

# 全局适配器管理器实例
ai_manager = AIAdapterManager()

# 导出的类和函数
__all__ = [
    "AIModelType",
    "AITaskType",
    "AIResponseStatus",
    "AIMessage",
    "AIRequest",
    "AIResponse",
    "AIUsageStats",
    "AIServiceError",
    "AIRateLimitError",
    "AIQuotaExceededError",
    "AIModelError",
    "AINetworkError",
    "BaseAIAdapter",
    "AIAdapterManager",
    "ai_manager"
]