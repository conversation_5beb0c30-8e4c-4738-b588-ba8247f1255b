"""故事元素管理界面

提供角色、场景、事件等故事元素的管理界面，包括关系图谱可视化和批量编辑功能。
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Callable, Any, Set
import flet as ft
from datetime import datetime


class ElementType(Enum):
    """故事元素类型"""
    CHARACTER = "character"
    SCENE = "scene"
    EVENT = "event"
    ITEM = "item"
    ORGANIZATION = "organization"


class RelationType(Enum):
    """关系类型"""
    FRIEND = "friend"
    ENEMY = "enemy"
    FAMILY = "family"
    LOVER = "lover"
    COLLEAGUE = "colleague"
    MENTOR = "mentor"
    STUDENT = "student"
    BELONGS_TO = "belongs_to"
    LOCATED_IN = "located_in"
    PARTICIPATES_IN = "participates_in"
    OWNS = "owns"
    CUSTOM = "custom"


class ViewMode(Enum):
    """视图模式"""
    LIST = "list"
    GRID = "grid"
    GRAPH = "graph"
    TIMELINE = "timeline"


@dataclass
class StoryElement:
    """故事元素基类"""
    id: str
    name: str
    element_type: ElementType
    description: str = ""
    tags: List[str] = field(default_factory=list)
    properties: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    color: str = "#2196F3"
    image_path: Optional[str] = None


@dataclass
class Character(StoryElement):
    """角色元素"""
    age: Optional[int] = None
    gender: str = ""
    occupation: str = ""
    personality: List[str] = field(default_factory=list)
    background: str = ""
    goals: List[str] = field(default_factory=list)
    conflicts: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        self.element_type = ElementType.CHARACTER
        if not self.color:
            self.color = "#E91E63"


@dataclass
class Scene(StoryElement):
    """场景元素"""
    location: str = ""
    time_period: str = ""
    atmosphere: str = ""
    weather: str = ""
    significance: str = ""
    
    def __post_init__(self):
        self.element_type = ElementType.SCENE
        if not self.color:
            self.color = "#4CAF50"


@dataclass
class Event(StoryElement):
    """事件元素"""
    date: Optional[datetime] = None
    duration: str = ""
    participants: List[str] = field(default_factory=list)
    location: str = ""
    outcome: str = ""
    importance: int = 1  # 1-5
    
    def __post_init__(self):
        self.element_type = ElementType.EVENT
        if not self.color:
            self.color = "#FF9800"


@dataclass
class ElementRelation:
    """元素关系"""
    id: str
    from_element: str
    to_element: str
    relation_type: RelationType
    description: str = ""
    strength: int = 1  # 1-5
    is_bidirectional: bool = False
    custom_label: str = ""


class ElementCard(ft.UserControl):
    """故事元素卡片"""
    
    def __init__(
        self,
        element: StoryElement,
        on_click: Optional[Callable] = None,
        on_edit: Optional[Callable] = None,
        on_delete: Optional[Callable] = None,
        selected: bool = False
    ):
        super().__init__()
        self.element = element
        self.on_click = on_click
        self.on_edit = on_edit
        self.on_delete = on_delete
        self.selected = selected
    
    def build(self):
        # 元素图标
        icon_map = {
            ElementType.CHARACTER: ft.icons.PERSON,
            ElementType.SCENE: ft.icons.PLACE,
            ElementType.EVENT: ft.icons.EVENT,
            ElementType.ITEM: ft.icons.INVENTORY,
            ElementType.ORGANIZATION: ft.icons.BUSINESS
        }
        
        # 卡片内容
        content = ft.Column([
            ft.Row([
                ft.Icon(
                    icon_map.get(self.element.element_type, ft.icons.HELP),
                    color=self.element.color,
                    size=24
                ),
                ft.Text(
                    self.element.name,
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    expand=True
                ),
                ft.PopupMenuButton(
                    items=[
                        ft.PopupMenuItem(
                            text="编辑",
                            icon=ft.icons.EDIT,
                            on_click=lambda e: self._handle_edit()
                        ),
                        ft.PopupMenuItem(
                            text="删除",
                            icon=ft.icons.DELETE,
                            on_click=lambda e: self._handle_delete()
                        )
                    ]
                )
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            ft.Text(
                self.element.description[:100] + "..." if len(self.element.description) > 100 else self.element.description,
                size=12,
                color=ft.colors.ON_SURFACE_VARIANT,
                max_lines=3
            ),
            ft.Wrap([
                ft.Chip(
                    label=ft.Text(tag, size=10),
                    bgcolor=ft.colors.SURFACE_VARIANT,
                    height=24
                )
                for tag in self.element.tags[:3]
            ], spacing=4) if self.element.tags else ft.Container(height=0)
        ], spacing=8)
        
        return ft.Card(
            content=ft.Container(
                content=content,
                padding=12,
                on_click=self._handle_click
            ),
            elevation=4 if self.selected else 2,
            color=ft.colors.PRIMARY_CONTAINER if self.selected else None
        )
    
    def _handle_click(self, e):
        if self.on_click:
            self.on_click(self.element)
    
    def _handle_edit(self):
        if self.on_edit:
            self.on_edit(self.element)
    
    def _handle_delete(self):
        if self.on_delete:
            self.on_delete(self.element)


class ElementEditor(ft.UserControl):
    """故事元素编辑器"""
    
    def __init__(
        self,
        element_type: ElementType,
        element: Optional[StoryElement] = None,
        on_save: Optional[Callable] = None,
        on_cancel: Optional[Callable] = None
    ):
        super().__init__()
        self.element_type = element_type
        self.element = element
        self.on_save = on_save
        self.on_cancel = on_cancel
        self.is_editing = element is not None
        
        # 基础字段
        self.name_field = ft.TextField(
            label="名称",
            value=element.name if element else "",
            autofocus=True
        )
        self.description_field = ft.TextField(
            label="描述",
            value=element.description if element else "",
            multiline=True,
            min_lines=3,
            max_lines=5
        )
        self.tags_field = ft.TextField(
            label="标签（用逗号分隔）",
            value=", ".join(element.tags) if element else ""
        )
        self.color_field = ft.TextField(
            label="颜色",
            value=element.color if element else "#2196F3"
        )
        
        # 特定类型字段
        self.specific_fields = self._create_specific_fields()
    
    def build(self):
        controls = [
            ft.Text(
                f"{'编辑' if self.is_editing else '创建'}{self.element_type.value.title()}",
                size=20,
                weight=ft.FontWeight.BOLD
            ),
            ft.Divider(),
            self.name_field,
            self.description_field,
            self.tags_field,
            self.color_field
        ]
        
        # 添加特定字段
        if self.specific_fields:
            controls.extend(self.specific_fields)
        
        # 操作按钮
        controls.extend([
            ft.Divider(),
            ft.Row([
                ft.ElevatedButton(
                    "保存",
                    icon=ft.icons.SAVE,
                    on_click=self._handle_save
                ),
                ft.OutlinedButton(
                    "取消",
                    on_click=self._handle_cancel
                )
            ], spacing=8)
        ])
        
        return ft.Container(
            content=ft.Column(controls, spacing=12, scroll=ft.ScrollMode.AUTO),
            padding=16,
            width=400
        )
    
    def _create_specific_fields(self) -> List[ft.Control]:
        """创建特定类型的字段"""
        fields = []
        
        if self.element_type == ElementType.CHARACTER:
            char = self.element if isinstance(self.element, Character) else None
            
            self.age_field = ft.TextField(
                label="年龄",
                value=str(char.age) if char and char.age else "",
                keyboard_type=ft.KeyboardType.NUMBER
            )
            self.gender_field = ft.Dropdown(
                label="性别",
                options=[
                    ft.dropdown.Option("男", "男"),
                    ft.dropdown.Option("女", "女"),
                    ft.dropdown.Option("其他", "其他")
                ],
                value=char.gender if char else ""
            )
            self.occupation_field = ft.TextField(
                label="职业",
                value=char.occupation if char else ""
            )
            self.personality_field = ft.TextField(
                label="性格特点（用逗号分隔）",
                value=", ".join(char.personality) if char else ""
            )
            self.background_field = ft.TextField(
                label="背景故事",
                value=char.background if char else "",
                multiline=True,
                min_lines=2
            )
            
            fields.extend([
                self.age_field,
                self.gender_field,
                self.occupation_field,
                self.personality_field,
                self.background_field
            ])
            
        elif self.element_type == ElementType.SCENE:
            scene = self.element if isinstance(self.element, Scene) else None
            
            self.location_field = ft.TextField(
                label="地点",
                value=scene.location if scene else ""
            )
            self.time_period_field = ft.TextField(
                label="时间段",
                value=scene.time_period if scene else ""
            )
            self.atmosphere_field = ft.TextField(
                label="氛围",
                value=scene.atmosphere if scene else ""
            )
            self.weather_field = ft.TextField(
                label="天气",
                value=scene.weather if scene else ""
            )
            
            fields.extend([
                self.location_field,
                self.time_period_field,
                self.atmosphere_field,
                self.weather_field
            ])
            
        elif self.element_type == ElementType.EVENT:
            event = self.element if isinstance(self.element, Event) else None
            
            self.duration_field = ft.TextField(
                label="持续时间",
                value=event.duration if event else ""
            )
            self.participants_field = ft.TextField(
                label="参与者（用逗号分隔）",
                value=", ".join(event.participants) if event else ""
            )
            self.event_location_field = ft.TextField(
                label="发生地点",
                value=event.location if event else ""
            )
            self.outcome_field = ft.TextField(
                label="结果",
                value=event.outcome if event else "",
                multiline=True
            )
            self.importance_field = ft.Slider(
                min=1,
                max=5,
                value=event.importance if event else 1,
                label="重要性",
                divisions=4
            )
            
            fields.extend([
                self.duration_field,
                self.participants_field,
                self.event_location_field,
                self.outcome_field,
                ft.Text("重要性"),
                self.importance_field
            ])
        
        return fields
    
    def _handle_save(self, e):
        """处理保存"""
        if not self.name_field.value.strip():
            self.page.show_snack_bar(ft.SnackBar(content=ft.Text("名称不能为空")))
            return
        
        # 创建或更新元素
        element_data = {
            "id": self.element.id if self.element else f"{self.element_type.value}_{datetime.now().timestamp()}",
            "name": self.name_field.value.strip(),
            "description": self.description_field.value.strip(),
            "tags": [tag.strip() for tag in self.tags_field.value.split(",") if tag.strip()],
            "color": self.color_field.value.strip() or "#2196F3"
        }
        
        # 添加特定字段
        if self.element_type == ElementType.CHARACTER:
            element_data.update({
                "age": int(self.age_field.value) if self.age_field.value.isdigit() else None,
                "gender": self.gender_field.value or "",
                "occupation": self.occupation_field.value.strip(),
                "personality": [p.strip() for p in self.personality_field.value.split(",") if p.strip()],
                "background": self.background_field.value.strip()
            })
            element = Character(**element_data)
            
        elif self.element_type == ElementType.SCENE:
            element_data.update({
                "location": self.location_field.value.strip(),
                "time_period": self.time_period_field.value.strip(),
                "atmosphere": self.atmosphere_field.value.strip(),
                "weather": self.weather_field.value.strip()
            })
            element = Scene(**element_data)
            
        elif self.element_type == ElementType.EVENT:
            element_data.update({
                "duration": self.duration_field.value.strip(),
                "participants": [p.strip() for p in self.participants_field.value.split(",") if p.strip()],
                "location": self.event_location_field.value.strip(),
                "outcome": self.outcome_field.value.strip(),
                "importance": int(self.importance_field.value)
            })
            element = Event(**element_data)
        
        else:
            element = StoryElement(**element_data)
        
        if self.on_save:
            self.on_save(element)
    
    def _handle_cancel(self, e):
        if self.on_cancel:
            self.on_cancel()


class RelationshipGraph(ft.UserControl):
    """关系图谱组件"""
    
    def __init__(
        self,
        elements: List[StoryElement],
        relations: List[ElementRelation],
        on_element_click: Optional[Callable] = None,
        on_relation_click: Optional[Callable] = None
    ):
        super().__init__()
        self.elements = elements
        self.relations = relations
        self.on_element_click = on_element_click
        self.on_relation_click = on_relation_click
        self.selected_elements = set()
    
    def build(self):
        # 简化的图谱显示（实际项目中可以使用更复杂的图形库）
        return ft.Container(
            content=ft.Column([
                ft.Text("关系图谱", size=18, weight=ft.FontWeight.BOLD),
                ft.Text("图谱功能开发中...", color=ft.colors.ON_SURFACE_VARIANT),
                ft.Container(
                    content=ft.Text("这里将显示交互式关系图谱"),
                    height=400,
                    bgcolor=ft.colors.SURFACE_VARIANT,
                    border_radius=8,
                    alignment=ft.alignment.center
                )
            ], spacing=12),
            padding=16
        )


class StoryElementsPage(ft.UserControl):
    """故事元素管理页面"""
    
    def __init__(self):
        super().__init__()
        self.elements = []  # 存储所有元素
        self.relations = []  # 存储所有关系
        self.selected_elements = set()
        self.current_view = ViewMode.LIST
        self.current_filter = ElementType.CHARACTER
        self.search_query = ""
        
        # UI组件
        self.search_field = ft.TextField(
            hint_text="搜索故事元素...",
            prefix_icon=ft.icons.SEARCH,
            on_change=self._handle_search
        )
        
        self.filter_tabs = ft.Tabs(
            selected_index=0,
            on_change=self._handle_filter_change,
            tabs=[
                ft.Tab(text="角色", icon=ft.icons.PERSON),
                ft.Tab(text="场景", icon=ft.icons.PLACE),
                ft.Tab(text="事件", icon=ft.icons.EVENT),
                ft.Tab(text="物品", icon=ft.icons.INVENTORY),
                ft.Tab(text="组织", icon=ft.icons.BUSINESS)
            ]
        )
        
        self.view_buttons = ft.Row([
            ft.IconButton(
                ft.icons.LIST,
                tooltip="列表视图",
                selected=True,
                on_click=lambda e: self._change_view(ViewMode.LIST)
            ),
            ft.IconButton(
                ft.icons.GRID_VIEW,
                tooltip="网格视图",
                on_click=lambda e: self._change_view(ViewMode.GRID)
            ),
            ft.IconButton(
                ft.icons.ACCOUNT_TREE,
                tooltip="关系图谱",
                on_click=lambda e: self._change_view(ViewMode.GRAPH)
            ),
            ft.IconButton(
                ft.icons.TIMELINE,
                tooltip="时间线",
                on_click=lambda e: self._change_view(ViewMode.TIMELINE)
            )
        ], spacing=4)
        
        self.action_buttons = ft.Row([
            ft.ElevatedButton(
                "新建",
                icon=ft.icons.ADD,
                on_click=self._handle_create
            ),
            ft.OutlinedButton(
                "批量编辑",
                icon=ft.icons.EDIT_NOTE,
                on_click=self._handle_batch_edit,
                disabled=True
            ),
            ft.OutlinedButton(
                "导出",
                icon=ft.icons.DOWNLOAD,
                on_click=self._handle_export
            )
        ], spacing=8)
        
        self.content_area = ft.Container(
            content=ft.Text("暂无数据"),
            expand=True
        )
        
        # 编辑对话框
        self.editor_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("编辑元素"),
            content_padding=0
        )
    
    def build(self):
        return ft.Column([
            # 标题栏
            ft.Row([
                ft.Text("故事元素管理", size=24, weight=ft.FontWeight.BOLD),
                ft.Container(expand=True),
                self.action_buttons
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            
            # 搜索和过滤
            ft.Row([
                ft.Container(
                    content=self.search_field,
                    width=300
                ),
                ft.Container(expand=True),
                self.view_buttons
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            
            # 过滤标签
            self.filter_tabs,
            
            # 内容区域
            ft.Container(
                content=self.content_area,
                expand=True,
                border=ft.border.all(1, ft.colors.OUTLINE),
                border_radius=8,
                padding=16
            )
        ], spacing=16, expand=True)
    
    def _handle_search(self, e):
        """处理搜索"""
        self.search_query = e.control.value.lower()
        self._update_content()
    
    def _handle_filter_change(self, e):
        """处理过滤器变化"""
        filter_map = {
            0: ElementType.CHARACTER,
            1: ElementType.SCENE,
            2: ElementType.EVENT,
            3: ElementType.ITEM,
            4: ElementType.ORGANIZATION
        }
        self.current_filter = filter_map[e.control.selected_index]
        self._update_content()
    
    def _change_view(self, view_mode: ViewMode):
        """切换视图模式"""
        self.current_view = view_mode
        
        # 更新按钮状态
        for i, button in enumerate(self.view_buttons.controls):
            button.selected = (i == list(ViewMode).index(view_mode))
        
        self._update_content()
        self.update()
    
    def _update_content(self):
        """更新内容显示"""
        # 过滤元素
        filtered_elements = [
            elem for elem in self.elements
            if elem.element_type == self.current_filter and
            (not self.search_query or self.search_query in elem.name.lower() or self.search_query in elem.description.lower())
        ]
        
        # 根据视图模式显示内容
        if self.current_view == ViewMode.LIST:
            self.content_area.content = self._create_list_view(filtered_elements)
        elif self.current_view == ViewMode.GRID:
            self.content_area.content = self._create_grid_view(filtered_elements)
        elif self.current_view == ViewMode.GRAPH:
            self.content_area.content = RelationshipGraph(
                elements=filtered_elements,
                relations=self.relations,
                on_element_click=self._handle_element_click
            )
        elif self.current_view == ViewMode.TIMELINE:
            self.content_area.content = self._create_timeline_view(filtered_elements)
        
        # 更新批量编辑按钮状态
        self.action_buttons.controls[1].disabled = len(self.selected_elements) == 0
        
        self.update()
    
    def _create_list_view(self, elements: List[StoryElement]) -> ft.Control:
        """创建列表视图"""
        if not elements:
            return ft.Text("暂无数据", text_align=ft.TextAlign.CENTER)
        
        return ft.ListView([
            ElementCard(
                element=elem,
                on_click=self._handle_element_click,
                on_edit=self._handle_element_edit,
                on_delete=self._handle_element_delete,
                selected=elem.id in self.selected_elements
            )
            for elem in elements
        ], spacing=8)
    
    def _create_grid_view(self, elements: List[StoryElement]) -> ft.Control:
        """创建网格视图"""
        if not elements:
            return ft.Text("暂无数据", text_align=ft.TextAlign.CENTER)
        
        # 将元素分组为行
        rows = []
        for i in range(0, len(elements), 3):
            row_elements = elements[i:i+3]
            row = ft.Row([
                ft.Container(
                    content=ElementCard(
                        element=elem,
                        on_click=self._handle_element_click,
                        on_edit=self._handle_element_edit,
                        on_delete=self._handle_element_delete,
                        selected=elem.id in self.selected_elements
                    ),
                    width=200
                )
                for elem in row_elements
            ], spacing=8)
            rows.append(row)
        
        return ft.Column(rows, spacing=8, scroll=ft.ScrollMode.AUTO)
    
    def _create_timeline_view(self, elements: List[StoryElement]) -> ft.Control:
        """创建时间线视图"""
        # 过滤出有时间信息的事件
        events = [elem for elem in elements if isinstance(elem, Event) and elem.date]
        events.sort(key=lambda x: x.date)
        
        if not events:
            return ft.Text("暂无时间线数据", text_align=ft.TextAlign.CENTER)
        
        timeline_items = []
        for event in events:
            timeline_items.append(
                ft.ListTile(
                    leading=ft.Icon(ft.icons.EVENT, color=event.color),
                    title=ft.Text(event.name),
                    subtitle=ft.Text(f"{event.date.strftime('%Y-%m-%d')} - {event.description[:50]}..."),
                    on_click=lambda e, elem=event: self._handle_element_click(elem)
                )
            )
        
        return ft.ListView(timeline_items, spacing=4)
    
    def _handle_element_click(self, element: StoryElement):
        """处理元素点击"""
        if element.id in self.selected_elements:
            self.selected_elements.remove(element.id)
        else:
            self.selected_elements.add(element.id)
        
        self._update_content()
    
    def _handle_element_edit(self, element: StoryElement):
        """处理元素编辑"""
        editor = ElementEditor(
            element_type=element.element_type,
            element=element,
            on_save=self._handle_save_element,
            on_cancel=self._handle_cancel_edit
        )
        
        self.editor_dialog.content = editor
        self.editor_dialog.open = True
        self.page.dialog = self.editor_dialog
        self.page.update()
    
    def _handle_element_delete(self, element: StoryElement):
        """处理元素删除"""
        def confirm_delete(e):
            self.elements = [elem for elem in self.elements if elem.id != element.id]
            self.selected_elements.discard(element.id)
            self._update_content()
            self.page.dialog.open = False
            self.page.update()
        
        def cancel_delete(e):
            self.page.dialog.open = False
            self.page.update()
        
        confirm_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("确认删除"),
            content=ft.Text(f"确定要删除 '{element.name}' 吗？此操作不可撤销。"),
            actions=[
                ft.TextButton("取消", on_click=cancel_delete),
                ft.TextButton("删除", on_click=confirm_delete)
            ]
        )
        
        self.page.dialog = confirm_dialog
        confirm_dialog.open = True
        self.page.update()
    
    def _handle_create(self, e):
        """处理创建新元素"""
        editor = ElementEditor(
            element_type=self.current_filter,
            on_save=self._handle_save_element,
            on_cancel=self._handle_cancel_edit
        )
        
        self.editor_dialog.content = editor
        self.editor_dialog.open = True
        self.page.dialog = self.editor_dialog
        self.page.update()
    
    def _handle_save_element(self, element: StoryElement):
        """处理保存元素"""
        # 查找是否是编辑现有元素
        existing_index = next(
            (i for i, elem in enumerate(self.elements) if elem.id == element.id),
            None
        )
        
        if existing_index is not None:
            self.elements[existing_index] = element
        else:
            self.elements.append(element)
        
        self._update_content()
        self.editor_dialog.open = False
        self.page.update()
        
        self.page.show_snack_bar(
            ft.SnackBar(content=ft.Text(f"元素 '{element.name}' 已保存"))
        )
    
    def _handle_cancel_edit(self):
        """处理取消编辑"""
        self.editor_dialog.open = False
        self.page.update()
    
    def _handle_batch_edit(self, e):
        """处理批量编辑"""
        if not self.selected_elements:
            return
        
        # TODO: 实现批量编辑功能
        self.page.show_snack_bar(
            ft.SnackBar(content=ft.Text("批量编辑功能开发中..."))
        )
    
    def _handle_export(self, e):
        """处理导出"""
        # TODO: 实现导出功能
        self.page.show_snack_bar(
            ft.SnackBar(content=ft.Text("导出功能开发中..."))
        )
    
    def load_sample_data(self):
        """加载示例数据"""
        # 示例角色
        self.elements.extend([
            Character(
                id="char_1",
                name="李明",
                description="主角，一个年轻的程序员",
                age=25,
                gender="男",
                occupation="程序员",
                personality=["聪明", "内向", "坚持"],
                background="从小对计算机感兴趣，大学学习计算机科学",
                tags=["主角", "技术宅"]
            ),
            Character(
                id="char_2",
                name="王小雨",
                description="女主角，设计师",
                age=23,
                gender="女",
                occupation="UI设计师",
                personality=["创意", "开朗", "细心"],
                background="艺术学院毕业，热爱设计",
                tags=["女主", "设计师"]
            )
        ])
        
        # 示例场景
        self.elements.extend([
            Scene(
                id="scene_1",
                name="咖啡厅",
                description="市中心的一家温馨咖啡厅",
                location="市中心商业区",
                atmosphere="温馨、安静",
                weather="晴朗",
                tags=["约会地点", "日常"]
            ),
            Scene(
                id="scene_2",
                name="公司办公室",
                description="现代化的开放式办公空间",
                location="科技园区",
                atmosphere="忙碌、现代",
                tags=["工作场所"]
            )
        ])
        
        # 示例事件
        self.elements.extend([
            Event(
                id="event_1",
                name="初次相遇",
                description="李明和王小雨在咖啡厅初次相遇",
                date=datetime(2024, 1, 15),
                participants=["李明", "王小雨"],
                location="咖啡厅",
                importance=5,
                tags=["重要情节", "相遇"]
            )
        ])
        
        self._update_content()


# 工具函数
def create_story_elements_page() -> StoryElementsPage:
    """创建故事元素管理页面"""
    page = StoryElementsPage()
    page.load_sample_data()  # 加载示例数据
    return page


def create_element_quick_add(element_type: ElementType, on_add: Optional[Callable] = None) -> ft.Control:
    """创建快速添加元素组件"""
    name_field = ft.TextField(hint_text="名称", width=200)
    
    def handle_add(e):
        if name_field.value.strip():
            element = StoryElement(
                id=f"{element_type.value}_{datetime.now().timestamp()}",
                name=name_field.value.strip(),
                element_type=element_type
            )
            if on_add:
                on_add(element)
            name_field.value = ""
            name_field.update()
    
    return ft.Row([
        name_field,
        ft.IconButton(
            ft.icons.ADD,
            tooltip=f"添加{element_type.value}",
            on_click=handle_add
        )
    ], spacing=8)
