"""故事元素数据模型

定义小说创作中的核心故事元素，包括：
- 角色管理（Character）
- 场景管理（Scene）
- 事件管理（Event）
- 元素间的关联关系
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum

from sqlalchemy import Column, String, Text, Integer, Float, Boolean, ForeignKey, JSON, Table
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from pydantic import BaseModel, Field, validator

from .base import BaseTable, BaseSchema, BaseResponseSchema, BaseCreateSchema, BaseUpdateSchema

# 关联表定义
character_scene_association = Table(
    'character_scene_associations',
    BaseTable.metadata,
    Column('character_id', UUID(as_uuid=True), ForeignKey('characters.id'), primary_key=True),
    Column('scene_id', UUID(as_uuid=True), ForeignKey('scenes.id'), primary_key=True),
    Column('role_in_scene', String(100), comment='角色在场景中的作用'),
    Column('created_at', nullable=False, comment='关联创建时间')
)

character_event_association = Table(
    'character_event_associations',
    BaseTable.metadata,
    Column('character_id', UUID(as_uuid=True), ForeignKey('characters.id'), primary_key=True),
    Column('event_id', UUID(as_uuid=True), ForeignKey('events.id'), primary_key=True),
    Column('involvement_level', String(50), comment='参与程度'),
    Column('created_at', nullable=False, comment='关联创建时间')
)

scene_event_association = Table(
    'scene_event_associations',
    BaseTable.metadata,
    Column('scene_id', UUID(as_uuid=True), ForeignKey('scenes.id'), primary_key=True),
    Column('event_id', UUID(as_uuid=True), ForeignKey('events.id'), primary_key=True),
    Column('sequence_order', Integer, comment='事件在场景中的顺序'),
    Column('created_at', nullable=False, comment='关联创建时间')
)

class CharacterType(str, Enum):
    """角色类型枚举"""
    PROTAGONIST = "protagonist"      # 主角
    DEUTERAGONIST = "deuteragonist"  # 重要配角
    ANTAGONIST = "antagonist"        # 反派
    SUPPORTING = "supporting"        # 配角
    MINOR = "minor"                  # 次要角色
    CAMEO = "cameo"                  # 客串角色

class CharacterGender(str, Enum):
    """角色性别枚举"""
    MALE = "male"        # 男性
    FEMALE = "female"    # 女性
    OTHER = "other"      # 其他
    UNKNOWN = "unknown"  # 未知

class SceneType(str, Enum):
    """场景类型枚举"""
    INDOOR = "indoor"      # 室内
    OUTDOOR = "outdoor"    # 户外
    VIRTUAL = "virtual"    # 虚拟场景
    FANTASY = "fantasy"    # 幻想场景
    HISTORICAL = "historical"  # 历史场景

class EventType(str, Enum):
    """事件类型枚举"""
    PLOT = "plot"          # 情节事件
    DIALOGUE = "dialogue"  # 对话事件
    ACTION = "action"      # 动作事件
    CONFLICT = "conflict"  # 冲突事件
    RESOLUTION = "resolution"  # 解决事件
    FLASHBACK = "flashback"    # 回忆事件
    FORESHADOWING = "foreshadowing"  # 伏笔事件

class Character(BaseTable):
    """角色数据模型
    
    存储小说中的角色信息和设定。
    """
    __tablename__ = "characters"
    
    # 关联项目
    project_id = Column(
        UUID(as_uuid=True),
        ForeignKey("projects.id", ondelete="CASCADE"),
        nullable=False,
        comment="所属项目ID"
    )
    
    # 角色基本信息
    name = Column(
        String(100),
        nullable=False,
        comment="角色姓名"
    )
    
    nickname = Column(
        String(100),
        nullable=True,
        comment="角色昵称/别名"
    )
    
    # 角色类型
    character_type = Column(
        String(20),
        nullable=False,
        default=CharacterType.SUPPORTING.value,
        comment="角色类型"
    )
    
    # 基本属性
    age = Column(
        Integer,
        nullable=True,
        comment="年龄"
    )
    
    gender = Column(
        String(10),
        nullable=True,
        default=CharacterGender.UNKNOWN.value,
        comment="性别"
    )
    
    occupation = Column(
        String(100),
        nullable=True,
        comment="职业"
    )
    
    # 外貌描述
    appearance = Column(
        Text,
        nullable=True,
        comment="外貌描述"
    )
    
    # 性格描述
    personality = Column(
        Text,
        nullable=True,
        comment="性格描述"
    )
    
    # 背景故事
    background = Column(
        Text,
        nullable=True,
        comment="背景故事"
    )
    
    # 角色目标
    goals = Column(
        Text,
        nullable=True,
        comment="角色目标"
    )
    
    # 角色动机
    motivations = Column(
        Text,
        nullable=True,
        comment="角色动机"
    )
    
    # 角色弱点
    weaknesses = Column(
        Text,
        nullable=True,
        comment="角色弱点"
    )
    
    # 角色技能
    skills = Column(
        JSON,
        nullable=True,
        comment="角色技能（JSON数组）"
    )
    
    # 角色关系
    relationships = Column(
        JSON,
        nullable=True,
        comment="角色关系（JSON对象）"
    )
    
    # 角色头像
    avatar = Column(
        String(500),
        nullable=True,
        comment="角色头像路径"
    )
    
    # 重要程度（1-10）
    importance_level = Column(
        Integer,
        default=5,
        nullable=False,
        comment="重要程度（1-10）"
    )
    
    # 出场次数统计
    appearance_count = Column(
        Integer,
        default=0,
        nullable=False,
        comment="出场次数"
    )
    
    # 关联关系
    project = relationship("Project", back_populates="characters")
    scenes = relationship("Scene", secondary=character_scene_association, back_populates="characters")
    events = relationship("Event", secondary=character_event_association, back_populates="characters")
    
    def __repr__(self) -> str:
        return f"<Character(id={self.id}, name='{self.name}', type='{self.character_type}')>"

class Scene(BaseTable):
    """场景数据模型
    
    存储小说中的场景信息和设定。
    """
    __tablename__ = "scenes"
    
    # 关联项目
    project_id = Column(
        UUID(as_uuid=True),
        ForeignKey("projects.id", ondelete="CASCADE"),
        nullable=False,
        comment="所属项目ID"
    )
    
    # 场景基本信息
    name = Column(
        String(100),
        nullable=False,
        comment="场景名称"
    )
    
    # 场景类型
    scene_type = Column(
        String(20),
        nullable=False,
        default=SceneType.INDOOR.value,
        comment="场景类型"
    )
    
    # 地理位置
    location = Column(
        String(200),
        nullable=True,
        comment="地理位置"
    )
    
    # 场景描述
    description = Column(
        Text,
        nullable=True,
        comment="场景描述"
    )
    
    # 环境细节
    environment_details = Column(
        Text,
        nullable=True,
        comment="环境细节"
    )
    
    # 氛围描述
    atmosphere = Column(
        Text,
        nullable=True,
        comment="氛围描述"
    )
    
    # 时间设定
    time_period = Column(
        String(100),
        nullable=True,
        comment="时间设定"
    )
    
    # 天气条件
    weather = Column(
        String(50),
        nullable=True,
        comment="天气条件"
    )
    
    # 场景图片
    image = Column(
        String(500),
        nullable=True,
        comment="场景图片路径"
    )
    
    # 场景标签
    tags = Column(
        JSON,
        nullable=True,
        comment="场景标签（JSON数组）"
    )
    
    # 重要程度（1-10）
    importance_level = Column(
        Integer,
        default=5,
        nullable=False,
        comment="重要程度（1-10）"
    )
    
    # 使用次数统计
    usage_count = Column(
        Integer,
        default=0,
        nullable=False,
        comment="使用次数"
    )
    
    # 关联关系
    project = relationship("Project", back_populates="scenes")
    characters = relationship("Character", secondary=character_scene_association, back_populates="scenes")
    events = relationship("Event", secondary=scene_event_association, back_populates="scenes")
    
    def __repr__(self) -> str:
        return f"<Scene(id={self.id}, name='{self.name}', type='{self.scene_type}')>"

class Event(BaseTable):
    """事件数据模型
    
    存储小说中的事件信息和情节点。
    """
    __tablename__ = "events"
    
    # 关联项目
    project_id = Column(
        UUID(as_uuid=True),
        ForeignKey("projects.id", ondelete="CASCADE"),
        nullable=False,
        comment="所属项目ID"
    )
    
    # 关联章节（可选）
    chapter_id = Column(
        UUID(as_uuid=True),
        ForeignKey("chapters.id", ondelete="SET NULL"),
        nullable=True,
        comment="所属章节ID"
    )
    
    # 事件基本信息
    title = Column(
        String(200),
        nullable=False,
        comment="事件标题"
    )
    
    # 事件类型
    event_type = Column(
        String(20),
        nullable=False,
        default=EventType.PLOT.value,
        comment="事件类型"
    )
    
    # 事件描述
    description = Column(
        Text,
        nullable=True,
        comment="事件描述"
    )
    
    # 事件结果
    outcome = Column(
        Text,
        nullable=True,
        comment="事件结果"
    )
    
    # 事件影响
    impact = Column(
        Text,
        nullable=True,
        comment="事件影响"
    )
    
    # 时间顺序
    sequence_order = Column(
        Integer,
        default=0,
        nullable=False,
        comment="时间顺序"
    )
    
    # 持续时间（分钟）
    duration_minutes = Column(
        Integer,
        nullable=True,
        comment="持续时间（分钟）"
    )
    
    # 重要程度（1-10）
    importance_level = Column(
        Integer,
        default=5,
        nullable=False,
        comment="重要程度（1-10）"
    )
    
    # 情感强度（1-10）
    emotional_intensity = Column(
        Integer,
        default=5,
        nullable=False,
        comment="情感强度（1-10）"
    )
    
    # 冲突级别（1-10）
    conflict_level = Column(
        Integer,
        default=1,
        nullable=False,
        comment="冲突级别（1-10）"
    )
    
    # 事件标签
    tags = Column(
        JSON,
        nullable=True,
        comment="事件标签（JSON数组）"
    )
    
    # 前置事件
    prerequisite_events = Column(
        JSON,
        nullable=True,
        comment="前置事件ID列表（JSON数组）"
    )
    
    # 后续事件
    consequence_events = Column(
        JSON,
        nullable=True,
        comment="后续事件ID列表（JSON数组）"
    )
    
    # 关联关系
    project = relationship("Project", back_populates="events")
    chapter = relationship("Chapter", back_populates="events")
    characters = relationship("Character", secondary=character_event_association, back_populates="events")
    scenes = relationship("Scene", secondary=scene_event_association, back_populates="events")
    
    def __repr__(self) -> str:
        return f"<Event(id={self.id}, title='{self.title}', type='{self.event_type}')>"

# Pydantic模式定义

class CharacterBaseSchema(BaseSchema):
    """角色基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="角色姓名")
    nickname: Optional[str] = Field(None, max_length=100, description="角色昵称/别名")
    character_type: CharacterType = Field(CharacterType.SUPPORTING, description="角色类型")
    age: Optional[int] = Field(None, ge=0, le=200, description="年龄")
    gender: CharacterGender = Field(CharacterGender.UNKNOWN, description="性别")
    occupation: Optional[str] = Field(None, max_length=100, description="职业")
    appearance: Optional[str] = Field(None, description="外貌描述")
    personality: Optional[str] = Field(None, description="性格描述")
    background: Optional[str] = Field(None, description="背景故事")
    goals: Optional[str] = Field(None, description="角色目标")
    motivations: Optional[str] = Field(None, description="角色动机")
    weaknesses: Optional[str] = Field(None, description="角色弱点")
    skills: Optional[List[str]] = Field(None, description="角色技能")
    relationships: Optional[Dict[str, Any]] = Field(None, description="角色关系")
    avatar: Optional[str] = Field(None, description="角色头像路径")
    importance_level: int = Field(5, ge=1, le=10, description="重要程度（1-10）")

class CharacterCreateSchema(CharacterBaseSchema, BaseCreateSchema):
    """创建角色模式"""
    project_id: uuid.UUID = Field(..., description="所属项目ID")

class CharacterUpdateSchema(BaseUpdateSchema):
    """更新角色模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="角色姓名")
    nickname: Optional[str] = Field(None, max_length=100, description="角色昵称/别名")
    character_type: Optional[CharacterType] = Field(None, description="角色类型")
    age: Optional[int] = Field(None, ge=0, le=200, description="年龄")
    gender: Optional[CharacterGender] = Field(None, description="性别")
    occupation: Optional[str] = Field(None, max_length=100, description="职业")
    appearance: Optional[str] = Field(None, description="外貌描述")
    personality: Optional[str] = Field(None, description="性格描述")
    background: Optional[str] = Field(None, description="背景故事")
    goals: Optional[str] = Field(None, description="角色目标")
    motivations: Optional[str] = Field(None, description="角色动机")
    weaknesses: Optional[str] = Field(None, description="角色弱点")
    skills: Optional[List[str]] = Field(None, description="角色技能")
    relationships: Optional[Dict[str, Any]] = Field(None, description="角色关系")
    avatar: Optional[str] = Field(None, description="角色头像路径")
    importance_level: Optional[int] = Field(None, ge=1, le=10, description="重要程度（1-10）")

class CharacterResponseSchema(CharacterBaseSchema, BaseResponseSchema):
    """角色响应模式"""
    project_id: uuid.UUID = Field(..., description="所属项目ID")
    appearance_count: int = Field(0, description="出场次数")

class SceneBaseSchema(BaseSchema):
    """场景基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="场景名称")
    scene_type: SceneType = Field(SceneType.INDOOR, description="场景类型")
    location: Optional[str] = Field(None, max_length=200, description="地理位置")
    description: Optional[str] = Field(None, description="场景描述")
    environment_details: Optional[str] = Field(None, description="环境细节")
    atmosphere: Optional[str] = Field(None, description="氛围描述")
    time_period: Optional[str] = Field(None, max_length=100, description="时间设定")
    weather: Optional[str] = Field(None, max_length=50, description="天气条件")
    image: Optional[str] = Field(None, description="场景图片路径")
    tags: Optional[List[str]] = Field(None, description="场景标签")
    importance_level: int = Field(5, ge=1, le=10, description="重要程度（1-10）")

class SceneCreateSchema(SceneBaseSchema, BaseCreateSchema):
    """创建场景模式"""
    project_id: uuid.UUID = Field(..., description="所属项目ID")

class SceneUpdateSchema(BaseUpdateSchema):
    """更新场景模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="场景名称")
    scene_type: Optional[SceneType] = Field(None, description="场景类型")
    location: Optional[str] = Field(None, max_length=200, description="地理位置")
    description: Optional[str] = Field(None, description="场景描述")
    environment_details: Optional[str] = Field(None, description="环境细节")
    atmosphere: Optional[str] = Field(None, description="氛围描述")
    time_period: Optional[str] = Field(None, max_length=100, description="时间设定")
    weather: Optional[str] = Field(None, max_length=50, description="天气条件")
    image: Optional[str] = Field(None, description="场景图片路径")
    tags: Optional[List[str]] = Field(None, description="场景标签")
    importance_level: Optional[int] = Field(None, ge=1, le=10, description="重要程度（1-10）")

class SceneResponseSchema(SceneBaseSchema, BaseResponseSchema):
    """场景响应模式"""
    project_id: uuid.UUID = Field(..., description="所属项目ID")
    usage_count: int = Field(0, description="使用次数")

class EventBaseSchema(BaseSchema):
    """事件基础模式"""
    title: str = Field(..., min_length=1, max_length=200, description="事件标题")
    event_type: EventType = Field(EventType.PLOT, description="事件类型")
    description: Optional[str] = Field(None, description="事件描述")
    outcome: Optional[str] = Field(None, description="事件结果")
    impact: Optional[str] = Field(None, description="事件影响")
    sequence_order: int = Field(0, ge=0, description="时间顺序")
    duration_minutes: Optional[int] = Field(None, ge=0, description="持续时间（分钟）")
    importance_level: int = Field(5, ge=1, le=10, description="重要程度（1-10）")
    emotional_intensity: int = Field(5, ge=1, le=10, description="情感强度（1-10）")
    conflict_level: int = Field(1, ge=1, le=10, description="冲突级别（1-10）")
    tags: Optional[List[str]] = Field(None, description="事件标签")
    prerequisite_events: Optional[List[uuid.UUID]] = Field(None, description="前置事件ID列表")
    consequence_events: Optional[List[uuid.UUID]] = Field(None, description="后续事件ID列表")

class EventCreateSchema(EventBaseSchema, BaseCreateSchema):
    """创建事件模式"""
    project_id: uuid.UUID = Field(..., description="所属项目ID")
    chapter_id: Optional[uuid.UUID] = Field(None, description="所属章节ID")

class EventUpdateSchema(BaseUpdateSchema):
    """更新事件模式"""
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="事件标题")
    event_type: Optional[EventType] = Field(None, description="事件类型")
    description: Optional[str] = Field(None, description="事件描述")
    outcome: Optional[str] = Field(None, description="事件结果")
    impact: Optional[str] = Field(None, description="事件影响")
    sequence_order: Optional[int] = Field(None, ge=0, description="时间顺序")
    duration_minutes: Optional[int] = Field(None, ge=0, description="持续时间（分钟）")
    importance_level: Optional[int] = Field(None, ge=1, le=10, description="重要程度（1-10）")
    emotional_intensity: Optional[int] = Field(None, ge=1, le=10, description="情感强度（1-10）")
    conflict_level: Optional[int] = Field(None, ge=1, le=10, description="冲突级别（1-10）")
    tags: Optional[List[str]] = Field(None, description="事件标签")
    prerequisite_events: Optional[List[uuid.UUID]] = Field(None, description="前置事件ID列表")
    consequence_events: Optional[List[uuid.UUID]] = Field(None, description="后续事件ID列表")
    chapter_id: Optional[uuid.UUID] = Field(None, description="所属章节ID")

class EventResponseSchema(EventBaseSchema, BaseResponseSchema):
    """事件响应模式"""
    project_id: uuid.UUID = Field(..., description="所属项目ID")
    chapter_id: Optional[uuid.UUID] = Field(None, description="所属章节ID")

# 导出的类
__all__ = [
    "CharacterType",
    "CharacterGender",
    "SceneType",
    "EventType",
    "Character",
    "Scene",
    "Event",
    "CharacterBaseSchema",
    "CharacterCreateSchema",
    "CharacterUpdateSchema",
    "CharacterResponseSchema",
    "SceneBaseSchema",
    "SceneCreateSchema",
    "SceneUpdateSchema",
    "SceneResponseSchema",
    "EventBaseSchema",
    "EventCreateSchema",
    "EventUpdateSchema",
    "EventResponseSchema"
]