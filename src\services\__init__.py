"""服务层模块

提供业务逻辑服务，包括AI服务管理、用户管理、项目管理等。
"""

from .ai_service import AIService, AIServiceManager
from .content_service import (
    ContentService, ContentType, WritingStyle, OptimizationGoal,
    GenerationContext, GenerationRequest, OptimizationRequest,
    get_content_service
)
from .project_service import ProjectService, get_project_service
from .outline_service import (
    OutlineNodeType,
    OutlineNode,
    OutlineStructure,
    OutlineTemplate,
    OutlineGenerationRequest,
    OutlineService,
    get_outline_service
)
from .character_service import (
    CharacterRelationship,
    CharacterArc,
    DialogueStyle,
    CharacterGenerationRequest,
    CharacterConsistencyCheck,
    CharacterService,
    get_character_service
)
from .story_service import (
    ConflictType,
    PlotStructure,
    Timeline,
    ConflictDesign,
    SceneGenerationRequest,
    EventGenerationRequest,
    PlotAnalysis,
    StoryService,
    get_story_service
)
from .export_service import (
    ExportFormat,
    ExportScope,
    TemplateType,
    CloudProvider,
    ExportTemplate,
    ExportRequest,
    ExportResult,
    BatchExportRequest,
    CloudSyncConfig,
    ExportService,
    get_export_service
)

__all__ = [
    "AIService",
    "AIServiceManager",
    "get_ai_service",
    "ContentService",
    "ContentType",
    "WritingStyle",
    "OptimizationGoal",
    "GenerationContext",
    "GenerationRequest",
    "OptimizationRequest",
    "get_content_service",
    "ProjectService",
    "get_project_service",
    "OutlineNodeType",
    "OutlineNode",
    "OutlineStructure",
    "OutlineTemplate",
    "OutlineGenerationRequest",
    "OutlineService",
    "get_outline_service",
    "CharacterRelationship",
    "CharacterArc",
    "DialogueStyle",
    "CharacterGenerationRequest",
    "CharacterConsistencyCheck",
    "CharacterService",
    "get_character_service",
    "ConflictType",
    "PlotStructure",
    "Timeline",
    "ConflictDesign",
    "SceneGenerationRequest",
    "EventGenerationRequest",
    "PlotAnalysis",
    "StoryService",
    "get_story_service",
    "ExportFormat",
    "ExportScope",
    "TemplateType",
    "CloudProvider",
    "ExportTemplate",
    "ExportRequest",
    "ExportResult",
    "BatchExportRequest",
    "CloudSyncConfig",
    "ExportService",
    "get_export_service"
]