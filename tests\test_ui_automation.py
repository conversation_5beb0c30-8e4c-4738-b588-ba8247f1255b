"""UI自动化测试

使用Playwright测试用户界面功能和关键用户流程。
"""

import pytest
import asyncio
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
from playwright.sync_api import sync_playwright
import json
import tempfile
import os

# 测试配置
TEST_CONFIG = {
    "base_url": "http://localhost:8000",
    "timeout": 30000,  # 30秒
    "viewport": {"width": 1280, "height": 720},
    "headless": True,  # 在CI环境中使用无头模式
    "slow_mo": 100 if os.getenv("DEBUG") else 0  # 调试时减慢操作
}

# 测试数据
TEST_DATA = {
    "user": {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123"
    },
    "project": {
        "name": "自动化测试项目",
        "description": "这是一个用于UI自动化测试的项目",
        "author": "测试作者",
        "genre": "科幻"
    },
    "story": {
        "title": "测试故事",
        "synopsis": "这是一个测试故事的简介",
        "content": "这是测试故事的内容" * 100
    },
    "character": {
        "name": "测试角色",
        "description": "这是一个测试角色",
        "age": "25",
        "gender": "男",
        "occupation": "程序员"
    }
}


class UITestBase:
    """UI测试基类"""
    
    @pytest.fixture(scope="session")
    async def browser(self):
        """浏览器实例"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=TEST_CONFIG["headless"],
                slow_mo=TEST_CONFIG["slow_mo"]
            )
            yield browser
            await browser.close()
    
    @pytest.fixture
    async def context(self, browser):
        """浏览器上下文"""
        context = await browser.new_context(
            viewport=TEST_CONFIG["viewport"],
            ignore_https_errors=True
        )
        yield context
        await context.close()
    
    @pytest.fixture
    async def page(self, context):
        """页面实例"""
        page = await context.new_page()
        page.set_default_timeout(TEST_CONFIG["timeout"])
        yield page
        await page.close()
    
    async def wait_for_app_ready(self, page: Page):
        """等待应用准备就绪"""
        # 等待主要元素加载
        await page.wait_for_selector('[data-testid="app-container"]', timeout=10000)
        # 等待加载动画消失
        await page.wait_for_function(
            "document.querySelector('[data-testid="loading-spinner"]') === null",
            timeout=5000
        )
    
    async def take_screenshot(self, page: Page, name: str):
        """截图用于调试"""
        if os.getenv("DEBUG"):
            screenshot_dir = Path("test_screenshots")
            screenshot_dir.mkdir(exist_ok=True)
            await page.screenshot(path=screenshot_dir / f"{name}.png")


class TestApplicationStartup(UITestBase):
    """应用启动测试"""
    
    @pytest.mark.asyncio
    async def test_app_loads_successfully(self, page: Page):
        """测试应用成功加载"""
        # 访问主页
        await page.goto(TEST_CONFIG["base_url"])
        
        # 等待应用加载
        await self.wait_for_app_ready(page)
        
        # 验证页面标题
        title = await page.title()
        assert "BambooFall" in title or "小说创作" in title
        
        # 验证主要导航元素存在
        await page.wait_for_selector('[data-testid="main-nav"]')
        await page.wait_for_selector('[data-testid="sidebar"]')
        
        await self.take_screenshot(page, "app_startup")
    
    @pytest.mark.asyncio
    async def test_responsive_design(self, page: Page):
        """测试响应式设计"""
        await page.goto(TEST_CONFIG["base_url"])
        await self.wait_for_app_ready(page)
        
        # 测试不同屏幕尺寸
        screen_sizes = [
            {"width": 1920, "height": 1080, "name": "desktop"},
            {"width": 1024, "height": 768, "name": "tablet"},
            {"width": 375, "height": 667, "name": "mobile"}
        ]
        
        for size in screen_sizes:
            await page.set_viewport_size(size["width"], size["height"])
            await page.wait_for_timeout(500)  # 等待布局调整
            
            # 验证主要元素仍然可见
            main_nav = await page.query_selector('[data-testid="main-nav"]')
            assert main_nav is not None
            
            await self.take_screenshot(page, f"responsive_{size['name']}")
    
    @pytest.mark.asyncio
    async def test_error_handling(self, page: Page):
        """测试错误处理"""
        # 访问不存在的页面
        await page.goto(f"{TEST_CONFIG['base_url']}/nonexistent-page")
        
        # 验证错误页面显示
        error_element = await page.wait_for_selector(
            '[data-testid="error-page"], [data-testid="not-found"]',
            timeout=5000
        )
        assert error_element is not None
        
        # 验证返回首页链接
        home_link = await page.query_selector('[data-testid="back-to-home"]')
        if home_link:
            await home_link.click()
            await self.wait_for_app_ready(page)


class TestProjectManagement(UITestBase):
    """项目管理测试"""
    
    @pytest.mark.asyncio
    async def test_create_new_project(self, page: Page):
        """测试创建新项目"""
        await page.goto(TEST_CONFIG["base_url"])
        await self.wait_for_app_ready(page)
        
        # 点击创建项目按钮
        create_btn = await page.wait_for_selector('[data-testid="create-project-btn"]')
        await create_btn.click()
        
        # 等待创建项目对话框
        dialog = await page.wait_for_selector('[data-testid="create-project-dialog"]')
        assert dialog is not None
        
        # 填写项目信息
        await page.fill('[data-testid="project-name-input"]', TEST_DATA["project"]["name"])
        await page.fill('[data-testid="project-description-input"]', TEST_DATA["project"]["description"])
        await page.fill('[data-testid="project-author-input"]', TEST_DATA["project"]["author"])
        
        # 选择项目类型
        await page.select_option('[data-testid="project-type-select"]', "novel")
        
        # 提交表单
        submit_btn = await page.wait_for_selector('[data-testid="create-project-submit"]')
        await submit_btn.click()
        
        # 验证项目创建成功
        success_message = await page.wait_for_selector(
            '[data-testid="success-message"]',
            timeout=5000
        )
        assert success_message is not None
        
        # 验证跳转到项目页面
        await page.wait_for_url(f"{TEST_CONFIG['base_url']}/project/*")
        
        await self.take_screenshot(page, "project_created")
    
    @pytest.mark.asyncio
    async def test_project_list_view(self, page: Page):
        """测试项目列表视图"""
        await page.goto(f"{TEST_CONFIG['base_url']}/projects")
        await self.wait_for_app_ready(page)
        
        # 验证项目列表容器存在
        project_list = await page.wait_for_selector('[data-testid="project-list"]')
        assert project_list is not None
        
        # 验证搜索功能
        search_input = await page.query_selector('[data-testid="project-search"]')
        if search_input:
            await search_input.fill("测试")
            await page.wait_for_timeout(1000)  # 等待搜索结果
            
            # 验证搜索结果
            search_results = await page.query_selector_all('[data-testid="project-card"]')
            # 应该有搜索结果或显示无结果消息
            no_results = await page.query_selector('[data-testid="no-results"]')
            assert len(search_results) > 0 or no_results is not None
        
        # 测试排序功能
        sort_select = await page.query_selector('[data-testid="project-sort"]')
        if sort_select:
            await sort_select.select_option("name")
            await page.wait_for_timeout(500)
            
            await sort_select.select_option("created_at")
            await page.wait_for_timeout(500)
        
        await self.take_screenshot(page, "project_list")
    
    @pytest.mark.asyncio
    async def test_project_settings(self, page: Page):
        """测试项目设置"""
        # 假设已有项目，直接访问项目设置页面
        await page.goto(f"{TEST_CONFIG['base_url']}/project/test-project/settings")
        await self.wait_for_app_ready(page)
        
        # 验证设置页面元素
        settings_form = await page.wait_for_selector('[data-testid="project-settings-form"]')
        assert settings_form is not None
        
        # 测试修改项目信息
        name_input = await page.query_selector('[data-testid="project-name-input"]')
        if name_input:
            await name_input.clear()
            await name_input.fill("修改后的项目名")
        
        # 测试保存设置
        save_btn = await page.query_selector('[data-testid="save-settings-btn"]')
        if save_btn:
            await save_btn.click()
            
            # 验证保存成功消息
            success_msg = await page.wait_for_selector(
                '[data-testid="settings-saved"]',
                timeout=3000
            )
            assert success_msg is not None


class TestStoryEditor(UITestBase):
    """故事编辑器测试"""
    
    @pytest.mark.asyncio
    async def test_story_editor_basic_functionality(self, page: Page):
        """测试故事编辑器基本功能"""
        await page.goto(f"{TEST_CONFIG['base_url']}/editor")
        await self.wait_for_app_ready(page)
        
        # 验证编辑器加载
        editor = await page.wait_for_selector('[data-testid="story-editor"]')
        assert editor is not None
        
        # 测试文本输入
        editor_content = await page.wait_for_selector('[data-testid="editor-content"]')
        await editor_content.click()
        await page.keyboard.type(TEST_DATA["story"]["content"])
        
        # 验证字数统计
        word_count = await page.wait_for_selector('[data-testid="word-count"]')
        count_text = await word_count.text_content()
        assert "字" in count_text and int(count_text.split()[0]) > 0
        
        # 测试保存功能
        save_btn = await page.query_selector('[data-testid="save-btn"]')
        if save_btn:
            await save_btn.click()
            
            # 验证保存状态
            save_status = await page.wait_for_selector(
                '[data-testid="save-status"]',
                timeout=3000
            )
            status_text = await save_status.text_content()
            assert "已保存" in status_text or "保存成功" in status_text
        
        await self.take_screenshot(page, "story_editor")
    
    @pytest.mark.asyncio
    async def test_editor_formatting_tools(self, page: Page):
        """测试编辑器格式化工具"""
        await page.goto(f"{TEST_CONFIG['base_url']}/editor")
        await self.wait_for_app_ready(page)
        
        editor_content = await page.wait_for_selector('[data-testid="editor-content"]')
        await editor_content.click()
        
        # 输入测试文本
        test_text = "这是一段测试文本"
        await page.keyboard.type(test_text)
        
        # 选择文本
        await page.keyboard.press("Control+A")
        
        # 测试格式化按钮
        formatting_buttons = [
            "bold-btn", "italic-btn", "underline-btn",
            "align-left-btn", "align-center-btn", "align-right-btn"
        ]
        
        for btn_id in formatting_buttons:
            btn = await page.query_selector(f'[data-testid="{btn_id}"]')
            if btn:
                await btn.click()
                await page.wait_for_timeout(200)
        
        # 测试撤销/重做
        undo_btn = await page.query_selector('[data-testid="undo-btn"]')
        redo_btn = await page.query_selector('[data-testid="redo-btn"]')
        
        if undo_btn:
            await undo_btn.click()
            await page.wait_for_timeout(200)
        
        if redo_btn:
            await redo_btn.click()
            await page.wait_for_timeout(200)
    
    @pytest.mark.asyncio
    async def test_chapter_management(self, page: Page):
        """测试章节管理"""
        await page.goto(f"{TEST_CONFIG['base_url']}/story/test-story/chapters")
        await self.wait_for_app_ready(page)
        
        # 测试创建新章节
        create_chapter_btn = await page.query_selector('[data-testid="create-chapter-btn"]')
        if create_chapter_btn:
            await create_chapter_btn.click()
            
            # 填写章节信息
            chapter_dialog = await page.wait_for_selector('[data-testid="chapter-dialog"]')
            await page.fill('[data-testid="chapter-title-input"]', "第一章")
            await page.fill('[data-testid="chapter-content-input"]', "这是第一章的内容")
            
            # 提交
            submit_btn = await page.wait_for_selector('[data-testid="chapter-submit"]')
            await submit_btn.click()
            
            # 验证章节创建成功
            chapter_list = await page.wait_for_selector('[data-testid="chapter-list"]')
            chapters = await chapter_list.query_selector_all('[data-testid="chapter-item"]')
            assert len(chapters) > 0
        
        # 测试章节排序
        sort_btn = await page.query_selector('[data-testid="sort-chapters-btn"]')
        if sort_btn:
            await sort_btn.click()
            await page.wait_for_timeout(500)


class TestAIFeatures(UITestBase):
    """AI功能测试"""
    
    @pytest.mark.asyncio
    async def test_ai_writing_assistant(self, page: Page):
        """测试AI写作助手"""
        await page.goto(f"{TEST_CONFIG['base_url']}/editor")
        await self.wait_for_app_ready(page)
        
        # 打开AI助手面板
        ai_btn = await page.query_selector('[data-testid="ai-assistant-btn"]')
        if ai_btn:
            await ai_btn.click()
            
            # 验证AI面板打开
            ai_panel = await page.wait_for_selector('[data-testid="ai-panel"]')
            assert ai_panel is not None
            
            # 测试AI功能按钮
            ai_functions = [
                "continue-writing", "improve-text", "generate-outline",
                "character-development", "scene-description"
            ]
            
            for func in ai_functions:
                func_btn = await page.query_selector(f'[data-testid="ai-{func}-btn"]')
                if func_btn:
                    await func_btn.click()
                    await page.wait_for_timeout(200)
                    
                    # 验证功能面板打开
                    func_panel = await page.query_selector(f'[data-testid="{func}-panel"]')
                    if func_panel:
                        # 输入提示
                        prompt_input = await page.query_selector('[data-testid="ai-prompt-input"]')
                        if prompt_input:
                            await prompt_input.fill("请帮我生成一些内容")
                        
                        # 点击生成按钮
                        generate_btn = await page.query_selector('[data-testid="ai-generate-btn"]')
                        if generate_btn:
                            await generate_btn.click()
                            
                            # 验证加载状态
                            loading = await page.wait_for_selector(
                                '[data-testid="ai-loading"]',
                                timeout=2000
                            )
                            assert loading is not None
            
            await self.take_screenshot(page, "ai_assistant")
    
    @pytest.mark.asyncio
    async def test_ai_settings_configuration(self, page: Page):
        """测试AI设置配置"""
        await page.goto(f"{TEST_CONFIG['base_url']}/settings/ai")
        await self.wait_for_app_ready(page)
        
        # 验证AI设置页面
        ai_settings = await page.wait_for_selector('[data-testid="ai-settings"]')
        assert ai_settings is not None
        
        # 测试API密钥设置
        api_key_input = await page.query_selector('[data-testid="api-key-input"]')
        if api_key_input:
            await api_key_input.fill("test-api-key-12345")
        
        # 测试模型选择
        model_select = await page.query_selector('[data-testid="ai-model-select"]')
        if model_select:
            await model_select.select_option("gpt-3.5-turbo")
        
        # 测试参数调整
        temperature_slider = await page.query_selector('[data-testid="temperature-slider"]')
        if temperature_slider:
            await temperature_slider.fill("0.7")
        
        # 保存设置
        save_btn = await page.query_selector('[data-testid="save-ai-settings"]')
        if save_btn:
            await save_btn.click()
            
            # 验证保存成功
            success_msg = await page.wait_for_selector(
                '[data-testid="ai-settings-saved"]',
                timeout=3000
            )
            assert success_msg is not None


class TestCharacterManagement(UITestBase):
    """角色管理测试"""
    
    @pytest.mark.asyncio
    async def test_create_character(self, page: Page):
        """测试创建角色"""
        await page.goto(f"{TEST_CONFIG['base_url']}/story/test-story/characters")
        await self.wait_for_app_ready(page)
        
        # 点击创建角色按钮
        create_btn = await page.query_selector('[data-testid="create-character-btn"]')
        if create_btn:
            await create_btn.click()
            
            # 填写角色信息
            character_dialog = await page.wait_for_selector('[data-testid="character-dialog"]')
            
            await page.fill('[data-testid="character-name-input"]', TEST_DATA["character"]["name"])
            await page.fill('[data-testid="character-description-input"]', TEST_DATA["character"]["description"])
            await page.fill('[data-testid="character-age-input"]', TEST_DATA["character"]["age"])
            await page.select_option('[data-testid="character-gender-select"]', TEST_DATA["character"]["gender"])
            await page.fill('[data-testid="character-occupation-input"]', TEST_DATA["character"]["occupation"])
            
            # 提交表单
            submit_btn = await page.wait_for_selector('[data-testid="character-submit"]')
            await submit_btn.click()
            
            # 验证角色创建成功
            character_list = await page.wait_for_selector('[data-testid="character-list"]')
            characters = await character_list.query_selector_all('[data-testid="character-card"]')
            assert len(characters) > 0
            
            await self.take_screenshot(page, "character_created")
    
    @pytest.mark.asyncio
    async def test_character_relationship_graph(self, page: Page):
        """测试角色关系图"""
        await page.goto(f"{TEST_CONFIG['base_url']}/story/test-story/relationships")
        await self.wait_for_app_ready(page)
        
        # 验证关系图容器
        relationship_graph = await page.wait_for_selector('[data-testid="relationship-graph"]')
        assert relationship_graph is not None
        
        # 测试添加关系
        add_relation_btn = await page.query_selector('[data-testid="add-relationship-btn"]')
        if add_relation_btn:
            await add_relation_btn.click()
            
            # 填写关系信息
            relation_dialog = await page.wait_for_selector('[data-testid="relationship-dialog"]')
            
            # 选择角色
            await page.select_option('[data-testid="character1-select"]', "character1")
            await page.select_option('[data-testid="character2-select"]', "character2")
            await page.select_option('[data-testid="relationship-type-select"]', "friend")
            
            # 提交
            submit_btn = await page.wait_for_selector('[data-testid="relationship-submit"]')
            await submit_btn.click()
        
        # 测试图形交互
        graph_nodes = await page.query_selector_all('[data-testid="graph-node"]')
        if graph_nodes:
            # 点击节点
            await graph_nodes[0].click()
            
            # 验证节点详情显示
            node_details = await page.query_selector('[data-testid="node-details"]')
            assert node_details is not None


class TestExportFeatures(UITestBase):
    """导出功能测试"""
    
    @pytest.mark.asyncio
    async def test_export_story_to_different_formats(self, page: Page):
        """测试导出故事为不同格式"""
        await page.goto(f"{TEST_CONFIG['base_url']}/story/test-story/export")
        await self.wait_for_app_ready(page)
        
        # 验证导出页面
        export_panel = await page.wait_for_selector('[data-testid="export-panel"]')
        assert export_panel is not None
        
        # 测试不同导出格式
        export_formats = ["docx", "pdf", "html", "txt"]
        
        for format_type in export_formats:
            # 选择格式
            format_radio = await page.query_selector(f'[data-testid="export-{format_type}"]')
            if format_radio:
                await format_radio.click()
                
                # 配置导出选项
                if format_type in ["docx", "pdf"]:
                    # 测试高级选项
                    advanced_btn = await page.query_selector('[data-testid="advanced-options"]')
                    if advanced_btn:
                        await advanced_btn.click()
                        
                        # 设置字体和样式
                        font_select = await page.query_selector('[data-testid="font-family-select"]')
                        if font_select:
                            await font_select.select_option("宋体")
                        
                        font_size_input = await page.query_selector('[data-testid="font-size-input"]')
                        if font_size_input:
                            await font_size_input.fill("12")
                
                # 开始导出
                export_btn = await page.query_selector('[data-testid="start-export-btn"]')
                if export_btn:
                    await export_btn.click()
                    
                    # 验证导出进度
                    progress_bar = await page.wait_for_selector(
                        '[data-testid="export-progress"]',
                        timeout=5000
                    )
                    assert progress_bar is not None
                    
                    # 等待导出完成
                    download_link = await page.wait_for_selector(
                        '[data-testid="download-link"]',
                        timeout=10000
                    )
                    assert download_link is not None
        
        await self.take_screenshot(page, "export_options")


class TestSearchAndFilter(UITestBase):
    """搜索和过滤测试"""
    
    @pytest.mark.asyncio
    async def test_global_search(self, page: Page):
        """测试全局搜索"""
        await page.goto(TEST_CONFIG["base_url"])
        await self.wait_for_app_ready(page)
        
        # 打开搜索
        search_btn = await page.query_selector('[data-testid="global-search-btn"]')
        if search_btn:
            await search_btn.click()
        else:
            # 使用快捷键
            await page.keyboard.press("Control+K")
        
        # 验证搜索框出现
        search_input = await page.wait_for_selector('[data-testid="search-input"]')
        assert search_input is not None
        
        # 输入搜索关键词
        await search_input.fill("测试")
        await page.wait_for_timeout(1000)  # 等待搜索结果
        
        # 验证搜索结果
        search_results = await page.query_selector('[data-testid="search-results"]')
        assert search_results is not None
        
        # 测试搜索结果点击
        result_items = await search_results.query_selector_all('[data-testid="search-result-item"]')
        if result_items:
            await result_items[0].click()
            # 验证跳转到相应页面
            await page.wait_for_timeout(1000)
    
    @pytest.mark.asyncio
    async def test_content_filtering(self, page: Page):
        """测试内容过滤"""
        await page.goto(f"{TEST_CONFIG['base_url']}/projects")
        await self.wait_for_app_ready(page)
        
        # 测试类型过滤
        type_filter = await page.query_selector('[data-testid="type-filter"]')
        if type_filter:
            await type_filter.select_option("novel")
            await page.wait_for_timeout(500)
            
            # 验证过滤结果
            filtered_items = await page.query_selector_all('[data-testid="project-card"]')
            # 应该只显示小说类型的项目
        
        # 测试状态过滤
        status_filter = await page.query_selector('[data-testid="status-filter"]')
        if status_filter:
            await status_filter.select_option("active")
            await page.wait_for_timeout(500)
        
        # 测试日期范围过滤
        date_filter = await page.query_selector('[data-testid="date-filter"]')
        if date_filter:
            await date_filter.click()
            
            # 选择日期范围
            start_date = await page.query_selector('[data-testid="start-date"]')
            end_date = await page.query_selector('[data-testid="end-date"]')
            
            if start_date and end_date:
                await start_date.fill("2024-01-01")
                await end_date.fill("2024-12-31")
                
                apply_btn = await page.query_selector('[data-testid="apply-date-filter"]')
                if apply_btn:
                    await apply_btn.click()


class TestPerformanceAndAccessibility(UITestBase):
    """性能和可访问性测试"""
    
    @pytest.mark.asyncio
    async def test_page_load_performance(self, page: Page):
        """测试页面加载性能"""
        # 开始性能监控
        await page.goto("about:blank")
        
        # 记录开始时间
        start_time = time.time()
        
        # 访问主页
        await page.goto(TEST_CONFIG["base_url"])
        
        # 等待页面完全加载
        await page.wait_for_load_state("networkidle")
        await self.wait_for_app_ready(page)
        
        # 计算加载时间
        load_time = time.time() - start_time
        
        # 验证性能指标
        assert load_time < 5.0, f"页面加载时间过长: {load_time:.2f}秒"
        
        # 获取性能指标
        performance_metrics = await page.evaluate("""
            () => {
                const navigation = performance.getEntriesByType('navigation')[0];
                return {
                    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                    loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                    firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
                    firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
                };
            }
        """)
        
        print(f"\n性能指标:")
        print(f"DOM内容加载: {performance_metrics['domContentLoaded']:.2f}ms")
        print(f"页面完全加载: {performance_metrics['loadComplete']:.2f}ms")
        print(f"首次绘制: {performance_metrics['firstPaint']:.2f}ms")
        print(f"首次内容绘制: {performance_metrics['firstContentfulPaint']:.2f}ms")
        
        # 验证关键性能指标
        assert performance_metrics['firstContentfulPaint'] < 3000, "首次内容绘制时间过长"
    
    @pytest.mark.asyncio
    async def test_keyboard_navigation(self, page: Page):
        """测试键盘导航"""
        await page.goto(TEST_CONFIG["base_url"])
        await self.wait_for_app_ready(page)
        
        # 测试Tab键导航
        focusable_elements = []
        
        # 连续按Tab键，记录焦点元素
        for i in range(10):
            await page.keyboard.press("Tab")
            focused_element = await page.evaluate("document.activeElement.tagName")
            focusable_elements.append(focused_element)
            await page.wait_for_timeout(100)
        
        # 验证有可聚焦元素
        assert len(set(focusable_elements)) > 1, "没有足够的可聚焦元素"
        
        # 测试快捷键
        shortcuts = [
            {"key": "Control+N", "action": "新建项目"},
            {"key": "Control+S", "action": "保存"},
            {"key": "Control+K", "action": "搜索"},
            {"key": "Escape", "action": "关闭对话框"}
        ]
        
        for shortcut in shortcuts:
            await page.keyboard.press(shortcut["key"])
            await page.wait_for_timeout(500)
            # 这里可以添加具体的验证逻辑
    
    @pytest.mark.asyncio
    async def test_accessibility_features(self, page: Page):
        """测试可访问性功能"""
        await page.goto(TEST_CONFIG["base_url"])
        await self.wait_for_app_ready(page)
        
        # 检查页面是否有适当的语义标签
        semantic_elements = await page.evaluate("""
            () => {
                const elements = {
                    main: document.querySelector('main'),
                    nav: document.querySelector('nav'),
                    header: document.querySelector('header'),
                    footer: document.querySelector('footer'),
                    h1: document.querySelector('h1')
                };
                return Object.keys(elements).filter(key => elements[key] !== null);
            }
        """)
        
        assert len(semantic_elements) > 0, "页面缺少语义化标签"
        
        # 检查图片是否有alt属性
        images_without_alt = await page.evaluate("""
            () => {
                const images = Array.from(document.querySelectorAll('img'));
                return images.filter(img => !img.alt).length;
            }
        """)
        
        assert images_without_alt == 0, f"有{images_without_alt}个图片缺少alt属性"
        
        # 检查表单元素是否有标签
        unlabeled_inputs = await page.evaluate("""
            () => {
                const inputs = Array.from(document.querySelectorAll('input, textarea, select'));
                return inputs.filter(input => {
                    const id = input.id;
                    const label = document.querySelector(`label[for="${id}"]`);
                    const ariaLabel = input.getAttribute('aria-label');
                    const ariaLabelledby = input.getAttribute('aria-labelledby');
                    return !label && !ariaLabel && !ariaLabelledby;
                }).length;
            }
        """)
        
        # 允许少量未标记的输入（如搜索框等）
        assert unlabeled_inputs <= 2, f"有{unlabeled_inputs}个表单元素缺少标签"


class TestMobileResponsiveness(UITestBase):
    """移动端响应式测试"""
    
    @pytest.mark.asyncio
    async def test_mobile_navigation(self, page: Page):
        """测试移动端导航"""
        # 设置移动端视口
        await page.set_viewport_size(375, 667)
        await page.goto(TEST_CONFIG["base_url"])
        await self.wait_for_app_ready(page)
        
        # 查找移动端菜单按钮
        mobile_menu_btn = await page.query_selector('[data-testid="mobile-menu-btn"]')
        if mobile_menu_btn:
            await mobile_menu_btn.click()
            
            # 验证移动端菜单打开
            mobile_menu = await page.wait_for_selector('[data-testid="mobile-menu"]')
            assert mobile_menu is not None
            
            # 测试菜单项点击
            menu_items = await mobile_menu.query_selector_all('[data-testid="menu-item"]')
            if menu_items:
                await menu_items[0].click()
                await page.wait_for_timeout(500)
        
        await self.take_screenshot(page, "mobile_navigation")
    
    @pytest.mark.asyncio
    async def test_touch_interactions(self, page: Page):
        """测试触摸交互"""
        await page.set_viewport_size(375, 667)
        await page.goto(f"{TEST_CONFIG['base_url']}/editor")
        await self.wait_for_app_ready(page)
        
        # 模拟触摸滚动
        await page.evaluate("""
            () => {
                const editor = document.querySelector('[data-testid="editor-content"]');
                if (editor) {
                    editor.scrollTop = 100;
                }
            }
        """)
        
        # 测试长按菜单（如果有的话）
        editor_content = await page.query_selector('[data-testid="editor-content"]')
        if editor_content:
            # 模拟长按
            await editor_content.click(button="right")
            await page.wait_for_timeout(500)
            
            # 查找上下文菜单
            context_menu = await page.query_selector('[data-testid="context-menu"]')
            if context_menu:
                # 点击菜单外部关闭
                await page.click("body")


if __name__ == "__main__":
    # 运行测试
    pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "--html=test_report.html",
        "--self-contained-html",
        "--durations=10"
    ])