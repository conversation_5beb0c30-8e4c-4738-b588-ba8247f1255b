"""UI基础组件和主题系统

提供完整的UI开发基础，包括：
- 基础组件类和常用UI组件
- 主题管理和样式系统
- 响应式布局支持
- 组件复用机制
"""

import flet as ft
from typing import Optional, Dict, Any, List, Callable, Union
from abc import ABC, abstractmethod
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime

# 主题相关枚举和数据类

class ThemeMode(str, Enum):
    """主题模式枚举"""
    LIGHT = "light"
    DARK = "dark"
    SYSTEM = "system"

class ComponentSize(str, Enum):
    """组件尺寸枚举"""
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"
    EXTRA_LARGE = "xl"

class ComponentVariant(str, Enum):
    """组件变体枚举"""
    PRIMARY = "primary"
    SECONDARY = "secondary"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    INFO = "info"

@dataclass
class ColorPalette:
    """颜色调色板"""
    primary: str = "#2196F3"
    secondary: str = "#FF9800"
    success: str = "#4CAF50"
    warning: str = "#FF5722"
    error: str = "#F44336"
    info: str = "#00BCD4"
    background: str = "#FFFFFF"
    surface: str = "#F5F5F5"
    text_primary: str = "#212121"
    text_secondary: str = "#757575"
    border: str = "#E0E0E0"
    shadow: str = "rgba(0,0,0,0.1)"

@dataclass
class Typography:
    """字体排版配置"""
    font_family: str = "Microsoft YaHei, Arial, sans-serif"
    font_size_xs: int = 12
    font_size_sm: int = 14
    font_size_md: int = 16
    font_size_lg: int = 18
    font_size_xl: int = 20
    font_size_2xl: int = 24
    font_weight_normal: int = 400
    font_weight_medium: int = 500
    font_weight_bold: int = 700
    line_height: float = 1.5

@dataclass
class Spacing:
    """间距配置"""
    xs: int = 4
    sm: int = 8
    md: int = 16
    lg: int = 24
    xl: int = 32
    xxl: int = 48

@dataclass
class BorderRadius:
    """圆角配置"""
    none: int = 0
    sm: int = 4
    md: int = 8
    lg: int = 12
    xl: int = 16
    full: int = 9999

@dataclass
class Theme:
    """主题配置"""
    name: str
    mode: ThemeMode
    colors: ColorPalette = field(default_factory=ColorPalette)
    typography: Typography = field(default_factory=Typography)
    spacing: Spacing = field(default_factory=Spacing)
    border_radius: BorderRadius = field(default_factory=BorderRadius)
    
    def get_color(self, variant: ComponentVariant) -> str:
        """根据变体获取颜色"""
        color_map = {
            ComponentVariant.PRIMARY: self.colors.primary,
            ComponentVariant.SECONDARY: self.colors.secondary,
            ComponentVariant.SUCCESS: self.colors.success,
            ComponentVariant.WARNING: self.colors.warning,
            ComponentVariant.ERROR: self.colors.error,
            ComponentVariant.INFO: self.colors.info,
        }
        return color_map.get(variant, self.colors.primary)
    
    def get_font_size(self, size: ComponentSize) -> int:
        """根据尺寸获取字体大小"""
        size_map = {
            ComponentSize.SMALL: self.typography.font_size_sm,
            ComponentSize.MEDIUM: self.typography.font_size_md,
            ComponentSize.LARGE: self.typography.font_size_lg,
            ComponentSize.EXTRA_LARGE: self.typography.font_size_xl,
        }
        return size_map.get(size, self.typography.font_size_md)

class ThemeManager:
    """主题管理器"""
    
    def __init__(self):
        self._themes: Dict[str, Theme] = {}
        self._current_theme: Optional[Theme] = None
        self._theme_change_callbacks: List[Callable[[Theme], None]] = []
        
        # 注册默认主题
        self._register_default_themes()
    
    def _register_default_themes(self):
        """注册默认主题"""
        # 浅色主题
        light_theme = Theme(
            name="light",
            mode=ThemeMode.LIGHT,
            colors=ColorPalette(
                primary="#2196F3",
                secondary="#FF9800",
                success="#4CAF50",
                warning="#FF5722",
                error="#F44336",
                info="#00BCD4",
                background="#FFFFFF",
                surface="#F5F5F5",
                text_primary="#212121",
                text_secondary="#757575",
                border="#E0E0E0",
                shadow="rgba(0,0,0,0.1)"
            )
        )
        
        # 深色主题
        dark_colors = ColorPalette(
            primary="#1976D2",
            secondary="#F57C00",
            success="#388E3C",
            warning="#D84315",
            error="#C62828",
            info="#0097A7",
            background="#121212",
            surface="#1E1E1E",
            text_primary="#FFFFFF",
            text_secondary="#B0B0B0",
            border="#333333",
            shadow="rgba(0,0,0,0.3)"
        )
        
        dark_theme = Theme(
            name="dark",
            mode=ThemeMode.DARK,
            colors=dark_colors
        )
        
        self.register_theme(light_theme)
        self.register_theme(dark_theme)
        
        # 设置默认主题
        self.set_theme("light")
    
    def register_theme(self, theme: Theme):
        """注册主题"""
        self._themes[theme.name] = theme
    
    def get_theme(self, name: str) -> Optional[Theme]:
        """获取主题"""
        return self._themes.get(name)
    
    def get_current_theme(self) -> Optional[Theme]:
        """获取当前主题"""
        return self._current_theme
    
    def set_theme(self, name: str) -> bool:
        """设置当前主题"""
        theme = self.get_theme(name)
        if theme:
            self._current_theme = theme
            # 通知主题变更
            for callback in self._theme_change_callbacks:
                callback(theme)
            return True
        return False
    
    def add_theme_change_callback(self, callback: Callable[[Theme], None]):
        """添加主题变更回调"""
        self._theme_change_callbacks.append(callback)
    
    def remove_theme_change_callback(self, callback: Callable[[Theme], None]):
        """移除主题变更回调"""
        if callback in self._theme_change_callbacks:
            self._theme_change_callbacks.remove(callback)
    
    def list_themes(self) -> List[str]:
        """列出所有主题名称"""
        return list(self._themes.keys())

# 全局主题管理器实例
theme_manager = ThemeManager()

class BaseComponent(ABC):
    """基础组件抽象类"""
    
    def __init__(
        self,
        key: Optional[str] = None,
        visible: bool = True,
        disabled: bool = False,
        tooltip: Optional[str] = None,
        **kwargs
    ):
        self.key = key or f"component_{id(self)}"
        self.visible = visible
        self.disabled = disabled
        self.tooltip = tooltip
        self.theme = theme_manager.get_current_theme()
        self._control: Optional[ft.Control] = None
        
        # 注册主题变更回调
        theme_manager.add_theme_change_callback(self._on_theme_changed)
    
    def _on_theme_changed(self, theme: Theme):
        """主题变更回调"""
        self.theme = theme
        self._update_theme_styles()
    
    @abstractmethod
    def _update_theme_styles(self):
        """更新主题样式"""
        pass
    
    @abstractmethod
    def build(self) -> ft.Control:
        """构建组件"""
        pass
    
    def get_control(self) -> ft.Control:
        """获取Flet控件"""
        if self._control is None:
            self._control = self.build()
        return self._control
    
    def update(self):
        """更新组件"""
        if self._control:
            self._control.update()

class Button(BaseComponent):
    """按钮组件"""
    
    def __init__(
        self,
        text: str = "",
        icon: Optional[str] = None,
        variant: ComponentVariant = ComponentVariant.PRIMARY,
        size: ComponentSize = ComponentSize.MEDIUM,
        on_click: Optional[Callable] = None,
        width: Optional[float] = None,
        height: Optional[float] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.text = text
        self.icon = icon
        self.variant = variant
        self.size = size
        self.on_click = on_click
        self.width = width
        self.height = height
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            # 更新按钮样式
            self._control.bgcolor = self.theme.get_color(self.variant)
            self._control.color = self.theme.colors.text_primary if self.variant == ComponentVariant.SECONDARY else "white"
    
    def build(self) -> ft.Control:
        """构建按钮"""
        if not self.theme:
            return ft.Container()
        
        # 根据尺寸设置高度
        height_map = {
            ComponentSize.SMALL: 32,
            ComponentSize.MEDIUM: 40,
            ComponentSize.LARGE: 48,
            ComponentSize.EXTRA_LARGE: 56,
        }
        
        button_height = self.height or height_map.get(self.size, 40)
        font_size = self.theme.get_font_size(self.size)
        
        # 创建按钮
        if self.icon and self.text:
            # 带图标和文字的按钮
            button = ft.ElevatedButton(
                text=self.text,
                icon=self.icon,
                on_click=self.on_click,
                width=self.width,
                height=button_height,
                bgcolor=self.theme.get_color(self.variant),
                color="white" if self.variant != ComponentVariant.SECONDARY else self.theme.colors.text_primary,
                disabled=self.disabled,
                tooltip=self.tooltip,
                style=ft.ButtonStyle(
                    text_style=ft.TextStyle(
                        size=font_size,
                        weight=ft.FontWeight.MEDIUM
                    ),
                    shape=ft.RoundedRectangleBorder(
                        radius=self.theme.border_radius.md
                    )
                )
            )
        elif self.icon:
            # 仅图标按钮
            button = ft.IconButton(
                icon=self.icon,
                on_click=self.on_click,
                icon_size=font_size + 4,
                bgcolor=self.theme.get_color(self.variant),
                icon_color="white" if self.variant != ComponentVariant.SECONDARY else self.theme.colors.text_primary,
                disabled=self.disabled,
                tooltip=self.tooltip or self.text,
                width=button_height,
                height=button_height,
            )
        else:
            # 仅文字按钮
            button = ft.ElevatedButton(
                text=self.text,
                on_click=self.on_click,
                width=self.width,
                height=button_height,
                bgcolor=self.theme.get_color(self.variant),
                color="white" if self.variant != ComponentVariant.SECONDARY else self.theme.colors.text_primary,
                disabled=self.disabled,
                tooltip=self.tooltip,
                style=ft.ButtonStyle(
                    text_style=ft.TextStyle(
                        size=font_size,
                        weight=ft.FontWeight.MEDIUM
                    ),
                    shape=ft.RoundedRectangleBorder(
                        radius=self.theme.border_radius.md
                    )
                )
            )
        
        return button

class Input(BaseComponent):
    """输入框组件"""
    
    def __init__(
        self,
        label: str = "",
        placeholder: str = "",
        value: str = "",
        password: bool = False,
        multiline: bool = False,
        max_lines: Optional[int] = None,
        on_change: Optional[Callable] = None,
        on_submit: Optional[Callable] = None,
        width: Optional[float] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.label = label
        self.placeholder = placeholder
        self.value = value
        self.password = password
        self.multiline = multiline
        self.max_lines = max_lines
        self.on_change = on_change
        self.on_submit = on_submit
        self.width = width
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            # 更新输入框样式
            self._control.bgcolor = self.theme.colors.surface
            self._control.color = self.theme.colors.text_primary
            self._control.border_color = self.theme.colors.border
    
    def build(self) -> ft.Control:
        """构建输入框"""
        if not self.theme:
            return ft.Container()
        
        input_field = ft.TextField(
            label=self.label,
            hint_text=self.placeholder,
            value=self.value,
            password=self.password,
            multiline=self.multiline,
            max_lines=self.max_lines,
            on_change=self.on_change,
            on_submit=self.on_submit,
            width=self.width,
            disabled=self.disabled,
            tooltip=self.tooltip,
            bgcolor=self.theme.colors.surface,
            color=self.theme.colors.text_primary,
            border_color=self.theme.colors.border,
            focused_border_color=self.theme.colors.primary,
            text_style=ft.TextStyle(
                size=self.theme.typography.font_size_md,
                font_family=self.theme.typography.font_family
            ),
            label_style=ft.TextStyle(
                size=self.theme.typography.font_size_sm,
                color=self.theme.colors.text_secondary
            )
        )
        
        return input_field

class TextArea(Input):
    """文本域组件"""
    
    def __init__(
        self,
        min_lines: int = 3,
        max_lines: int = 10,
        **kwargs
    ):
        kwargs['multiline'] = True
        kwargs['max_lines'] = max_lines
        super().__init__(**kwargs)
        self.min_lines = min_lines
    
    def build(self) -> ft.Control:
        """构建文本域"""
        control = super().build()
        if hasattr(control, 'min_lines'):
            control.min_lines = self.min_lines
        return control

class Card(BaseComponent):
    """卡片组件"""
    
    def __init__(
        self,
        content: Optional[ft.Control] = None,
        title: Optional[str] = None,
        subtitle: Optional[str] = None,
        actions: Optional[List[ft.Control]] = None,
        elevation: float = 2.0,
        padding: Optional[float] = None,
        margin: Optional[float] = None,
        width: Optional[float] = None,
        height: Optional[float] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.content = content
        self.title = title
        self.subtitle = subtitle
        self.actions = actions or []
        self.elevation = elevation
        self.padding = padding
        self.margin = margin
        self.width = width
        self.height = height
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            # 更新卡片样式
            self._control.bgcolor = self.theme.colors.surface
            self._control.border = ft.border.all(1, self.theme.colors.border)
    
    def build(self) -> ft.Control:
        """构建卡片"""
        if not self.theme:
            return ft.Container()
        
        card_content = []
        
        # 标题区域
        if self.title or self.subtitle:
            title_content = []
            
            if self.title:
                title_content.append(
                    ft.Text(
                        self.title,
                        size=self.theme.typography.font_size_lg,
                        weight=ft.FontWeight.BOLD,
                        color=self.theme.colors.text_primary
                    )
                )
            
            if self.subtitle:
                title_content.append(
                    ft.Text(
                        self.subtitle,
                        size=self.theme.typography.font_size_sm,
                        color=self.theme.colors.text_secondary
                    )
                )
            
            card_content.append(
                ft.Column(
                    title_content,
                    spacing=self.theme.spacing.xs,
                    tight=True
                )
            )
        
        # 主要内容
        if self.content:
            card_content.append(self.content)
        
        # 操作按钮
        if self.actions:
            card_content.append(
                ft.Row(
                    self.actions,
                    alignment=ft.MainAxisAlignment.END,
                    spacing=self.theme.spacing.sm
                )
            )
        
        card = ft.Container(
            content=ft.Column(
                card_content,
                spacing=self.theme.spacing.md,
                tight=True
            ),
            bgcolor=self.theme.colors.surface,
            border=ft.border.all(1, self.theme.colors.border),
            border_radius=self.theme.border_radius.lg,
            padding=self.padding or self.theme.spacing.lg,
            margin=self.margin or self.theme.spacing.sm,
            width=self.width,
            height=self.height,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=self.elevation * 2,
                color=self.theme.colors.shadow,
                offset=ft.Offset(0, self.elevation)
            )
        )
        
        return card

class Modal(BaseComponent):
    """模态框组件"""
    
    def __init__(
        self,
        title: str = "",
        content: Optional[ft.Control] = None,
        actions: Optional[List[ft.Control]] = None,
        width: float = 400,
        height: Optional[float] = None,
        dismissible: bool = True,
        on_dismiss: Optional[Callable] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.title = title
        self.content = content
        self.actions = actions or []
        self.width = width
        self.height = height
        self.dismissible = dismissible
        self.on_dismiss = on_dismiss
        self._is_open = False
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            # 更新模态框样式
            pass  # AlertDialog的样式由Flet内部管理
    
    def build(self) -> ft.Control:
        """构建模态框"""
        if not self.theme:
            return ft.Container()
        
        modal_content = []
        
        # 内容区域
        if self.content:
            modal_content.append(self.content)
        
        dialog = ft.AlertDialog(
            title=ft.Text(self.title) if self.title else None,
            content=ft.Column(
                modal_content,
                spacing=self.theme.spacing.md,
                tight=True,
                scroll=ft.ScrollMode.AUTO
            ) if modal_content else None,
            actions=self.actions,
            actions_alignment=ft.MainAxisAlignment.END,
            on_dismiss=self.on_dismiss if self.dismissible else None,
        )
        
        return dialog
    
    def show(self, page: ft.Page):
        """显示模态框"""
        if not self._is_open:
            page.dialog = self.get_control()
            page.dialog.open = True
            page.update()
            self._is_open = True
    
    def hide(self, page: ft.Page):
        """隐藏模态框"""
        if self._is_open and page.dialog:
            page.dialog.open = False
            page.update()
            self._is_open = False

class LoadingSpinner(BaseComponent):
    """加载动画组件"""
    
    def __init__(
        self,
        size: ComponentSize = ComponentSize.MEDIUM,
        color: Optional[str] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.size = size
        self.color = color
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            # 更新加载动画样式
            self._control.color = self.color or self.theme.colors.primary
    
    def build(self) -> ft.Control:
        """构建加载动画"""
        if not self.theme:
            return ft.Container()
        
        # 根据尺寸设置大小
        size_map = {
            ComponentSize.SMALL: 16,
            ComponentSize.MEDIUM: 24,
            ComponentSize.LARGE: 32,
            ComponentSize.EXTRA_LARGE: 48,
        }
        
        spinner_size = size_map.get(self.size, 24)
        
        spinner = ft.ProgressRing(
            width=spinner_size,
            height=spinner_size,
            color=self.color or self.theme.colors.primary,
            stroke_width=2
        )
        
        return spinner

class ProgressBar(BaseComponent):
    """进度条组件"""
    
    def __init__(
        self,
        value: float = 0.0,
        max_value: float = 1.0,
        show_label: bool = True,
        color: Optional[str] = None,
        width: Optional[float] = None,
        height: float = 8,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.value = value
        self.max_value = max_value
        self.show_label = show_label
        self.color = color
        self.width = width
        self.height = height
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            # 更新进度条样式
            self._control.color = self.color or self.theme.colors.primary
    
    def build(self) -> ft.Control:
        """构建进度条"""
        if not self.theme:
            return ft.Container()
        
        progress_value = self.value / self.max_value if self.max_value > 0 else 0
        
        progress_bar = ft.ProgressBar(
            value=progress_value,
            width=self.width,
            height=self.height,
            color=self.color or self.theme.colors.primary,
            bgcolor=self.theme.colors.border,
            tooltip=self.tooltip
        )
        
        if self.show_label:
            percentage = int(progress_value * 100)
            label = ft.Text(
                f"{percentage}%",
                size=self.theme.typography.font_size_sm,
                color=self.theme.colors.text_secondary
            )
            
            return ft.Column(
                [
                    progress_bar,
                    label
                ],
                spacing=self.theme.spacing.xs,
                tight=True
            )
        
        return progress_bar
    
    def set_value(self, value: float):
        """设置进度值"""
        self.value = max(0, min(value, self.max_value))
        if self._control:
            if hasattr(self._control, 'value'):
                self._control.value = self.value / self.max_value
            self.update()

class Notification(BaseComponent):
    """通知组件"""
    
    def __init__(
        self,
        message: str,
        title: Optional[str] = None,
        variant: ComponentVariant = ComponentVariant.INFO,
        duration: Optional[int] = 5000,  # 毫秒
        closable: bool = True,
        on_close: Optional[Callable] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.message = message
        self.title = title
        self.variant = variant
        self.duration = duration
        self.closable = closable
        self.on_close = on_close
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            # 更新通知样式
            self._control.bgcolor = self.theme.get_color(self.variant)
    
    def build(self) -> ft.Control:
        """构建通知"""
        if not self.theme:
            return ft.Container()
        
        # 图标映射
        icon_map = {
            ComponentVariant.SUCCESS: ft.icons.CHECK_CIRCLE,
            ComponentVariant.WARNING: ft.icons.WARNING,
            ComponentVariant.ERROR: ft.icons.ERROR,
            ComponentVariant.INFO: ft.icons.INFO,
        }
        
        notification_content = [
            ft.Row(
                [
                    ft.Icon(
                        icon_map.get(self.variant, ft.icons.INFO),
                        color="white",
                        size=20
                    ),
                    ft.Column(
                        [
                            ft.Text(
                                self.title,
                                size=self.theme.typography.font_size_md,
                                weight=ft.FontWeight.BOLD,
                                color="white"
                            ) if self.title else None,
                            ft.Text(
                                self.message,
                                size=self.theme.typography.font_size_sm,
                                color="white"
                            )
                        ],
                        spacing=self.theme.spacing.xs,
                        tight=True,
                        expand=True
                    ),
                    ft.IconButton(
                        icon=ft.icons.CLOSE,
                        icon_color="white",
                        icon_size=16,
                        on_click=self.on_close
                    ) if self.closable else None
                ],
                spacing=self.theme.spacing.sm,
                alignment=ft.CrossAxisAlignment.START
            )
        ]
        
        # 过滤None值
        notification_content = [item for item in notification_content if item is not None]
        
        notification = ft.Container(
            content=ft.Column(
                notification_content,
                spacing=0,
                tight=True
            ),
            bgcolor=self.theme.get_color(self.variant),
            border_radius=self.theme.border_radius.md,
            padding=self.theme.spacing.md,
            margin=self.theme.spacing.sm,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=self.theme.colors.shadow,
                offset=ft.Offset(0, 2)
            )
        )
        
        return notification
    
    def show(self, page: ft.Page, container: Optional[ft.Control] = None):
        """显示通知"""
        # 这里可以实现通知的显示逻辑
        # 例如添加到页面的通知容器中
        pass

# 导出所有组件
__all__ = [
    'ThemeMode',
    'ComponentSize', 
    'ComponentVariant',
    'ColorPalette',
    'Typography',
    'Spacing',
    'BorderRadius',
    'Theme',
    'ThemeManager',
    'theme_manager',
    'BaseComponent',
    'Button',
    'Input',
    'TextArea',
    'Card',
    'Modal',
    'LoadingSpinner',
    'ProgressBar',
    'Notification',
]