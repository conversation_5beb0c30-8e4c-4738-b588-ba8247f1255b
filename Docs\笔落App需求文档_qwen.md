# 笔落App 产品需求文档

## 1. 引言

### 1.1 项目背景
笔落App是一款专为小说创作者设计的智能化写作工具，旨在通过先进的AI技术与人性化的创作功能相结合，帮助作者将创意高效转化为精彩的文学作品。本App致力于简化写作流程，降低创作门槛，使作者能更专注于核心创意的构思与打磨。

### 1.2 目标用户
- 网络小说作者
- 剧本编剧
- 业余写作爱好者
- 希望借助AI提升内容创作效率的内容创作者

### 1.3 核心价值
- **创意落地**: 将作者的灵感火花，通过结构化的大纲和AI辅助，系统地发展成完整的故事框架。
- **效率提升**: 借助AI自动生成、续写、润色内容，大幅缩短创作周期，让作者从繁琐的文字工作中解放出来。
- **体验优化**: 提供一站式的项目管理、资料设定、内容创作与发布服务，打造沉浸式、无干扰的写作环境。

## 2. 功能需求

### 2.1 项目管理
#### 2.1.1 项目创建与开启
- **创建项目**: 用户可通过“创建项目”按钮，在指定目录（默认为`project/`）下初始化一个新的写作项目。
- **打开项目**: 用户可通过“打开项目”按钮，浏览并加载本地的项目文件，快速进入创作界面。
- **项目模板**: 提供不同类型（如长篇小说、短篇故事、剧本）的模板，内置相应的结构化目录。

### 2.2 核心创作模块
#### 2.2.1 故事圣经 (Story Bible)
“故事圣经”是承载小说世界观的核心模块，方便作者随时查阅和管理，并为AI提供精准的创作背景。
- **角色管理**:
    - 添加、编辑、删除角色卡片。
    - 设定角色属性：姓名、性别、年龄、性格、背景故事、能力、社交关系等。
    - 自动生成角色头像（可选）。
- **场景管理**:
    - 添加、编辑、删除场景卡片。
    - 设定场景属性：名称、地理位置、环境描述、氛围、相关事件等。
- **事件管理**:
    - 添加、编辑、删除事件卡片。
    - 设定事件属性：名称、时间线、起因、经过、结果、涉及角色等。
- **物品与设定**:
    - 管理小说中的关键物品、特殊能力、世界规则等自定义设定。

#### 2.2.2 大纲设计
- **多层级大纲**: 支持树状结构的大纲，可无限层级嵌套，方便管理“卷-章-节”等复杂结构。
- **卡片式管理**: 每个节点均为一张可拖拽的卡片，包含标题和简介，支持拖拽排序和调整层级。
- **情节线规划**: 可为不同角色的情节线设置专属标签和颜色，直观展示多线叙事。

#### 2.2.3 章节写作
- **沉浸式编辑器**: 提供简洁、无干扰的写作界面，支持Markdown语法。
- **多文档视图**: 支持分屏或标签页形式，同时打开章节内容、大纲、角色卡片等，方便参照。
- **写作目标设定**: 可设定单章/单日的字数目标，并通过进度条直观展示。

### 2.3 AI辅助功能
#### 2.3.1 智能内容生成
- **大纲扩展**: 根据大纲节点简介，一键生成章节的详细草稿。
- **内容续写**: 在当前光标位置，根据上下文智能续写故事。
- **段落生成**: 根据用户输入的关键词或简单描述（如“描写一场激烈的雨夜追逐战”），生成完整的段落。

#### 2.3.2 文本优化与润色
- **AI一键润色**:
    - **去AI味**: 优化AI生成内容的表达方式，使其更自然、更富文采。
    - **风格转换**: 将段落转换为不同风格（如“鲁迅风”、“古龙风”）。
    - **对话优化**: 优化角色对话，使其更符合人物性格和当前情境。
    - **描写增强**: 丰富场景和动作的细节描写，提升画面感。
- **语法与错别字检查**: 自动识别并修正文本中的语法错误和错别字。

### 2.4 内容输出与发布
- **多种导出格式**: 支持将作品导出为TXT、PDF、DOCX、EPUB等多种格式。
- **自定义排版**: 提供简单的排版设置，如字体、字号、行间距、封面等。
- **云同步与备份**: 支持将项目文件同步至云端，确保数据安全，并支持多端写作。

## 3. 界面与工作流设计

### 3.1 设计哲学
- **沉浸式创作**: 最大化减少界面干扰，让作者专注于文字本身。
- **模块化与整合**: 各功能模块既独立又互相关联，数据在后台无缝流转。
- **可视化与直观**: 通过UI设计，将复杂的项目结构和故事元素直观地呈现给用户。

### 3.2 界面设计
#### 3.2.1 主界面 (Dashboard)
- **近期项目**: 以卡片列表形式展示最近打开的项目，包含项目名称、上次修改时间和字数统计。
- **核心操作**: 显眼的“创建新项目”和“打开本地项目”按钮。
- **全局设置**: 右上角提供一个全局的设置入口。
- **意境背景**: 保留富有设计感的背景，营造创作氛围。

#### 3.2.2 创作界面 (The Forge)
采用经典的三栏式布局，为作者提供最高效的创作环境。
- **左侧边栏 (导航与资料库)**:
    - **大纲视图**: 以可折叠的树状列表展示小说大纲，方便快速跳转。
    - **故事圣经**: 提供对角色、场景、事件等核心设定的快速访问入口。
    - 该侧边栏可完全折叠，以进入“禅模式”专注写作。
- **中间主编辑区 (写作核心)**:
    - **章节编辑器**: 简洁的文本编辑区域，支持Markdown。
    - **文档标签页**: 允许用户同时打开多个章节或笔记，方便切换。
- **右侧边栏 (智能工具箱)**:
    - **AI助手**: 一个聊天窗口，可以进行头脑风暴、查询设定、或执行特定指令。
    - **情境信息板**: 根据光标所在位置，自动显示相关的角色卡片或场景信息。
    - **写作目标**: 显示当前章节的字数统计和目标进度。

#### 3.2.3 设置界面
- **通用设置**: 主题（浅色/深色）、字体选择、字号、背景图片更换。
- **AI模型设置**:
    - API Key管理：支持添加和管理多个AI厂商的API Key。
    - 模型选择：为不同任务（如续写、润色、对话生成）选择偏好的AI模型。
- **账户与同步**: 账户信息、云同步开关、数据导入/导出。

### 3.3 核心工作流
1. **启动与进入**: 用户打开App，进入主界面Dashboard，选择一个近期项目或创建一个新项目。
2. **项目初始化**: 创建新项目时，用户可以选择一个项目模板（如“赛博朋克小说模板”），App会自动创建好推荐的大纲结构和故事圣经条目。
3. **构建世界**: 在创作初期，用户主要在“故事圣经”中填充世界观，创建核心角色、主要场景和关键事件。
4. **规划骨架**: 用户在“大纲设计”模块中，通过拖拽卡片的方式，快速构建故事的起承转合。
5. **迭代式写作**:
    - 在左侧边栏选择一个章节，内容加载至中间**主编辑区**。
    - 作者进行写作，随时可以在右侧边栏的**AI助手**处获取灵感，或让AI根据上下文续写。
    - 当写到一个关键角色或场景时，其信息会自动出现在右侧**情境信息板**上，无需手动查找。
    - 写作过程中，随时可以跳回“故事圣经”或“大纲”进行调整，所有变更都会实时同步。
6. **内容优化**: 完成初稿后，作者可以选中全文或特定段落，使用“AI一键润色”功能进行深度优化。
7. **导出与分享**: 写作完成后，通过导出功能生成多种格式的电子书或文稿。

## 4. 技术选型建议

### 4.1 前端框架
- **推荐**: Electron + Vue 3 / React
- **理由**: 
    - **Electron**: 实现跨平台桌面应用的最佳选择，可以将Web技术栈（HTML, CSS, JavaScript）打包为原生的桌面程序，一次开发，多平台运行。
    - **Vue 3 / React**: 主流的前端框架，拥有庞大的社区和丰富的生态系统，组件化开发模式能有效提升开发效率和代码可维护性。

### 4.2 UI组件库
- **推荐**: Element Plus (for Vue) / Ant Design (for React)
- **理由**: 提供丰富、高质量的预设组件，能快速构建出专业、美观的界面，减少重复造轮子的工作。

### 4.3 核心AI能力
- **推荐**: 对接主流大语言模型（LLM）提供商的API。
- **理由**: 
    - **灵活性**: 支持用户配置自己的API Key，可以选择不同的模型（如GPT系列、Claude系列、国内厂商模型等），满足多样化的创作需求和成本考量。
    - **能力保障**: 直接利用业界最前沿的AI技术，确保生成内容的质量。

### 4.4 数据存储
- **推荐**: 本地文件系统 + SQLite
- **理由**: 
    - **本地优先**: 项目文件和设定直接以文件形式（如JSON或Markdown）存储在用户本地，简单、直观，便于用户自行备份和管理。
    - **SQLite**: 对于结构化的数据（如角色、事件的关系），使用轻量级的嵌入式数据库SQLite进行管理，可以提供高效的查询和索引能力，而无需用户安装独立的数据库服务。

## 5. 开发路线图 (Roadmap)

### 第一阶段：核心功能闭环 (MVP - 最小可行产品)
- **目标**: 快速验证核心创意，跑通从项目创建到内容生成、导出的主流程。
- **核心任务**:
    1.  完成主界面和创作界面的基础布局。
    2.  实现项目的创建、打开与保存功能。
    3.  实现树状大纲的编辑与管理。
    4.  实现核心的章节编辑器，支持Markdown输入。
    5.  对接至少一个主流AI模型的API，实现最基本的内容续写功能。
    6.  实现简单的文本导出功能（如TXT）。

### 第二阶段：创作体验优化
- **目标**: 完善“故事圣经”和AI辅助功能，提升创作效率和智能化水平。
- **核心任务**:
    1.  完成“故事圣经”模块，包括角色、场景、事件的增删改查。
    2.  实现更丰富的AI功能，如大纲扩展、段落生成、对话优化等。
    3.  实现右侧边栏的“情境信息板”，根据上下文自动展示相关资料。
    4.  优化编辑器，支持多标签页和分屏视图。
    5.  引入本地SQLite数据库，优化“故事圣经”的数据管理。

### 第三阶段：产品完善与生态构建
- **目标**: 提升产品的专业性和用户粘性。
- **核心任务**:
    1.  实现“AI一键润色”功能，包括“去AI味”、风格转换等高级功能。
    2.  完善设置中心，支持主题切换、多AI模型配置等。
    3.  支持更多导出格式（PDF, DOCX, EPUB）。
    4.  开发云同步功能，实现跨设备写作。
    5.  根据用户反馈，持续迭代和优化现有功能。