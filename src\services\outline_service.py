"""大纲管理服务

提供完整的故事大纲管理功能，包括：
- 多层级大纲创建和编辑
- 大纲可视化和结构管理
- AI辅助大纲生成
- 大纲逻辑检查和优化
- 大纲模板管理
"""

import json
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from uuid import UUID
from enum import Enum

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from fastapi import HTTPException, status
from pydantic import BaseModel, Field

from ..models.project import Project
from ..services.content_service import ContentService, ContentType, GenerationRequest
from ..core.database import get_db
from ..utils.logger import get_logger

logger = get_logger(__name__)

class OutlineNodeType(str, Enum):
    """大纲节点类型"""
    PART = "part"              # 部分
    CHAPTER = "chapter"        # 章节
    SECTION = "section"        # 小节
    SCENE = "scene"            # 场景
    PLOT_POINT = "plot_point"  # 情节点

class OutlineNode(BaseModel):
    """大纲节点模型"""
    id: str = Field(..., description="节点ID")
    title: str = Field(..., description="节点标题")
    description: Optional[str] = Field(None, description="节点描述")
    node_type: OutlineNodeType = Field(..., description="节点类型")
    level: int = Field(0, description="层级深度")
    order: int = Field(0, description="排序序号")
    parent_id: Optional[str] = Field(None, description="父节点ID")
    children: List['OutlineNode'] = Field(default_factory=list, description="子节点列表")
    
    # 内容相关
    content: Optional[str] = Field(None, description="详细内容")
    word_count: int = Field(0, description="字数")
    target_word_count: Optional[int] = Field(None, description="目标字数")
    
    # 状态和标记
    status: str = Field("draft", description="状态：draft, writing, completed")
    tags: List[str] = Field(default_factory=list, description="标签")
    notes: Optional[str] = Field(None, description="备注")
    
    # 时间信息
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    # 故事元素
    characters: List[str] = Field(default_factory=list, description="涉及角色")
    locations: List[str] = Field(default_factory=list, description="涉及地点")
    conflicts: List[str] = Field(default_factory=list, description="冲突要素")
    themes: List[str] = Field(default_factory=list, description="主题要素")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

OutlineNode.model_rebuild()

class OutlineStructure(BaseModel):
    """大纲结构模型"""
    project_id: UUID = Field(..., description="项目ID")
    title: str = Field(..., description="大纲标题")
    description: Optional[str] = Field(None, description="大纲描述")
    root_nodes: List[OutlineNode] = Field(default_factory=list, description="根节点列表")
    version: str = Field("1.0", description="版本号")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    def get_all_nodes(self) -> List[OutlineNode]:
        """获取所有节点的扁平列表"""
        nodes = []
        
        def collect_nodes(node_list: List[OutlineNode]):
            for node in node_list:
                nodes.append(node)
                if node.children:
                    collect_nodes(node.children)
        
        collect_nodes(self.root_nodes)
        return nodes
    
    def find_node(self, node_id: str) -> Optional[OutlineNode]:
        """查找指定ID的节点"""
        def search_nodes(node_list: List[OutlineNode]) -> Optional[OutlineNode]:
            for node in node_list:
                if node.id == node_id:
                    return node
                if node.children:
                    result = search_nodes(node.children)
                    if result:
                        return result
            return None
        
        return search_nodes(self.root_nodes)
    
    def get_node_path(self, node_id: str) -> List[str]:
        """获取节点路径"""
        def find_path(node_list: List[OutlineNode], target_id: str, current_path: List[str]) -> Optional[List[str]]:
            for node in node_list:
                new_path = current_path + [node.id]
                if node.id == target_id:
                    return new_path
                if node.children:
                    result = find_path(node.children, target_id, new_path)
                    if result:
                        return result
            return None
        
        return find_path(self.root_nodes, node_id, []) or []

class OutlineTemplate(BaseModel):
    """大纲模板模型"""
    id: str = Field(..., description="模板ID")
    name: str = Field(..., description="模板名称")
    description: Optional[str] = Field(None, description="模板描述")
    genre: str = Field(..., description="适用类型")
    structure: OutlineStructure = Field(..., description="模板结构")
    tags: List[str] = Field(default_factory=list, description="标签")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")

class OutlineGenerationRequest(BaseModel):
    """大纲生成请求"""
    project_id: UUID = Field(..., description="项目ID")
    story_premise: str = Field(..., description="故事前提")
    genre: str = Field(..., description="小说类型")
    target_length: Optional[int] = Field(None, description="目标长度（字数）")
    structure_type: str = Field("three_act", description="结构类型：three_act, hero_journey, etc.")
    detail_level: str = Field("medium", description="详细程度：basic, medium, detailed")
    characters: List[str] = Field(default_factory=list, description="主要角色")
    themes: List[str] = Field(default_factory=list, description="主题要素")
    special_requirements: Optional[str] = Field(None, description="特殊要求")

class OutlineService:
    """大纲管理服务类"""
    
    def __init__(self, db: Session, content_service: ContentService = None):
        self.db = db
        self.content_service = content_service
    
    # 大纲CRUD操作
    
    def create_outline(self, project_id: UUID, title: str, description: str = None) -> OutlineStructure:
        """创建新大纲"""
        try:
            # 验证项目存在
            project = self.db.query(Project).filter(
                and_(
                    Project.id == project_id,
                    Project.deleted_at.is_(None)
                )
            ).first()
            
            if not project:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="项目不存在"
                )
            
            outline = OutlineStructure(
                project_id=project_id,
                title=title,
                description=description
            )
            
            # 保存大纲到项目文件
            self._save_outline_to_file(outline)
            
            logger.info(f"Created outline: {title} for project {project_id}")
            return outline
            
        except Exception as e:
            logger.error(f"Failed to create outline: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建大纲失败: {str(e)}"
            )
    
    def get_outline(self, project_id: UUID) -> Optional[OutlineStructure]:
        """获取项目大纲"""
        try:
            return self._load_outline_from_file(project_id)
        except Exception as e:
            logger.error(f"Failed to get outline for project {project_id}: {str(e)}")
            return None
    
    def update_outline(self, outline: OutlineStructure) -> OutlineStructure:
        """更新大纲"""
        try:
            outline.updated_at = datetime.utcnow()
            self._save_outline_to_file(outline)
            
            logger.info(f"Updated outline for project {outline.project_id}")
            return outline
            
        except Exception as e:
            logger.error(f"Failed to update outline: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"更新大纲失败: {str(e)}"
            )
    
    def delete_outline(self, project_id: UUID) -> bool:
        """删除大纲"""
        try:
            outline_file = self._get_outline_file_path(project_id)
            if outline_file.exists():
                outline_file.unlink()
                logger.info(f"Deleted outline for project {project_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Failed to delete outline: {str(e)}")
            return False
    
    # 节点操作
    
    def add_node(
        self,
        project_id: UUID,
        parent_id: Optional[str],
        node_data: Dict[str, Any]
    ) -> OutlineNode:
        """添加大纲节点"""
        outline = self.get_outline(project_id)
        if not outline:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="大纲不存在"
            )
        
        # 生成节点ID
        node_id = f"node_{int(datetime.utcnow().timestamp() * 1000)}"
        
        # 创建新节点
        new_node = OutlineNode(
            id=node_id,
            **node_data
        )
        
        # 添加到适当位置
        if parent_id:
            parent_node = outline.find_node(parent_id)
            if parent_node:
                new_node.level = parent_node.level + 1
                new_node.parent_id = parent_id
                parent_node.children.append(new_node)
            else:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="父节点不存在"
                )
        else:
            # 添加为根节点
            new_node.level = 0
            outline.root_nodes.append(new_node)
        
        # 更新排序
        self._update_node_order(outline)
        
        # 保存大纲
        self.update_outline(outline)
        
        return new_node
    
    def update_node(
        self,
        project_id: UUID,
        node_id: str,
        node_data: Dict[str, Any]
    ) -> Optional[OutlineNode]:
        """更新大纲节点"""
        outline = self.get_outline(project_id)
        if not outline:
            return None
        
        node = outline.find_node(node_id)
        if not node:
            return None
        
        # 更新节点数据
        for key, value in node_data.items():
            if hasattr(node, key):
                setattr(node, key, value)
        
        node.updated_at = datetime.utcnow()
        
        # 保存大纲
        self.update_outline(outline)
        
        return node
    
    def delete_node(self, project_id: UUID, node_id: str) -> bool:
        """删除大纲节点"""
        outline = self.get_outline(project_id)
        if not outline:
            return False
        
        # 查找并删除节点
        def remove_node(node_list: List[OutlineNode]) -> bool:
            for i, node in enumerate(node_list):
                if node.id == node_id:
                    node_list.pop(i)
                    return True
                if node.children and remove_node(node.children):
                    return True
            return False
        
        if remove_node(outline.root_nodes):
            # 更新排序
            self._update_node_order(outline)
            # 保存大纲
            self.update_outline(outline)
            return True
        
        return False
    
    def move_node(
        self,
        project_id: UUID,
        node_id: str,
        new_parent_id: Optional[str],
        new_position: int
    ) -> bool:
        """移动大纲节点"""
        outline = self.get_outline(project_id)
        if not outline:
            return False
        
        # 找到要移动的节点
        node_to_move = outline.find_node(node_id)
        if not node_to_move:
            return False
        
        # 从原位置删除
        self.delete_node(project_id, node_id)
        
        # 重新获取大纲（因为删除操作会更新文件）
        outline = self.get_outline(project_id)
        
        # 添加到新位置
        if new_parent_id:
            parent_node = outline.find_node(new_parent_id)
            if parent_node:
                node_to_move.level = parent_node.level + 1
                node_to_move.parent_id = new_parent_id
                parent_node.children.insert(new_position, node_to_move)
            else:
                return False
        else:
            # 移动到根级别
            node_to_move.level = 0
            node_to_move.parent_id = None
            outline.root_nodes.insert(new_position, node_to_move)
        
        # 更新排序
        self._update_node_order(outline)
        
        # 保存大纲
        self.update_outline(outline)
        
        return True
    
    # AI辅助功能
    
    def generate_outline_with_ai(
        self,
        request: OutlineGenerationRequest
    ) -> OutlineStructure:
        """使用AI生成大纲"""
        if not self.content_service:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="AI服务不可用"
            )
        
        try:
            # 构建生成提示
            prompt = self._build_outline_generation_prompt(request)
            
            # 调用AI生成内容
            generation_request = GenerationRequest(
                content_type=ContentType.PLOT_OUTLINE,
                prompt=prompt,
                context={}
            )
            
            ai_response = self.content_service.generate_content(generation_request)
            
            # 解析AI响应为大纲结构
            outline = self._parse_ai_outline_response(ai_response, request)
            
            # 保存生成的大纲
            self._save_outline_to_file(outline)
            
            logger.info(f"Generated outline with AI for project {request.project_id}")
            return outline
            
        except Exception as e:
            logger.error(f"Failed to generate outline with AI: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"AI生成大纲失败: {str(e)}"
            )
    
    def enhance_node_with_ai(
        self,
        project_id: UUID,
        node_id: str,
        enhancement_type: str = "expand"
    ) -> Optional[OutlineNode]:
        """使用AI增强节点内容"""
        if not self.content_service:
            return None
        
        outline = self.get_outline(project_id)
        if not outline:
            return None
        
        node = outline.find_node(node_id)
        if not node:
            return None
        
        try:
            # 构建增强提示
            prompt = self._build_node_enhancement_prompt(node, enhancement_type)
            
            # 调用AI生成内容
            generation_request = GenerationRequest(
                content_type=ContentType.PLOT_OUTLINE,
                prompt=prompt,
                context={"node": node.dict()}
            )
            
            ai_response = self.content_service.generate_content(generation_request)
            
            # 更新节点内容
            if enhancement_type == "expand":
                node.description = ai_response
            elif enhancement_type == "detail":
                node.content = ai_response
            
            node.updated_at = datetime.utcnow()
            
            # 保存大纲
            self.update_outline(outline)
            
            return node
            
        except Exception as e:
            logger.error(f"Failed to enhance node with AI: {str(e)}")
            return None
    
    def check_outline_logic(self, project_id: UUID) -> Dict[str, Any]:
        """检查大纲逻辑"""
        outline = self.get_outline(project_id)
        if not outline:
            return {"valid": False, "errors": ["大纲不存在"]}
        
        errors = []
        warnings = []
        suggestions = []
        
        # 检查结构完整性
        all_nodes = outline.get_all_nodes()
        
        # 检查是否有空标题
        for node in all_nodes:
            if not node.title.strip():
                errors.append(f"节点 {node.id} 缺少标题")
        
        # 检查层级结构
        for node in all_nodes:
            if node.parent_id:
                parent = outline.find_node(node.parent_id)
                if not parent:
                    errors.append(f"节点 {node.id} 的父节点不存在")
                elif node.level != parent.level + 1:
                    warnings.append(f"节点 {node.id} 的层级不正确")
        
        # 检查内容完整性
        empty_nodes = [node for node in all_nodes if not node.description and not node.content]
        if empty_nodes:
            warnings.append(f"有 {len(empty_nodes)} 个节点缺少内容描述")
        
        # 检查字数目标
        nodes_with_targets = [node for node in all_nodes if node.target_word_count]
        if nodes_with_targets:
            total_target = sum(node.target_word_count for node in nodes_with_targets)
            suggestions.append(f"总目标字数: {total_target}")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "suggestions": suggestions,
            "statistics": {
                "total_nodes": len(all_nodes),
                "max_depth": max([node.level for node in all_nodes]) if all_nodes else 0,
                "nodes_by_type": self._count_nodes_by_type(all_nodes)
            }
        }
    
    # 模板管理
    
    def get_outline_templates(self, genre: str = None) -> List[OutlineTemplate]:
        """获取大纲模板列表"""
        # 这里返回预定义的模板，实际项目中可以从数据库或文件中加载
        templates = self._get_builtin_templates()
        
        if genre:
            templates = [t for t in templates if t.genre == genre or "通用" in t.genre]
        
        return templates
    
    def create_template_from_outline(
        self,
        project_id: UUID,
        template_name: str,
        description: str = None
    ) -> Optional[OutlineTemplate]:
        """从现有大纲创建模板"""
        outline = self.get_outline(project_id)
        if not outline:
            return None
        
        try:
            # 清理大纲数据（移除具体内容，保留结构）
            template_structure = self._clean_outline_for_template(outline)
            
            template = OutlineTemplate(
                id=f"template_{int(datetime.utcnow().timestamp())}",
                name=template_name,
                description=description,
                genre="通用",
                structure=template_structure
            )
            
            # 保存模板（实际项目中应该保存到数据库或文件）
            logger.info(f"Created template: {template_name} from project {project_id}")
            return template
            
        except Exception as e:
            logger.error(f"Failed to create template: {str(e)}")
            return None
    
    def apply_template(
        self,
        project_id: UUID,
        template_id: str,
        customizations: Dict[str, Any] = None
    ) -> Optional[OutlineStructure]:
        """应用模板到项目"""
        templates = self.get_outline_templates()
        template = next((t for t in templates if t.id == template_id), None)
        
        if not template:
            return None
        
        try:
            # 复制模板结构
            new_outline = OutlineStructure(
                project_id=project_id,
                title=f"{template.name}大纲",
                description=template.description,
                root_nodes=self._copy_template_nodes(template.structure.root_nodes)
            )
            
            # 应用自定义设置
            if customizations:
                self._apply_customizations(new_outline, customizations)
            
            # 保存大纲
            self._save_outline_to_file(new_outline)
            
            logger.info(f"Applied template {template_id} to project {project_id}")
            return new_outline
            
        except Exception as e:
            logger.error(f"Failed to apply template: {str(e)}")
            return None
    
    # 可视化和导出
    
    def get_outline_visualization_data(self, project_id: UUID) -> Dict[str, Any]:
        """获取大纲可视化数据"""
        outline = self.get_outline(project_id)
        if not outline:
            return {}
        
        def node_to_vis_data(node: OutlineNode) -> Dict[str, Any]:
            return {
                "id": node.id,
                "title": node.title,
                "type": node.node_type.value,
                "level": node.level,
                "status": node.status,
                "word_count": node.word_count,
                "target_word_count": node.target_word_count,
                "children": [node_to_vis_data(child) for child in node.children]
            }
        
        return {
            "project_id": str(outline.project_id),
            "title": outline.title,
            "nodes": [node_to_vis_data(node) for node in outline.root_nodes],
            "statistics": self._calculate_outline_statistics(outline)
        }
    
    def export_outline(
        self,
        project_id: UUID,
        export_format: str = "json"
    ) -> Optional[str]:
        """导出大纲"""
        outline = self.get_outline(project_id)
        if not outline:
            return None
        
        try:
            if export_format == "json":
                return outline.json(indent=2, ensure_ascii=False)
            elif export_format == "markdown":
                return self._outline_to_markdown(outline)
            elif export_format == "txt":
                return self._outline_to_text(outline)
            else:
                return None
                
        except Exception as e:
            logger.error(f"Failed to export outline: {str(e)}")
            return None
    
    # 私有辅助方法
    
    def _get_outline_file_path(self, project_id: UUID):
        """获取大纲文件路径"""
        from pathlib import Path
        from ..core.config import get_settings
        
        settings = get_settings()
        projects_dir = Path(settings.PROJECTS_DIR)
        
        # 查找项目目录
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if project and project.project_path:
            project_dir = Path(project.project_path)
        else:
            project_dir = projects_dir / str(project_id)
        
        return project_dir / "outlines" / "main_outline.json"
    
    def _save_outline_to_file(self, outline: OutlineStructure):
        """保存大纲到文件"""
        outline_file = self._get_outline_file_path(outline.project_id)
        outline_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(outline_file, 'w', encoding='utf-8') as f:
            f.write(outline.json(indent=2, ensure_ascii=False))
    
    def _load_outline_from_file(self, project_id: UUID) -> Optional[OutlineStructure]:
        """从文件加载大纲"""
        outline_file = self._get_outline_file_path(project_id)
        
        if not outline_file.exists():
            return None
        
        try:
            with open(outline_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return OutlineStructure(**data)
        except Exception as e:
            logger.error(f"Failed to load outline from file: {str(e)}")
            return None
    
    def _update_node_order(self, outline: OutlineStructure):
        """更新节点排序"""
        def update_order(nodes: List[OutlineNode]):
            for i, node in enumerate(nodes):
                node.order = i
                if node.children:
                    update_order(node.children)
        
        update_order(outline.root_nodes)
    
    def _build_outline_generation_prompt(self, request: OutlineGenerationRequest) -> str:
        """构建大纲生成提示"""
        prompt = f"""请为以下小说创建详细大纲：

故事前提：{request.story_premise}
小说类型：{request.genre}
结构类型：{request.structure_type}
详细程度：{request.detail_level}
"""
        
        if request.target_length:
            prompt += f"目标长度：{request.target_length}字\n"
        
        if request.characters:
            prompt += f"主要角色：{', '.join(request.characters)}\n"
        
        if request.themes:
            prompt += f"主题要素：{', '.join(request.themes)}\n"
        
        if request.special_requirements:
            prompt += f"特殊要求：{request.special_requirements}\n"
        
        prompt += "\n请按照层级结构生成大纲，包括部分、章节、场景等，每个节点都要有标题和简要描述。"
        
        return prompt
    
    def _parse_ai_outline_response(self, ai_response: str, request: OutlineGenerationRequest) -> OutlineStructure:
        """解析AI大纲响应"""
        # 这里需要实现AI响应的解析逻辑
        # 暂时返回一个基本结构
        outline = OutlineStructure(
            project_id=request.project_id,
            title=f"{request.genre}小说大纲",
            description="AI生成的大纲"
        )
        
        # 解析AI响应并创建节点结构
        # 这里需要根据实际AI响应格式来实现
        
        return outline
    
    def _build_node_enhancement_prompt(self, node: OutlineNode, enhancement_type: str) -> str:
        """构建节点增强提示"""
        if enhancement_type == "expand":
            return f"请扩展以下大纲节点的描述：\n标题：{node.title}\n当前描述：{node.description or '无'}\n请提供更详细的描述。"
        elif enhancement_type == "detail":
            return f"请为以下大纲节点提供详细内容：\n标题：{node.title}\n描述：{node.description or '无'}\n请提供具体的情节内容。"
        else:
            return f"请优化以下大纲节点：\n标题：{node.title}\n描述：{node.description or '无'}"
    
    def _count_nodes_by_type(self, nodes: List[OutlineNode]) -> Dict[str, int]:
        """按类型统计节点数量"""
        counts = {}
        for node in nodes:
            node_type = node.node_type.value
            counts[node_type] = counts.get(node_type, 0) + 1
        return counts
    
    def _get_builtin_templates(self) -> List[OutlineTemplate]:
        """获取内置模板"""
        # 返回一些预定义的模板
        return [
            OutlineTemplate(
                id="three_act_basic",
                name="三幕式结构",
                description="经典的三幕式故事结构",
                genre="通用",
                structure=OutlineStructure(
                    project_id=UUID("00000000-0000-0000-0000-000000000000"),
                    title="三幕式结构模板",
                    root_nodes=[
                        OutlineNode(
                            id="act1",
                            title="第一幕：建立",
                            node_type=OutlineNodeType.PART,
                            level=0,
                            description="介绍角色、背景和冲突"
                        ),
                        OutlineNode(
                            id="act2",
                            title="第二幕：对抗",
                            node_type=OutlineNodeType.PART,
                            level=0,
                            description="发展冲突，角色面临挑战"
                        ),
                        OutlineNode(
                            id="act3",
                            title="第三幕：解决",
                            node_type=OutlineNodeType.PART,
                            level=0,
                            description="解决冲突，故事结局"
                        )
                    ]
                )
            )
        ]
    
    def _clean_outline_for_template(self, outline: OutlineStructure) -> OutlineStructure:
        """清理大纲用作模板"""
        def clean_node(node: OutlineNode) -> OutlineNode:
            cleaned = OutlineNode(
                id=node.id,
                title=node.title,
                node_type=node.node_type,
                level=node.level,
                order=node.order,
                parent_id=node.parent_id,
                description="",  # 清空具体描述
                children=[clean_node(child) for child in node.children]
            )
            return cleaned
        
        return OutlineStructure(
            project_id=UUID("00000000-0000-0000-0000-000000000000"),
            title=outline.title,
            description="模板",
            root_nodes=[clean_node(node) for node in outline.root_nodes]
        )
    
    def _copy_template_nodes(self, nodes: List[OutlineNode]) -> List[OutlineNode]:
        """复制模板节点"""
        def copy_node(node: OutlineNode) -> OutlineNode:
            return OutlineNode(
                id=f"node_{int(datetime.utcnow().timestamp() * 1000000)}",
                title=node.title,
                description=node.description,
                node_type=node.node_type,
                level=node.level,
                order=node.order,
                parent_id=node.parent_id,
                children=[copy_node(child) for child in node.children]
            )
        
        return [copy_node(node) for node in nodes]
    
    def _apply_customizations(self, outline: OutlineStructure, customizations: Dict[str, Any]):
        """应用自定义设置"""
        if "title" in customizations:
            outline.title = customizations["title"]
        if "description" in customizations:
            outline.description = customizations["description"]
    
    def _calculate_outline_statistics(self, outline: OutlineStructure) -> Dict[str, Any]:
        """计算大纲统计信息"""
        all_nodes = outline.get_all_nodes()
        
        return {
            "total_nodes": len(all_nodes),
            "max_depth": max([node.level for node in all_nodes]) if all_nodes else 0,
            "total_word_count": sum(node.word_count for node in all_nodes),
            "total_target_words": sum(node.target_word_count or 0 for node in all_nodes),
            "nodes_by_type": self._count_nodes_by_type(all_nodes),
            "completion_rate": len([n for n in all_nodes if n.status == "completed"]) / len(all_nodes) * 100 if all_nodes else 0
        }
    
    def _outline_to_markdown(self, outline: OutlineStructure) -> str:
        """将大纲转换为Markdown格式"""
        def node_to_markdown(node: OutlineNode, indent: str = "") -> str:
            md = f"{indent}{'#' * (node.level + 1)} {node.title}\n\n"
            if node.description:
                md += f"{indent}{node.description}\n\n"
            
            for child in node.children:
                md += node_to_markdown(child, indent)
            
            return md
        
        md = f"# {outline.title}\n\n"
        if outline.description:
            md += f"{outline.description}\n\n"
        
        for node in outline.root_nodes:
            md += node_to_markdown(node)
        
        return md
    
    def _outline_to_text(self, outline: OutlineStructure) -> str:
        """将大纲转换为纯文本格式"""
        def node_to_text(node: OutlineNode, indent: str = "") -> str:
            text = f"{indent}{node.title}\n"
            if node.description:
                text += f"{indent}  {node.description}\n"
            
            for child in node.children:
                text += node_to_text(child, indent + "  ")
            
            return text
        
        text = f"{outline.title}\n{'=' * len(outline.title)}\n\n"
        if outline.description:
            text += f"{outline.description}\n\n"
        
        for node in outline.root_nodes:
            text += node_to_text(node)
        
        return text

# 服务实例获取函数
def get_outline_service(db: Session = None, content_service: ContentService = None) -> OutlineService:
    """获取大纲服务实例"""
    if db is None:
        db = next(get_db())
    return OutlineService(db, content_service)

# 导出的类和函数
__all__ = [
    "OutlineNodeType",
    "OutlineNode",
    "OutlineStructure",
    "OutlineTemplate",
    "OutlineGenerationRequest",
    "OutlineService",
    "get_outline_service"
]