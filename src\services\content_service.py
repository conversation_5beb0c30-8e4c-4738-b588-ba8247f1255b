"""内容生成服务

提供核心的AI辅助创作功能，包括内容生成、优化、去AI痕迹等功能，
以及上下文管理和提示词模板系统。
"""

import asyncio
import json
import logging
import re
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union, AsyncGenerator, <PERSON><PERSON>
from dataclasses import dataclass, asdict
from pathlib import Path

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..ai import AIMessage, AIResponse, AIModelType
from ..models.story_elements import Character, Scene, Event
from ..models.chapter import Chapter
from ..models.project import Project
from .ai_service import get_ai_service_manager, AIServiceManager

logger = logging.getLogger(__name__)


class ContentType(Enum):
    """内容类型"""
    PLOT_OUTLINE = "plot_outline"  # 情节大纲
    CHARACTER_PROFILE = "character_profile"  # 角色档案
    SCENE_DESCRIPTION = "scene_description"  # 场景描述
    DIALOGUE = "dialogue"  # 对话
    NARRATIVE = "narrative"  # 叙述
    CHAPTER_CONTENT = "chapter_content"  # 章节内容
    STORY_CONTINUATION = "story_continuation"  # 故事续写
    CONTENT_OPTIMIZATION = "content_optimization"  # 内容优化
    STYLE_ADJUSTMENT = "style_adjustment"  # 风格调整


class WritingStyle(Enum):
    """写作风格"""
    CLASSICAL = "classical"  # 古典文学
    MODERN = "modern"  # 现代文学
    FANTASY = "fantasy"  # 奇幻
    ROMANCE = "romance"  # 言情
    MYSTERY = "mystery"  # 悬疑
    SCIENCE_FICTION = "science_fiction"  # 科幻
    HISTORICAL = "historical"  # 历史
    URBAN = "urban"  # 都市
    MARTIAL_ARTS = "martial_arts"  # 武侠
    YOUTH = "youth"  # 青春


class OptimizationGoal(Enum):
    """优化目标"""
    READABILITY = "readability"  # 可读性
    ENGAGEMENT = "engagement"  # 吸引力
    EMOTION = "emotion"  # 情感表达
    PACING = "pacing"  # 节奏
    CHARACTER_DEPTH = "character_depth"  # 角色深度
    PLOT_COHERENCE = "plot_coherence"  # 情节连贯性
    LANGUAGE_POLISH = "language_polish"  # 语言润色
    REMOVE_AI_TRACES = "remove_ai_traces"  # 去AI痕迹


@dataclass
class GenerationContext:
    """生成上下文"""
    project_id: int
    chapter_id: Optional[int] = None
    characters: List[Character] = None
    scenes: List[Scene] = None
    events: List[Event] = None
    previous_content: Optional[str] = None
    writing_style: WritingStyle = WritingStyle.MODERN
    target_length: Optional[int] = None
    special_requirements: List[str] = None
    
    def __post_init__(self):
        if self.characters is None:
            self.characters = []
        if self.scenes is None:
            self.scenes = []
        if self.events is None:
            self.events = []
        if self.special_requirements is None:
            self.special_requirements = []


@dataclass
class GenerationRequest:
    """生成请求"""
    content_type: ContentType
    context: GenerationContext
    prompt: Optional[str] = None
    model: Optional[AIModelType] = None
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    stream: bool = False


@dataclass
class OptimizationRequest:
    """优化请求"""
    content: str
    goals: List[OptimizationGoal]
    context: GenerationContext
    model: Optional[AIModelType] = None
    temperature: float = 0.3


class PromptTemplate:
    """提示词模板"""
    
    def __init__(self, template_dir: Optional[Path] = None):
        self.template_dir = template_dir or Path(__file__).parent / "templates"
        self.templates: Dict[str, str] = {}
        self._load_templates()
    
    def _load_templates(self):
        """加载模板"""
        # 基础模板
        self.templates.update({
            "plot_outline": """
你是一位经验丰富的小说创作助手。请根据以下信息创建详细的情节大纲：

项目背景：{project_info}
主要角色：{characters}
关键场景：{scenes}
重要事件：{events}
写作风格：{writing_style}

请创建一个结构清晰、情节紧凑的故事大纲，包括：
1. 故事背景和设定
2. 主要情节线
3. 关键转折点
4. 角色发展弧线
5. 高潮和结局

要求：
- 保持情节的逻辑性和连贯性
- 确保角色动机合理
- 营造适当的冲突和张力
- 字数控制在{target_length}字左右

{special_requirements}
""",
            
            "character_profile": """
你是一位专业的角色设计师。请根据以下信息创建详细的角色档案：

角色基本信息：{character_info}
故事背景：{project_info}
相关角色：{other_characters}
写作风格：{writing_style}

请创建包含以下内容的角色档案：
1. 基本信息（姓名、年龄、职业等）
2. 外貌特征
3. 性格特点
4. 背景故事
5. 动机和目标
6. 人际关系
7. 成长弧线
8. 独特的说话方式或习惯

要求：
- 角色要有鲜明的个性
- 背景故事要合理可信
- 与其他角色的关系要清晰
- 为后续情节发展留下空间

{special_requirements}
""",
            
            "scene_description": """
你是一位擅长场景描写的作家。请根据以下信息创建生动的场景描述：

场景信息：{scene_info}
相关角色：{characters}
故事背景：{project_info}
写作风格：{writing_style}
前文内容：{previous_content}

请创建包含以下元素的场景描述：
1. 环境氛围
2. 视觉细节
3. 感官体验（声音、气味、触感等）
4. 情绪渲染
5. 与情节的关联

要求：
- 描写要生动具体
- 营造适当的氛围
- 服务于情节发展
- 字数控制在{target_length}字左右

{special_requirements}
""",
            
            "dialogue": """
你是一位对话写作专家。请根据以下信息创作自然流畅的对话：

对话场景：{scene_info}
参与角色：{characters}
情节背景：{project_info}
写作风格：{writing_style}
前文内容：{previous_content}

请创作符合以下要求的对话：
1. 符合角色性格特点
2. 推进情节发展
3. 展现角色关系
4. 语言自然流畅
5. 包含适当的动作和心理描写

要求：
- 每个角色的说话方式要有区别
- 对话要有目的性
- 避免冗长的说教
- 适当穿插动作和表情描写

{special_requirements}
""",
            
            "narrative": """
你是一位叙述技巧高超的作家。请根据以下信息创作精彩的叙述内容：

叙述内容：{content_info}
相关角色：{characters}
场景设定：{scenes}
故事背景：{project_info}
写作风格：{writing_style}
前文内容：{previous_content}

请创作包含以下元素的叙述：
1. 情节推进
2. 角色心理
3. 环境描写
4. 情感渲染
5. 节奏控制

要求：
- 叙述要生动有趣
- 保持适当的节奏
- 展现角色内心世界
- 字数控制在{target_length}字左右

{special_requirements}
""",
            
            "chapter_content": """
你是一位经验丰富的小说作家。请根据以下信息创作完整的章节内容：

章节大纲：{chapter_outline}
主要角色：{characters}
关键场景：{scenes}
重要事件：{events}
故事背景：{project_info}
写作风格：{writing_style}
前文内容：{previous_content}

请创作一个完整的章节，包含：
1. 引人入胜的开头
2. 情节发展
3. 角色互动
4. 场景描写
5. 适当的结尾

要求：
- 保持情节连贯性
- 角色行为符合设定
- 语言风格统一
- 节奏张弛有度
- 字数控制在{target_length}字左右

{special_requirements}
""",
            
            "story_continuation": """
你是一位续写专家。请根据以下信息继续创作故事：

前文内容：{previous_content}
主要角色：{characters}
当前场景：{scenes}
故事背景：{project_info}
写作风格：{writing_style}

请自然地续写故事，要求：
1. 与前文风格保持一致
2. 情节发展合理
3. 角色行为符合性格
4. 保持适当的悬念
5. 字数控制在{target_length}字左右

续写方向：{special_requirements}
""",
            
            "content_optimization": """
你是一位专业的文本编辑。请根据以下要求优化文本内容：

原始内容：{original_content}
优化目标：{optimization_goals}
写作风格：{writing_style}
故事背景：{project_info}

请针对以下方面进行优化：
{optimization_details}

要求：
- 保持原文的核心内容和情节
- 提升文本质量和可读性
- 确保风格统一
- 消除明显的AI生成痕迹

请提供优化后的内容，并简要说明主要改进点。
""",
            
            "style_adjustment": """
你是一位文体风格专家。请将以下内容调整为指定的写作风格：

原始内容：{original_content}
目标风格：{target_style}
故事背景：{project_info}

请调整以下方面：
1. 语言表达方式
2. 句式结构
3. 词汇选择
4. 叙述节奏
5. 情感表达

要求：
- 保持原文的核心内容
- 完全符合目标风格特点
- 语言自然流畅
- 避免生硬的转换

{special_requirements}
"""
        })
    
    def get_template(self, content_type: ContentType) -> str:
        """获取模板"""
        return self.templates.get(content_type.value, "")
    
    def format_template(
        self,
        content_type: ContentType,
        context: GenerationContext,
        **kwargs
    ) -> str:
        """格式化模板"""
        template = self.get_template(content_type)
        
        # 准备模板变量
        template_vars = {
            "project_info": self._format_project_info(context),
            "characters": self._format_characters(context.characters),
            "scenes": self._format_scenes(context.scenes),
            "events": self._format_events(context.events),
            "writing_style": self._format_writing_style(context.writing_style),
            "previous_content": context.previous_content or "无",
            "target_length": context.target_length or "适中",
            "special_requirements": self._format_special_requirements(context.special_requirements)
        }
        
        # 添加额外参数
        template_vars.update(kwargs)
        
        try:
            return template.format(**template_vars)
        except KeyError as e:
            logger.warning(f"Template formatting error: missing key {e}")
            return template
    
    def _format_project_info(self, context: GenerationContext) -> str:
        """格式化项目信息"""
        return f"项目ID: {context.project_id}"
    
    def _format_characters(self, characters: List[Character]) -> str:
        """格式化角色信息"""
        if not characters:
            return "暂无角色信息"
        
        char_info = []
        for char in characters:
            info = f"- {char.name}：{char.description or '暂无描述'}"
            if char.personality:
                info += f"，性格：{char.personality}"
            char_info.append(info)
        
        return "\n".join(char_info)
    
    def _format_scenes(self, scenes: List[Scene]) -> str:
        """格式化场景信息"""
        if not scenes:
            return "暂无场景信息"
        
        scene_info = []
        for scene in scenes:
            info = f"- {scene.name}：{scene.description or '暂无描述'}"
            if scene.atmosphere:
                info += f"，氛围：{scene.atmosphere}"
            scene_info.append(info)
        
        return "\n".join(scene_info)
    
    def _format_events(self, events: List[Event]) -> str:
        """格式化事件信息"""
        if not events:
            return "暂无事件信息"
        
        event_info = []
        for event in events:
            info = f"- {event.name}：{event.description or '暂无描述'}"
            if event.impact:
                info += f"，影响：{event.impact}"
            event_info.append(info)
        
        return "\n".join(event_info)
    
    def _format_writing_style(self, style: WritingStyle) -> str:
        """格式化写作风格"""
        style_map = {
            WritingStyle.CLASSICAL: "古典文学风格，语言典雅，注重意境",
            WritingStyle.MODERN: "现代文学风格，语言简洁，贴近生活",
            WritingStyle.FANTASY: "奇幻风格，想象丰富，世界观宏大",
            WritingStyle.ROMANCE: "言情风格，情感细腻，注重心理描写",
            WritingStyle.MYSTERY: "悬疑风格，情节紧凑，营造紧张氛围",
            WritingStyle.SCIENCE_FICTION: "科幻风格，逻辑严密，富有想象力",
            WritingStyle.HISTORICAL: "历史风格，考据严谨，还原时代背景",
            WritingStyle.URBAN: "都市风格，现实感强，反映都市生活",
            WritingStyle.MARTIAL_ARTS: "武侠风格，江湖气息，注重武打描写",
            WritingStyle.YOUTH: "青春风格，语言活泼，贴近年轻人"
        }
        return style_map.get(style, "现代文学风格")
    
    def _format_special_requirements(self, requirements: List[str]) -> str:
        """格式化特殊要求"""
        if not requirements:
            return "无特殊要求"
        
        return "特殊要求：\n" + "\n".join(f"- {req}" for req in requirements)


class ContentService:
    """内容生成服务"""
    
    def __init__(self, ai_manager: Optional[AIServiceManager] = None):
        self.ai_manager = ai_manager
        self.prompt_template = PromptTemplate()
        self._context_cache: Dict[int, GenerationContext] = {}
    
    async def initialize(self):
        """初始化服务"""
        if not self.ai_manager:
            self.ai_manager = await get_ai_service_manager()
    
    async def generate_content(
        self,
        request: GenerationRequest,
        db: AsyncSession
    ) -> Union[AIResponse, AsyncGenerator[str, None]]:
        """生成内容"""
        # 构建提示词
        prompt = await self._build_prompt(request, db)
        
        # 构建消息
        messages = [AIMessage(role="user", content=prompt)]
        
        # 选择模型
        model = request.model or AIModelType.GPT_4O_MINI
        
        # 生成内容
        if request.stream:
            return self.ai_manager.generate_stream(
                messages=messages,
                model=model,
                temperature=request.temperature,
                max_tokens=request.max_tokens
            )
        else:
            return await self.ai_manager.generate(
                messages=messages,
                model=model,
                temperature=request.temperature,
                max_tokens=request.max_tokens
            )
    
    async def optimize_content(
        self,
        request: OptimizationRequest,
        db: AsyncSession
    ) -> AIResponse:
        """优化内容"""
        # 构建优化提示词
        prompt = await self._build_optimization_prompt(request, db)
        
        # 构建消息
        messages = [AIMessage(role="user", content=prompt)]
        
        # 选择模型
        model = request.model or AIModelType.GPT_4O
        
        # 生成优化内容
        return await self.ai_manager.generate(
            messages=messages,
            model=model,
            temperature=request.temperature
        )
    
    async def remove_ai_traces(
        self,
        content: str,
        context: GenerationContext,
        db: AsyncSession
    ) -> str:
        """去除AI痕迹"""
        # 构建去AI痕迹的优化请求
        request = OptimizationRequest(
            content=content,
            goals=[OptimizationGoal.REMOVE_AI_TRACES, OptimizationGoal.LANGUAGE_POLISH],
            context=context
        )
        
        response = await self.optimize_content(request, db)
        return response.content
    
    async def adjust_style(
        self,
        content: str,
        target_style: WritingStyle,
        context: GenerationContext,
        db: AsyncSession
    ) -> str:
        """调整写作风格"""
        # 构建风格调整提示词
        prompt = self.prompt_template.format_template(
            ContentType.STYLE_ADJUSTMENT,
            context,
            original_content=content,
            target_style=self.prompt_template._format_writing_style(target_style)
        )
        
        messages = [AIMessage(role="user", content=prompt)]
        
        response = await self.ai_manager.generate(
            messages=messages,
            model=AIModelType.GPT_4O,
            temperature=0.3
        )
        
        return response.content
    
    async def generate_plot_outline(
        self,
        context: GenerationContext,
        db: AsyncSession,
        target_length: int = 2000
    ) -> str:
        """生成情节大纲"""
        context.target_length = target_length
        
        request = GenerationRequest(
            content_type=ContentType.PLOT_OUTLINE,
            context=context,
            temperature=0.8
        )
        
        response = await self.generate_content(request, db)
        return response.content
    
    async def generate_character_profile(
        self,
        character: Character,
        context: GenerationContext,
        db: AsyncSession
    ) -> str:
        """生成角色档案"""
        request = GenerationRequest(
            content_type=ContentType.CHARACTER_PROFILE,
            context=context,
            temperature=0.7
        )
        
        response = await self.generate_content(request, db)
        return response.content
    
    async def generate_scene_description(
        self,
        scene: Scene,
        context: GenerationContext,
        db: AsyncSession,
        target_length: int = 800
    ) -> str:
        """生成场景描述"""
        context.target_length = target_length
        
        request = GenerationRequest(
            content_type=ContentType.SCENE_DESCRIPTION,
            context=context,
            temperature=0.7
        )
        
        response = await self.generate_content(request, db)
        return response.content
    
    async def generate_dialogue(
        self,
        characters: List[Character],
        scene: Scene,
        context: GenerationContext,
        db: AsyncSession
    ) -> str:
        """生成对话"""
        context.characters = characters
        context.scenes = [scene]
        
        request = GenerationRequest(
            content_type=ContentType.DIALOGUE,
            context=context,
            temperature=0.8
        )
        
        response = await self.generate_content(request, db)
        return response.content
    
    async def generate_chapter(
        self,
        chapter: Chapter,
        context: GenerationContext,
        db: AsyncSession
    ) -> str:
        """生成章节内容"""
        context.chapter_id = chapter.id
        context.target_length = chapter.target_word_count or 3000
        
        request = GenerationRequest(
            content_type=ContentType.CHAPTER_CONTENT,
            context=context,
            temperature=0.7
        )
        
        response = await self.generate_content(request, db)
        return response.content
    
    async def continue_story(
        self,
        previous_content: str,
        context: GenerationContext,
        db: AsyncSession,
        target_length: int = 1000
    ) -> str:
        """续写故事"""
        context.previous_content = previous_content
        context.target_length = target_length
        
        request = GenerationRequest(
            content_type=ContentType.STORY_CONTINUATION,
            context=context,
            temperature=0.8
        )
        
        response = await self.generate_content(request, db)
        return response.content
    
    async def _build_prompt(
        self,
        request: GenerationRequest,
        db: AsyncSession
    ) -> str:
        """构建提示词"""
        # 如果有自定义提示词，直接使用
        if request.prompt:
            return request.prompt
        
        # 加载上下文数据
        await self._load_context_data(request.context, db)
        
        # 使用模板生成提示词
        return self.prompt_template.format_template(
            request.content_type,
            request.context
        )
    
    async def _build_optimization_prompt(
        self,
        request: OptimizationRequest,
        db: AsyncSession
    ) -> str:
        """构建优化提示词"""
        # 加载上下文数据
        await self._load_context_data(request.context, db)
        
        # 格式化优化目标
        goal_descriptions = {
            OptimizationGoal.READABILITY: "提升文本的可读性和流畅度",
            OptimizationGoal.ENGAGEMENT: "增强内容的吸引力和趣味性",
            OptimizationGoal.EMOTION: "加强情感表达和感染力",
            OptimizationGoal.PACING: "优化叙述节奏和结构",
            OptimizationGoal.CHARACTER_DEPTH: "深化角色刻画和性格表现",
            OptimizationGoal.PLOT_COHERENCE: "增强情节的逻辑性和连贯性",
            OptimizationGoal.LANGUAGE_POLISH: "润色语言表达和文字技巧",
            OptimizationGoal.REMOVE_AI_TRACES: "消除明显的AI生成痕迹，使文本更自然"
        }
        
        optimization_details = "\n".join(
            f"- {goal_descriptions.get(goal, goal.value)}"
            for goal in request.goals
        )
        
        return self.prompt_template.format_template(
            ContentType.CONTENT_OPTIMIZATION,
            request.context,
            original_content=request.content,
            optimization_goals=", ".join(goal.value for goal in request.goals),
            optimization_details=optimization_details
        )
    
    async def _load_context_data(
        self,
        context: GenerationContext,
        db: AsyncSession
    ):
        """加载上下文数据"""
        # 检查缓存
        if context.project_id in self._context_cache:
            cached_context = self._context_cache[context.project_id]
            if not context.characters:
                context.characters = cached_context.characters
            if not context.scenes:
                context.scenes = cached_context.scenes
            if not context.events:
                context.events = cached_context.events
            return
        
        try:
            # 加载角色数据
            if not context.characters:
                result = await db.execute(
                    select(Character).where(Character.project_id == context.project_id)
                )
                context.characters = result.scalars().all()
            
            # 加载场景数据
            if not context.scenes:
                result = await db.execute(
                    select(Scene).where(Scene.project_id == context.project_id)
                )
                context.scenes = result.scalars().all()
            
            # 加载事件数据
            if not context.events:
                result = await db.execute(
                    select(Event).where(Event.project_id == context.project_id)
                )
                context.events = result.scalars().all()
            
            # 缓存上下文
            self._context_cache[context.project_id] = context
            
        except Exception as e:
            logger.error(f"Failed to load context data: {e}")
    
    def clear_context_cache(self, project_id: Optional[int] = None):
        """清除上下文缓存"""
        if project_id:
            self._context_cache.pop(project_id, None)
        else:
            self._context_cache.clear()
    
    async def get_generation_suggestions(
        self,
        content_type: ContentType,
        context: GenerationContext,
        db: AsyncSession
    ) -> List[str]:
        """获取生成建议"""
        # 根据内容类型和上下文提供生成建议
        suggestions = []
        
        if content_type == ContentType.PLOT_OUTLINE:
            suggestions.extend([
                "考虑添加更多的冲突和转折点",
                "确保主角有清晰的成长弧线",
                "平衡主线和支线情节",
                "营造适当的悬念和张力"
            ])
        elif content_type == ContentType.CHARACTER_PROFILE:
            suggestions.extend([
                "给角色设定独特的说话方式",
                "添加角色的弱点和缺陷",
                "考虑角色的背景故事",
                "设计角色间的复杂关系"
            ])
        elif content_type == ContentType.DIALOGUE:
            suggestions.extend([
                "确保对话符合角色性格",
                "避免过于直白的表达",
                "适当添加动作和表情描写",
                "让对话推进情节发展"
            ])
        
        return suggestions


# 全局内容服务实例
_content_service: Optional[ContentService] = None


async def get_content_service() -> ContentService:
    """获取内容服务实例"""
    global _content_service
    
    if _content_service is None:
        _content_service = ContentService()
        await _content_service.initialize()
    
    return _content_service