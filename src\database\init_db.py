"""数据库初始化和迁移脚本

提供数据库的初始化、表创建、索引优化和初始数据填充功能。
支持数据库版本管理和迁移机制。
"""

import os
import sys
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime

from sqlalchemy import create_engine, text, inspect, Index
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
from alembic.config import Config
from alembic import command
from alembic.runtime.migration import MigrationContext
from alembic.operations import Operations

# 导入所有模型
from ..models.base import BaseTable
from ..models.project import Project, ProjectStatus, ProjectGenre
from ..models.chapter import Chapter, ChapterStatus
from ..models.story_elements import Character, Scene, Event, CharacterType, SceneType, EventType
from ..config.database import get_database_url, get_database_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseInitializer:
    """数据库初始化器
    
    负责数据库的创建、初始化、迁移和数据填充。
    """
    
    def __init__(self, database_url: Optional[str] = None):
        """初始化数据库初始化器
        
        Args:
            database_url: 数据库连接URL，如果不提供则从配置中获取
        """
        self.database_url = database_url or get_database_url()
        self.engine = None
        self.session_factory = None
        self.alembic_cfg = None
        
    def create_engine(self) -> None:
        """创建数据库引擎"""
        try:
            db_config = get_database_config()
            self.engine = create_engine(
                self.database_url,
                echo=db_config.get('echo', False),
                pool_size=db_config.get('pool_size', 10),
                max_overflow=db_config.get('max_overflow', 20),
                pool_timeout=db_config.get('pool_timeout', 30),
                pool_recycle=db_config.get('pool_recycle', 3600)
            )
            self.session_factory = sessionmaker(bind=self.engine)
            logger.info("数据库引擎创建成功")
        except Exception as e:
            logger.error(f"创建数据库引擎失败: {e}")
            raise
    
    def init_alembic(self) -> None:
        """初始化Alembic配置"""
        try:
            # 获取项目根目录
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            alembic_ini_path = os.path.join(project_root, 'alembic.ini')
            
            if os.path.exists(alembic_ini_path):
                self.alembic_cfg = Config(alembic_ini_path)
                self.alembic_cfg.set_main_option('sqlalchemy.url', self.database_url)
                logger.info("Alembic配置初始化成功")
            else:
                logger.warning("未找到alembic.ini文件，跳过Alembic初始化")
        except Exception as e:
            logger.error(f"初始化Alembic配置失败: {e}")
            raise
    
    def create_database(self) -> bool:
        """创建数据库（如果不存在）
        
        Returns:
            bool: 创建成功返回True，否则返回False
        """
        try:
            # 从URL中提取数据库名
            from urllib.parse import urlparse
            parsed_url = urlparse(self.database_url)
            
            if parsed_url.scheme.startswith('postgresql'):
                # PostgreSQL数据库创建
                db_name = parsed_url.path.lstrip('/')
                admin_url = self.database_url.replace(f'/{db_name}', '/postgres')
                
                admin_engine = create_engine(admin_url)
                with admin_engine.connect() as conn:
                    # 检查数据库是否存在
                    result = conn.execute(
                        text("SELECT 1 FROM pg_database WHERE datname = :db_name"),
                        {'db_name': db_name}
                    )
                    
                    if not result.fetchone():
                        # 创建数据库
                        conn.execute(text("COMMIT"))
                        conn.execute(text(f"CREATE DATABASE {db_name}"))
                        logger.info(f"数据库 {db_name} 创建成功")
                    else:
                        logger.info(f"数据库 {db_name} 已存在")
                
                admin_engine.dispose()
                
            elif parsed_url.scheme.startswith('sqlite'):
                # SQLite数据库会自动创建
                logger.info("SQLite数据库将自动创建")
            
            return True
            
        except Exception as e:
            logger.error(f"创建数据库失败: {e}")
            return False
    
    def create_tables(self) -> bool:
        """创建所有数据表
        
        Returns:
            bool: 创建成功返回True，否则返回False
        """
        try:
            if not self.engine:
                self.create_engine()
            
            # 创建所有表
            BaseTable.metadata.create_all(bind=self.engine)
            logger.info("数据表创建成功")
            
            # 创建索引
            self.create_indexes()
            
            return True
            
        except Exception as e:
            logger.error(f"创建数据表失败: {e}")
            return False
    
    def create_indexes(self) -> None:
        """创建数据库索引以优化查询性能"""
        try:
            with self.engine.connect() as conn:
                # 项目表索引
                indexes = [
                    # 项目表
                    Index('idx_projects_status', Project.status),
                    Index('idx_projects_genre', Project.genre),
                    Index('idx_projects_created_at', Project.created_at),
                    Index('idx_projects_updated_at', Project.updated_at),
                    
                    # 章节表
                    Index('idx_chapters_project_id', Chapter.project_id),
                    Index('idx_chapters_status', Chapter.status),
                    Index('idx_chapters_order_index', Chapter.order_index),
                    Index('idx_chapters_created_at', Chapter.created_at),
                    Index('idx_chapters_updated_at', Chapter.updated_at),
                    
                    # 角色表
                    Index('idx_characters_project_id', Character.project_id),
                    Index('idx_characters_type', Character.character_type),
                    Index('idx_characters_importance', Character.importance_level),
                    
                    # 场景表
                    Index('idx_scenes_project_id', Scene.project_id),
                    Index('idx_scenes_type', Scene.scene_type),
                    Index('idx_scenes_importance', Scene.importance_level),
                    
                    # 事件表
                    Index('idx_events_project_id', Event.project_id),
                    Index('idx_events_chapter_id', Event.chapter_id),
                    Index('idx_events_type', Event.event_type),
                    Index('idx_events_sequence', Event.sequence_order),
                    Index('idx_events_importance', Event.importance_level),
                ]
                
                # 创建索引
                for index in indexes:
                    try:
                        index.create(conn, checkfirst=True)
                        logger.info(f"索引 {index.name} 创建成功")
                    except Exception as e:
                        logger.warning(f"创建索引 {index.name} 失败: {e}")
                
                conn.commit()
                logger.info("数据库索引创建完成")
                
        except Exception as e:
            logger.error(f"创建索引失败: {e}")
    
    def populate_initial_data(self) -> bool:
        """填充初始数据
        
        Returns:
            bool: 填充成功返回True，否则返回False
        """
        try:
            if not self.session_factory:
                self.create_engine()
            
            with self.session_factory() as session:
                # 检查是否已有数据
                if session.query(Project).first():
                    logger.info("数据库已有数据，跳过初始数据填充")
                    return True
                
                # 创建示例项目
                sample_project = Project(
                    title="示例小说项目",
                    description="这是一个示例小说项目，用于演示系统功能。",
                    genre=ProjectGenre.FANTASY,
                    status=ProjectStatus.DRAFT,
                    target_word_count=100000,
                    current_word_count=0,
                    target_chapter_count=20,
                    current_chapter_count=0,
                    writing_goal_daily=1000,
                    outline="这是一个关于魔法世界的奇幻小说...",
                    theme="友情、成长、冒险",
                    style_notes="轻松幽默的写作风格",
                    target_audience="青少年读者",
                    created_by="system",
                    updated_by="system"
                )
                
                session.add(sample_project)
                session.flush()  # 获取项目ID
                
                # 创建示例章节
                sample_chapter = Chapter(
                    project_id=sample_project.id,
                    title="第一章：奇遇的开始",
                    content="在一个阳光明媚的早晨，主人公踏上了冒险的旅程...",
                    summary="主人公开始了他的冒险之旅",
                    order_index=1,
                    word_count=500,
                    status=ChapterStatus.DRAFT,
                    created_by="system",
                    updated_by="system"
                )
                
                session.add(sample_chapter)
                session.flush()
                
                # 创建示例角色
                sample_character = Character(
                    project_id=sample_project.id,
                    name="艾伦",
                    nickname="小艾",
                    character_type=CharacterType.PROTAGONIST,
                    age=16,
                    gender="male",
                    occupation="学生",
                    appearance="黑发蓝眼的少年，身材中等",
                    personality="勇敢、善良、有些冲动",
                    background="普通家庭出身，偶然发现了魔法天赋",
                    goals="成为强大的魔法师，保护重要的人",
                    motivations="对魔法的渴望和保护朋友的决心",
                    importance_level=10,
                    created_by="system",
                    updated_by="system"
                )
                
                session.add(sample_character)
                
                # 创建示例场景
                sample_scene = Scene(
                    project_id=sample_project.id,
                    name="魔法学院",
                    scene_type=SceneType.INDOOR,
                    location="艾尔德拉魔法学院",
                    description="一座宏伟的古老城堡，充满了魔法的气息",
                    environment_details="高耸的塔楼、宽敞的大厅、神秘的图书馆",
                    atmosphere="庄严而神秘",
                    time_period="中世纪奇幻",
                    importance_level=9,
                    created_by="system",
                    updated_by="system"
                )
                
                session.add(sample_scene)
                
                # 创建示例事件
                sample_event = Event(
                    project_id=sample_project.id,
                    chapter_id=sample_chapter.id,
                    title="初次施法",
                    event_type=EventType.PLOT,
                    description="主人公第一次成功施展魔法",
                    outcome="成功召唤出一团火焰",
                    impact="增强了主人公的信心",
                    sequence_order=1,
                    importance_level=8,
                    emotional_intensity=7,
                    conflict_level=3,
                    created_by="system",
                    updated_by="system"
                )
                
                session.add(sample_event)
                
                # 提交事务
                session.commit()
                logger.info("初始数据填充成功")
                
                return True
                
        except Exception as e:
            logger.error(f"填充初始数据失败: {e}")
            return False
    
    def run_migrations(self) -> bool:
        """运行数据库迁移
        
        Returns:
            bool: 迁移成功返回True，否则返回False
        """
        try:
            if not self.alembic_cfg:
                self.init_alembic()
            
            if self.alembic_cfg:
                # 运行迁移到最新版本
                command.upgrade(self.alembic_cfg, "head")
                logger.info("数据库迁移完成")
                return True
            else:
                logger.warning("Alembic配置未初始化，跳过迁移")
                return False
                
        except Exception as e:
            logger.error(f"运行数据库迁移失败: {e}")
            return False
    
    def check_database_version(self) -> Optional[str]:
        """检查数据库版本
        
        Returns:
            Optional[str]: 当前数据库版本，如果无法获取则返回None
        """
        try:
            if not self.engine:
                self.create_engine()
            
            with self.engine.connect() as conn:
                context = MigrationContext.configure(conn)
                current_rev = context.get_current_revision()
                return current_rev
                
        except Exception as e:
            logger.error(f"检查数据库版本失败: {e}")
            return None
    
    def reset_database(self) -> bool:
        """重置数据库（删除所有表并重新创建）
        
        Returns:
            bool: 重置成功返回True，否则返回False
        """
        try:
            if not self.engine:
                self.create_engine()
            
            # 删除所有表
            BaseTable.metadata.drop_all(bind=self.engine)
            logger.info("数据库表删除成功")
            
            # 重新创建表
            return self.create_tables()
            
        except Exception as e:
            logger.error(f"重置数据库失败: {e}")
            return False
    
    def initialize_database(self, reset: bool = False, populate_data: bool = True) -> bool:
        """完整的数据库初始化流程
        
        Args:
            reset: 是否重置数据库
            populate_data: 是否填充初始数据
            
        Returns:
            bool: 初始化成功返回True，否则返回False
        """
        try:
            logger.info("开始数据库初始化...")
            
            # 创建数据库引擎
            self.create_engine()
            
            # 初始化Alembic
            self.init_alembic()
            
            # 创建数据库
            if not self.create_database():
                return False
            
            # 重置数据库（如果需要）
            if reset:
                if not self.reset_database():
                    return False
            else:
                # 创建表
                if not self.create_tables():
                    return False
            
            # 运行迁移
            if not self.run_migrations():
                logger.warning("数据库迁移失败，但继续初始化流程")
            
            # 填充初始数据
            if populate_data:
                if not self.populate_initial_data():
                    logger.warning("初始数据填充失败，但数据库初始化完成")
            
            logger.info("数据库初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            return False
        finally:
            # 清理资源
            if self.engine:
                self.engine.dispose()

def init_database(database_url: Optional[str] = None, reset: bool = False, populate_data: bool = True) -> bool:
    """数据库初始化的便捷函数
    
    Args:
        database_url: 数据库连接URL
        reset: 是否重置数据库
        populate_data: 是否填充初始数据
        
    Returns:
        bool: 初始化成功返回True，否则返回False
    """
    initializer = DatabaseInitializer(database_url)
    return initializer.initialize_database(reset=reset, populate_data=populate_data)

def main():
    """命令行入口函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='数据库初始化脚本')
    parser.add_argument('--database-url', help='数据库连接URL')
    parser.add_argument('--reset', action='store_true', help='重置数据库')
    parser.add_argument('--no-data', action='store_true', help='不填充初始数据')
    parser.add_argument('--check-version', action='store_true', help='检查数据库版本')
    
    args = parser.parse_args()
    
    if args.check_version:
        initializer = DatabaseInitializer(args.database_url)
        initializer.create_engine()
        version = initializer.check_database_version()
        if version:
            print(f"当前数据库版本: {version}")
        else:
            print("无法获取数据库版本")
        return
    
    # 执行数据库初始化
    success = init_database(
        database_url=args.database_url,
        reset=args.reset,
        populate_data=not args.no_data
    )
    
    if success:
        print("数据库初始化成功")
        sys.exit(0)
    else:
        print("数据库初始化失败")
        sys.exit(1)

if __name__ == '__main__':
    main()