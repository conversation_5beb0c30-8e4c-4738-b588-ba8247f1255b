"""OpenAI适配器实现

提供OpenAI API的适配器实现，支持GPT-3.5和GPT-4模型。
实现聊天完成、文本生成、内容优化等功能。
"""

import asyncio
import logging
from typing import Dict, List, Optional, AsyncGenerator, Any
from decimal import Decimal

try:
    import openai
    from openai import AsyncOpenAI
except ImportError:
    openai = None
    AsyncOpenAI = None

from .base_adapter import (
    BaseAIAdapter,
    AIModelType,
    AITaskType,
    AIMessage,
    AIRequest,
    AIResponse,
    AIResponseStatus,
    AIUsageStats,
    AIServiceError,
    AIRateLimitError,
    AIQuotaExceededError,
    AIModelError,
    AINetworkError
)

logger = logging.getLogger(__name__)


class OpenAIAdapter(BaseAIAdapter):
    """OpenAI API适配器
    
    支持GPT-3.5-turbo、GPT-4等模型的API调用。
    """
    
    # OpenAI模型配置
    MODEL_CONFIGS = {
        AIModelType.GPT_3_5_TURBO: {
            "name": "gpt-3.5-turbo",
            "max_tokens": 4096,
            "context_window": 16385,
            "cost_per_1k_input": Decimal("0.0015"),
            "cost_per_1k_output": Decimal("0.002")
        },
        AIModelType.GPT_3_5_TURBO_16K: {
            "name": "gpt-3.5-turbo-16k",
            "max_tokens": 4096,
            "context_window": 16385,
            "cost_per_1k_input": Decimal("0.003"),
            "cost_per_1k_output": Decimal("0.004")
        },
        AIModelType.GPT_4: {
            "name": "gpt-4",
            "max_tokens": 8192,
            "context_window": 8192,
            "cost_per_1k_input": Decimal("0.03"),
            "cost_per_1k_output": Decimal("0.06")
        },
        AIModelType.GPT_4_TURBO: {
            "name": "gpt-4-turbo-preview",
            "max_tokens": 4096,
            "context_window": 128000,
            "cost_per_1k_input": Decimal("0.01"),
            "cost_per_1k_output": Decimal("0.03")
        },
        AIModelType.GPT_4O: {
            "name": "gpt-4o",
            "max_tokens": 4096,
            "context_window": 128000,
            "cost_per_1k_input": Decimal("0.005"),
            "cost_per_1k_output": Decimal("0.015")
        }
    }
    
    def __init__(self, api_key: str, base_url: Optional[str] = None, 
                 organization: Optional[str] = None, **kwargs):
        """初始化OpenAI适配器
        
        Args:
            api_key: OpenAI API密钥
            base_url: 自定义API基础URL
            organization: 组织ID
            **kwargs: 其他配置参数
        """
        if not openai or not AsyncOpenAI:
            raise ImportError("请安装openai库: pip install openai")
            
        super().__init__(**kwargs)
        
        self.api_key = api_key
        self.base_url = base_url
        self.organization = organization
        
        # 初始化OpenAI客户端
        client_kwargs = {
            "api_key": api_key,
        }
        if base_url:
            client_kwargs["base_url"] = base_url
        if organization:
            client_kwargs["organization"] = organization
            
        self.client = AsyncOpenAI(**client_kwargs)
        
        logger.info(f"OpenAI适配器初始化完成，支持模型: {list(self.MODEL_CONFIGS.keys())}")
    
    @property
    def supported_models(self) -> List[AIModelType]:
        """获取支持的模型列表"""
        return list(self.MODEL_CONFIGS.keys())
    
    @property
    def supported_tasks(self) -> List[AITaskType]:
        """获取支持的任务类型"""
        return [
            AITaskType.TEXT_GENERATION,
            AITaskType.CREATIVE_WRITING,
            AITaskType.DIALOGUE_GENERATION,
            AITaskType.CONTENT_OPTIMIZATION,
            AITaskType.CONTENT_EXPANSION,
            AITaskType.CONTENT_SUMMARIZATION,
            AITaskType.STYLE_TRANSFER,
            AITaskType.GRAMMAR_CHECK,
            AITaskType.TRANSLATION,
            AITaskType.BRAINSTORMING,
            AITaskType.QUESTION_ANSWERING,
            AITaskType.CODE_GENERATION,
            AITaskType.ANALYSIS
        ]
    
    def _get_model_config(self, model_type: AIModelType) -> Dict[str, Any]:
        """获取模型配置"""
        if model_type not in self.MODEL_CONFIGS:
            raise AIModelError(f"不支持的模型类型: {model_type}")
        return self.MODEL_CONFIGS[model_type]
    
    def _convert_messages(self, messages: List[AIMessage]) -> List[Dict[str, str]]:
        """转换消息格式为OpenAI格式"""
        openai_messages = []
        
        for msg in messages:
            role_mapping = {
                "user": "user",
                "assistant": "assistant",
                "system": "system"
            }
            
            openai_messages.append({
                "role": role_mapping.get(msg.role, "user"),
                "content": msg.content
            })
        
        return openai_messages
    
    def _handle_openai_error(self, error: Exception) -> AIServiceError:
        """处理OpenAI API错误"""
        error_message = str(error)
        
        if "rate_limit" in error_message.lower():
            return AIRateLimitError(f"OpenAI API限流: {error_message}")
        elif "quota" in error_message.lower() or "billing" in error_message.lower():
            return AIQuotaExceededError(f"OpenAI配额不足: {error_message}")
        elif "model" in error_message.lower() or "invalid" in error_message.lower():
            return AIModelError(f"OpenAI模型错误: {error_message}")
        elif "network" in error_message.lower() or "connection" in error_message.lower():
            return AINetworkError(f"OpenAI网络错误: {error_message}")
        else:
            return AIServiceError(f"OpenAI服务错误: {error_message}")
    
    async def _make_request(self, request: AIRequest) -> AIResponse:
        """发送请求到OpenAI API"""
        try:
            model_config = self._get_model_config(request.model_type)
            openai_messages = self._convert_messages(request.messages)
            
            # 构建请求参数
            request_params = {
                "model": model_config["name"],
                "messages": openai_messages,
                "max_tokens": min(request.max_tokens or model_config["max_tokens"], 
                                model_config["max_tokens"]),
                "temperature": request.temperature,
                "top_p": request.top_p,
                "stream": request.stream
            }
            
            # 添加其他参数
            if request.stop_sequences:
                request_params["stop"] = request.stop_sequences
            if request.presence_penalty is not None:
                request_params["presence_penalty"] = request.presence_penalty
            if request.frequency_penalty is not None:
                request_params["frequency_penalty"] = request.frequency_penalty
            
            logger.debug(f"发送OpenAI请求: {request_params['model']}, tokens: {request_params['max_tokens']}")
            
            # 发送请求
            if request.stream:
                return await self._handle_stream_response(request_params, model_config)
            else:
                response = await self.client.chat.completions.create(**request_params)
                return self._parse_response(response, model_config)
                
        except Exception as e:
            logger.error(f"OpenAI请求失败: {e}")
            raise self._handle_openai_error(e)
    
    async def _handle_stream_response(self, request_params: Dict[str, Any], 
                                    model_config: Dict[str, Any]) -> AIResponse:
        """处理流式响应"""
        try:
            stream = await self.client.chat.completions.create(**request_params)
            
            content_parts = []
            usage_stats = AIUsageStats()
            
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    content_parts.append(chunk.choices[0].delta.content)
                    yield chunk.choices[0].delta.content
            
            # 构建完整响应
            full_content = "".join(content_parts)
            
            # 估算使用量（流式响应通常不返回准确的token计数）
            estimated_input_tokens = sum(len(msg.get("content", "")) for msg in request_params["messages"]) // 4
            estimated_output_tokens = len(full_content) // 4
            
            usage_stats.input_tokens = estimated_input_tokens
            usage_stats.output_tokens = estimated_output_tokens
            usage_stats.total_tokens = estimated_input_tokens + estimated_output_tokens
            
            # 计算成本
            input_cost = (Decimal(estimated_input_tokens) / 1000) * model_config["cost_per_1k_input"]
            output_cost = (Decimal(estimated_output_tokens) / 1000) * model_config["cost_per_1k_output"]
            usage_stats.total_cost = input_cost + output_cost
            
            return AIResponse(
                content=full_content,
                status=AIResponseStatus.SUCCESS,
                model_type=request_params.get("model_type"),
                usage_stats=usage_stats,
                metadata={
                    "model": request_params["model"],
                    "stream": True,
                    "estimated_tokens": True
                }
            )
            
        except Exception as e:
            logger.error(f"OpenAI流式响应处理失败: {e}")
            raise self._handle_openai_error(e)
    
    def _parse_response(self, response, model_config: Dict[str, Any]) -> AIResponse:
        """解析OpenAI响应"""
        try:
            choice = response.choices[0]
            content = choice.message.content
            
            # 解析使用统计
            usage_stats = AIUsageStats()
            if hasattr(response, 'usage') and response.usage:
                usage_stats.input_tokens = response.usage.prompt_tokens
                usage_stats.output_tokens = response.usage.completion_tokens
                usage_stats.total_tokens = response.usage.total_tokens
                
                # 计算成本
                input_cost = (Decimal(usage_stats.input_tokens) / 1000) * model_config["cost_per_1k_input"]
                output_cost = (Decimal(usage_stats.output_tokens) / 1000) * model_config["cost_per_1k_output"]
                usage_stats.total_cost = input_cost + output_cost
            
            return AIResponse(
                content=content,
                status=AIResponseStatus.SUCCESS,
                model_type=response.model,
                usage_stats=usage_stats,
                metadata={
                    "model": response.model,
                    "finish_reason": choice.finish_reason,
                    "response_id": response.id
                }
            )
            
        except Exception as e:
            logger.error(f"解析OpenAI响应失败: {e}")
            raise AIServiceError(f"响应解析错误: {e}")
    
    async def generate_stream(self, request: AIRequest) -> AsyncGenerator[str, None]:
        """生成流式文本"""
        try:
            model_config = self._get_model_config(request.model_type)
            openai_messages = self._convert_messages(request.messages)
            
            request_params = {
                "model": model_config["name"],
                "messages": openai_messages,
                "max_tokens": min(request.max_tokens or model_config["max_tokens"], 
                                model_config["max_tokens"]),
                "temperature": request.temperature,
                "top_p": request.top_p,
                "stream": True
            }
            
            if request.stop_sequences:
                request_params["stop"] = request.stop_sequences
            
            logger.debug(f"开始OpenAI流式生成: {request_params['model']}")
            
            stream = await self.client.chat.completions.create(**request_params)
            
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"OpenAI流式生成失败: {e}")
            raise self._handle_openai_error(e)
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            # 发送简单的测试请求
            test_request = AIRequest(
                messages=[AIMessage(role="user", content="Hello")],
                model_type=AIModelType.GPT_3_5_TURBO,
                max_tokens=5,
                temperature=0.1
            )
            
            response = await self.generate(test_request)
            return response.status == AIResponseStatus.SUCCESS
            
        except Exception as e:
            logger.warning(f"OpenAI健康检查失败: {e}")
            return False
    
    def get_model_info(self, model_type: AIModelType) -> Dict[str, Any]:
        """获取模型信息"""
        if model_type not in self.MODEL_CONFIGS:
            raise AIModelError(f"不支持的模型类型: {model_type}")
        
        config = self.MODEL_CONFIGS[model_type]
        return {
            "name": config["name"],
            "max_tokens": config["max_tokens"],
            "context_window": config["context_window"],
            "cost_per_1k_input": float(config["cost_per_1k_input"]),
            "cost_per_1k_output": float(config["cost_per_1k_output"]),
            "provider": "OpenAI",
            "type": "chat"
        }


# 创建默认实例（需要在使用时提供API密钥）
def create_openai_adapter(api_key: str, **kwargs) -> OpenAIAdapter:
    """创建OpenAI适配器实例
    
    Args:
        api_key: OpenAI API密钥
        **kwargs: 其他配置参数
    
    Returns:
        OpenAI适配器实例
    """
    return OpenAIAdapter(api_key=api_key, **kwargs)