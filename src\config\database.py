"""数据库配置模块

提供数据库连接配置和相关工具函数。
支持多种数据库类型和环境配置。
"""

import os
from typing import Dict, Any, Optional
from urllib.parse import quote_plus

def get_database_url(env: str = None) -> str:
    """获取数据库连接URL
    
    Args:
        env: 环境名称（development, testing, production）
        
    Returns:
        str: 数据库连接URL
    """
    env = env or os.getenv('ENVIRONMENT', 'development')
    
    # 从环境变量获取数据库配置
    db_type = os.getenv('DB_TYPE', 'sqlite')
    
    if db_type.lower() == 'postgresql':
        # PostgreSQL配置
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '5432')
        db_name = os.getenv('DB_NAME', 'novel_writing_db')
        db_user = os.getenv('DB_USER', 'postgres')
        db_password = os.getenv('DB_PASSWORD', '')
        
        # URL编码密码以处理特殊字符
        encoded_password = quote_plus(db_password) if db_password else ''
        
        if encoded_password:
            return f"postgresql://{db_user}:{encoded_password}@{db_host}:{db_port}/{db_name}"
        else:
            return f"postgresql://{db_user}@{db_host}:{db_port}/{db_name}"
    
    elif db_type.lower() == 'mysql':
        # MySQL配置
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '3306')
        db_name = os.getenv('DB_NAME', 'novel_writing_db')
        db_user = os.getenv('DB_USER', 'root')
        db_password = os.getenv('DB_PASSWORD', '')
        
        # URL编码密码以处理特殊字符
        encoded_password = quote_plus(db_password) if db_password else ''
        
        if encoded_password:
            return f"mysql+pymysql://{db_user}:{encoded_password}@{db_host}:{db_port}/{db_name}?charset=utf8mb4"
        else:
            return f"mysql+pymysql://{db_user}@{db_host}:{db_port}/{db_name}?charset=utf8mb4"
    
    else:
        # SQLite配置（默认）
        db_path = os.getenv('DB_PATH')
        if not db_path:
            # 默认数据库路径
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            db_dir = os.path.join(project_root, 'data')
            os.makedirs(db_dir, exist_ok=True)
            
            if env == 'testing':
                db_path = os.path.join(db_dir, 'test_novel_writing.db')
            else:
                db_path = os.path.join(db_dir, 'novel_writing.db')
        
        return f"sqlite:///{db_path}"

def get_database_config(env: str = None) -> Dict[str, Any]:
    """获取数据库配置参数
    
    Args:
        env: 环境名称
        
    Returns:
        Dict[str, Any]: 数据库配置字典
    """
    env = env or os.getenv('ENVIRONMENT', 'development')
    
    # 基础配置
    config = {
        'echo': env == 'development',  # 开发环境显示SQL语句
        'pool_size': int(os.getenv('DB_POOL_SIZE', '10')),
        'max_overflow': int(os.getenv('DB_MAX_OVERFLOW', '20')),
        'pool_timeout': int(os.getenv('DB_POOL_TIMEOUT', '30')),
        'pool_recycle': int(os.getenv('DB_POOL_RECYCLE', '3600')),
        'pool_pre_ping': True,  # 连接前检查连接是否有效
    }
    
    # 根据数据库类型调整配置
    db_type = os.getenv('DB_TYPE', 'sqlite')
    
    if db_type.lower() == 'sqlite':
        # SQLite特殊配置
        config.update({
            'pool_size': 0,  # SQLite不支持连接池
            'max_overflow': 0,
            'pool_timeout': 0,
            'pool_recycle': -1,
            'pool_pre_ping': False,
            'connect_args': {
                'check_same_thread': False,  # 允许多线程访问
                'timeout': 20  # 数据库锁定超时
            }
        })
    
    elif db_type.lower() in ['postgresql', 'mysql']:
        # PostgreSQL/MySQL特殊配置
        config.update({
            'connect_args': {
                'connect_timeout': int(os.getenv('DB_CONNECT_TIMEOUT', '10')),
            }
        })
        
        if db_type.lower() == 'postgresql':
            config['connect_args'].update({
                'server_side_cursors': True,  # 使用服务器端游标
            })
    
    return config

def get_alembic_config() -> Dict[str, Any]:
    """获取Alembic迁移配置
    
    Returns:
        Dict[str, Any]: Alembic配置字典
    """
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    
    return {
        'script_location': os.path.join(project_root, 'alembic'),
        'sqlalchemy.url': get_database_url(),
        'file_template': '%%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d-%%(rev)s_%%(slug)s',
        'timezone': 'UTC',
        'truncate_slug_length': 40,
        'version_locations': os.path.join(project_root, 'alembic', 'versions'),
    }

def validate_database_config() -> bool:
    """验证数据库配置是否正确
    
    Returns:
        bool: 配置有效返回True，否则返回False
    """
    try:
        # 检查数据库URL是否可以生成
        url = get_database_url()
        if not url:
            return False
        
        # 检查配置参数是否合理
        config = get_database_config()
        
        # 验证连接池配置
        if config.get('pool_size', 0) < 0:
            return False
        
        if config.get('max_overflow', 0) < 0:
            return False
        
        if config.get('pool_timeout', 0) < 0:
            return False
        
        return True
        
    except Exception:
        return False

def get_test_database_url() -> str:
    """获取测试数据库URL
    
    Returns:
        str: 测试数据库连接URL
    """
    # 临时设置环境为测试
    original_env = os.getenv('ENVIRONMENT')
    os.environ['ENVIRONMENT'] = 'testing'
    
    try:
        return get_database_url('testing')
    finally:
        # 恢复原始环境设置
        if original_env:
            os.environ['ENVIRONMENT'] = original_env
        else:
            os.environ.pop('ENVIRONMENT', None)

def create_database_url(
    db_type: str,
    host: str = 'localhost',
    port: Optional[int] = None,
    database: str = 'novel_writing_db',
    username: Optional[str] = None,
    password: Optional[str] = None,
    **kwargs
) -> str:
    """创建数据库连接URL
    
    Args:
        db_type: 数据库类型（sqlite, postgresql, mysql）
        host: 数据库主机
        port: 数据库端口
        database: 数据库名称
        username: 用户名
        password: 密码
        **kwargs: 其他连接参数
        
    Returns:
        str: 数据库连接URL
    """
    db_type = db_type.lower()
    
    if db_type == 'sqlite':
        # SQLite URL
        if database.startswith('/'):
            # 绝对路径
            return f"sqlite:///{database}"
        else:
            # 相对路径或文件名
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            db_path = os.path.join(project_root, 'data', database)
            return f"sqlite:///{db_path}"
    
    elif db_type == 'postgresql':
        # PostgreSQL URL
        port = port or 5432
        if username and password:
            encoded_password = quote_plus(password)
            url = f"postgresql://{username}:{encoded_password}@{host}:{port}/{database}"
        elif username:
            url = f"postgresql://{username}@{host}:{port}/{database}"
        else:
            url = f"postgresql://{host}:{port}/{database}"
        
        # 添加额外参数
        if kwargs:
            params = '&'.join([f"{k}={v}" for k, v in kwargs.items()])
            url += f"?{params}"
        
        return url
    
    elif db_type == 'mysql':
        # MySQL URL
        port = port or 3306
        if username and password:
            encoded_password = quote_plus(password)
            url = f"mysql+pymysql://{username}:{encoded_password}@{host}:{port}/{database}"
        elif username:
            url = f"mysql+pymysql://{username}@{host}:{port}/{database}"
        else:
            url = f"mysql+pymysql://{host}:{port}/{database}"
        
        # 默认添加字符集参数
        params = ['charset=utf8mb4']
        if kwargs:
            params.extend([f"{k}={v}" for k, v in kwargs.items()])
        
        url += f"?{'&'.join(params)}"
        return url
    
    else:
        raise ValueError(f"不支持的数据库类型: {db_type}")

# 导出的函数
__all__ = [
    'get_database_url',
    'get_database_config',
    'get_alembic_config',
    'validate_database_config',
    'get_test_database_url',
    'create_database_url'
]