"""数据模型基础类

提供所有数据模型的统一基础，包括：
- SQLAlchemy基础模型类
- 通用字段定义
- 模型验证和序列化方法
- 数据库会话管理
"""

import uuid
from datetime import datetime
from typing import Any, Dict, Optional, Type, TypeVar

from sqlalchemy import Column, String, DateTime, Boolean, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from pydantic import BaseModel, Field, ConfigDict

# SQLAlchemy基础模型
Base = declarative_base()

# Pydantic模型类型变量
T = TypeVar('T', bound=BaseModel)

class BaseTable(Base):
    """SQLAlchemy基础表模型
    
    所有数据表都应继承此类，提供统一的基础字段和方法。
    """
    __abstract__ = True
    
    # 主键ID - 使用UUID确保唯一性
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        comment="主键ID"
    )
    
    # 创建时间
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    
    # 更新时间
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # 软删除标记
    is_deleted = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否已删除"
    )
    
    # 创建者ID（可选）
    created_by = Column(
        String(255),
        nullable=True,
        comment="创建者"
    )
    
    # 更新者ID（可选）
    updated_by = Column(
        String(255),
        nullable=True,
        comment="更新者"
    )
    
    # 备注字段
    remarks = Column(
        Text,
        nullable=True,
        comment="备注"
    )
    
    def to_dict(self, exclude_fields: Optional[list] = None) -> Dict[str, Any]:
        """将模型转换为字典
        
        Args:
            exclude_fields: 要排除的字段列表
            
        Returns:
            模型的字典表示
        """
        exclude_fields = exclude_fields or []
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude_fields:
                value = getattr(self, column.name)
                # 处理特殊类型
                if isinstance(value, datetime):
                    result[column.name] = value.isoformat()
                elif isinstance(value, uuid.UUID):
                    result[column.name] = str(value)
                else:
                    result[column.name] = value
                    
        return result
    
    def update_from_dict(self, data: Dict[str, Any], exclude_fields: Optional[list] = None) -> None:
        """从字典更新模型属性
        
        Args:
            data: 包含更新数据的字典
            exclude_fields: 要排除的字段列表
        """
        exclude_fields = exclude_fields or ['id', 'created_at']
        
        for key, value in data.items():
            if key not in exclude_fields and hasattr(self, key):
                setattr(self, key, value)
    
    def soft_delete(self, deleted_by: Optional[str] = None) -> None:
        """软删除记录
        
        Args:
            deleted_by: 删除操作者
        """
        self.is_deleted = True
        self.updated_by = deleted_by
        self.updated_at = datetime.utcnow()
    
    def restore(self, restored_by: Optional[str] = None) -> None:
        """恢复已删除的记录
        
        Args:
            restored_by: 恢复操作者
        """
        self.is_deleted = False
        self.updated_by = restored_by
        self.updated_at = datetime.utcnow()
    
    @classmethod
    def get_table_name(cls) -> str:
        """获取表名"""
        return cls.__tablename__
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<{self.__class__.__name__}(id={self.id})>"

class BaseSchema(BaseModel):
    """Pydantic基础模式类
    
    所有API数据传输对象都应继承此类。
    """
    model_config = ConfigDict(
        # 允许从ORM对象创建
        from_attributes=True,
        # 验证赋值
        validate_assignment=True,
        # 使用枚举值
        use_enum_values=True,
        # 允许额外字段
        extra='forbid'
    )
    
    @classmethod
    def from_orm_list(cls: Type[T], orm_objects: list) -> list[T]:
        """从ORM对象列表创建Pydantic模型列表
        
        Args:
            orm_objects: ORM对象列表
            
        Returns:
            Pydantic模型列表
        """
        return [cls.model_validate(obj) for obj in orm_objects]

class TimestampMixin(BaseModel):
    """时间戳混入类"""
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

class SoftDeleteMixin(BaseModel):
    """软删除混入类"""
    is_deleted: bool = Field(default=False, description="是否已删除")

class UserTrackingMixin(BaseModel):
    """用户跟踪混入类"""
    created_by: Optional[str] = Field(None, description="创建者")
    updated_by: Optional[str] = Field(None, description="更新者")

class BaseResponseSchema(BaseSchema):
    """基础响应模式
    
    包含所有响应都应该有的基础字段。
    """
    id: uuid.UUID = Field(..., description="主键ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    is_deleted: bool = Field(default=False, description="是否已删除")
    created_by: Optional[str] = Field(None, description="创建者")
    updated_by: Optional[str] = Field(None, description="更新者")
    remarks: Optional[str] = Field(None, description="备注")

class BaseCreateSchema(BaseSchema):
    """基础创建模式
    
    用于创建操作的基础字段。
    """
    created_by: Optional[str] = Field(None, description="创建者")
    remarks: Optional[str] = Field(None, description="备注")

class BaseUpdateSchema(BaseSchema):
    """基础更新模式
    
    用于更新操作的基础字段。
    """
    updated_by: Optional[str] = Field(None, description="更新者")
    remarks: Optional[str] = Field(None, description="备注")

# 导出的类和函数
__all__ = [
    "Base",
    "BaseTable",
    "BaseSchema",
    "BaseResponseSchema",
    "BaseCreateSchema",
    "BaseUpdateSchema",
    "TimestampMixin",
    "SoftDeleteMixin",
    "UserTrackingMixin"
]