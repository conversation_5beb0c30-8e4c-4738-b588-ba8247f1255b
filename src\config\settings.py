"""应用配置管理模块

提供统一的配置管理，支持环境变量和配置文件。
包含数据库连接、AI服务、UI主题等配置。
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class DatabaseConfig(BaseSettings):
    """数据库配置"""
    
    # SQLite配置
    sqlite_path: str = Field(default="data/bamboofall.db", description="SQLite数据库文件路径")
    
    # PostgreSQL配置（可选）
    postgres_host: Optional[str] = Field(default=None, description="PostgreSQL主机")
    postgres_port: int = Field(default=5432, description="PostgreSQL端口")
    postgres_user: Optional[str] = Field(default=None, description="PostgreSQL用户名")
    postgres_password: Optional[str] = Field(default=None, description="PostgreSQL密码")
    postgres_database: Optional[str] = Field(default=None, description="PostgreSQL数据库名")
    
    # 连接池配置
    pool_size: int = Field(default=10, description="连接池大小")
    max_overflow: int = Field(default=20, description="最大溢出连接数")
    pool_timeout: int = Field(default=30, description="连接池超时时间（秒）")
    
    class Config:
        env_prefix = "DB_"

class AIServiceConfig(BaseSettings):
    """AI服务配置"""
    
    # OpenAI配置
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API密钥")
    openai_base_url: str = Field(default="https://api.openai.com/v1", description="OpenAI API基础URL")
    openai_model: str = Field(default="gpt-3.5-turbo", description="默认OpenAI模型")
    
    # Claude配置
    claude_api_key: Optional[str] = Field(default=None, description="Claude API密钥")
    claude_base_url: str = Field(default="https://api.anthropic.com", description="Claude API基础URL")
    claude_model: str = Field(default="claude-3-sonnet-20240229", description="默认Claude模型")
    
    # 通义千问配置
    qwen_api_key: Optional[str] = Field(default=None, description="通义千问API密钥")
    qwen_base_url: str = Field(default="https://dashscope.aliyuncs.com/api/v1", description="通义千问API基础URL")
    qwen_model: str = Field(default="qwen-turbo", description="默认通义千问模型")
    
    # 文心一言配置
    ernie_api_key: Optional[str] = Field(default=None, description="文心一言API密钥")
    ernie_secret_key: Optional[str] = Field(default=None, description="文心一言Secret密钥")
    ernie_model: str = Field(default="ernie-bot-turbo", description="默认文心一言模型")
    
    # 通用配置
    request_timeout: int = Field(default=60, description="请求超时时间（秒）")
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟（秒）")
    
    class Config:
        env_prefix = "AI_"

class UIConfig(BaseSettings):
    """UI配置"""
    
    # 主题配置
    theme: str = Field(default="light", description="UI主题（light/dark）")
    primary_color: str = Field(default="#2196F3", description="主色调")
    secondary_color: str = Field(default="#FFC107", description="辅助色")
    
    # 窗口配置
    window_width: int = Field(default=1200, description="窗口宽度")
    window_height: int = Field(default=800, description="窗口高度")
    window_resizable: bool = Field(default=True, description="窗口是否可调整大小")
    
    # 字体配置
    font_family: str = Field(default="Microsoft YaHei", description="字体族")
    font_size: int = Field(default=12, description="字体大小")
    
    # 编辑器配置
    editor_font_family: str = Field(default="Consolas", description="编辑器字体族")
    editor_font_size: int = Field(default=14, description="编辑器字体大小")
    editor_line_height: float = Field(default=1.5, description="编辑器行高")
    
    class Config:
        env_prefix = "UI_"

class AppConfig(BaseSettings):
    """应用主配置"""
    
    # 应用基本信息
    app_name: str = Field(default="笔落 - AI小说创作平台", description="应用名称")
    app_version: str = Field(default="1.0.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    
    # 数据目录
    data_dir: str = Field(default="data", description="数据目录")
    logs_dir: str = Field(default="logs", description="日志目录")
    temp_dir: str = Field(default="temp", description="临时目录")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )
    log_max_size: int = Field(default=10, description="单个日志文件最大大小（MB）")
    log_backup_count: int = Field(default=5, description="日志文件备份数量")
    
    # 安全配置
    secret_key: str = Field(default="your-secret-key-here", description="应用密钥")
    
    # 子配置
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    ai_service: AIServiceConfig = Field(default_factory=AIServiceConfig)
    ui: UIConfig = Field(default_factory=UIConfig)
    
    class Config:
        env_prefix = "APP_"
        case_sensitive = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [self.data_dir, self.logs_dir, self.temp_dir]
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    @property
    def sqlite_url(self) -> str:
        """获取SQLite连接URL"""
        return f"sqlite:///{self.database.sqlite_path}"
    
    @property
    def postgres_url(self) -> Optional[str]:
        """获取PostgreSQL连接URL"""
        if not all([
            self.database.postgres_host,
            self.database.postgres_user,
            self.database.postgres_password,
            self.database.postgres_database
        ]):
            return None
        
        return (
            f"postgresql://{self.database.postgres_user}:"
            f"{self.database.postgres_password}@{self.database.postgres_host}:"
            f"{self.database.postgres_port}/{self.database.postgres_database}"
        )
    
    def get_ai_config(self, provider: str) -> Dict[str, Any]:
        """获取指定AI服务提供商的配置"""
        provider_configs = {
            "openai": {
                "api_key": self.ai_service.openai_api_key,
                "base_url": self.ai_service.openai_base_url,
                "model": self.ai_service.openai_model,
            },
            "claude": {
                "api_key": self.ai_service.claude_api_key,
                "base_url": self.ai_service.claude_base_url,
                "model": self.ai_service.claude_model,
            },
            "qwen": {
                "api_key": self.ai_service.qwen_api_key,
                "base_url": self.ai_service.qwen_base_url,
                "model": self.ai_service.qwen_model,
            },
            "ernie": {
                "api_key": self.ai_service.ernie_api_key,
                "secret_key": self.ai_service.ernie_secret_key,
                "model": self.ai_service.ernie_model,
            },
        }
        
        config = provider_configs.get(provider, {})
        config.update({
            "timeout": self.ai_service.request_timeout,
            "max_retries": self.ai_service.max_retries,
            "retry_delay": self.ai_service.retry_delay,
        })
        
        return config

# 全局配置实例
settings = AppConfig()

# 导出配置类和实例
__all__ = [
    "DatabaseConfig",
    "AIServiceConfig", 
    "UIConfig",
    "AppConfig",
    "settings"
]