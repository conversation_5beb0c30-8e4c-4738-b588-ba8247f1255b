"""服务层单元测试

测试所有服务类的功能和业务逻辑。
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime, timedelta
from pathlib import Path
import tempfile
import json
import asyncio
from typing import List, Dict, Any

# 导入要测试的服务
from src.services.project_service import ProjectService
from src.services.story_service import StoryService
from src.services.ai_service import AIService
from src.services.export_service import ExportService
from src.services.backup_service import BackupService
from src.services.user_service import UserService
from src.services.search_service import SearchService

# 导入模型
from src.models.project import Project, ProjectType, ProjectStatus
from src.models.story import Story, Chapter, Character, Scene
from src.models.ai_service import AIRequest, AIResponse, AIFunction, ProcessStatus
from src.models.user import User, UserPreferences

# 导入异常
from src.core.exceptions import (
    ProjectNotFoundError, StoryNotFoundError, AIServiceError,
    ExportError, BackupError, ValidationError
)


class TestProjectService:
    """项目服务测试"""
    
    @pytest.fixture
    def project_service(self):
        """项目服务fixture"""
        return ProjectService()
    
    @pytest.fixture
    def sample_project_data(self):
        """示例项目数据"""
        return {
            "name": "测试项目",
            "description": "这是一个测试项目",
            "author": "测试作者",
            "project_type": ProjectType.NOVEL,
            "target_word_count": 50000
        }
    
    def test_create_project(self, project_service, sample_project_data):
        """测试创建项目"""
        project = project_service.create_project(**sample_project_data)
        
        assert isinstance(project, Project)
        assert project.name == sample_project_data["name"]
        assert project.author == sample_project_data["author"]
        assert project.status == ProjectStatus.ACTIVE
        assert project.id is not None
    
    def test_get_project(self, project_service, sample_project_data):
        """测试获取项目"""
        # 创建项目
        created_project = project_service.create_project(**sample_project_data)
        
        # 获取项目
        retrieved_project = project_service.get_project(created_project.id)
        
        assert retrieved_project.id == created_project.id
        assert retrieved_project.name == created_project.name
    
    def test_get_nonexistent_project(self, project_service):
        """测试获取不存在的项目"""
        with pytest.raises(ProjectNotFoundError):
            project_service.get_project("nonexistent-id")
    
    def test_update_project(self, project_service, sample_project_data):
        """测试更新项目"""
        project = project_service.create_project(**sample_project_data)
        
        # 更新项目
        updates = {
            "name": "更新后的项目名",
            "description": "更新后的描述"
        }
        updated_project = project_service.update_project(project.id, updates)
        
        assert updated_project.name == updates["name"]
        assert updated_project.description == updates["description"]
        assert updated_project.updated_at > project.updated_at
    
    def test_delete_project(self, project_service, sample_project_data):
        """测试删除项目"""
        project = project_service.create_project(**sample_project_data)
        
        # 删除项目
        result = project_service.delete_project(project.id)
        assert result is True
        
        # 确认项目已删除
        with pytest.raises(ProjectNotFoundError):
            project_service.get_project(project.id)
    
    def test_list_projects(self, project_service, sample_project_data):
        """测试列出项目"""
        # 创建多个项目
        projects = []
        for i in range(3):
            data = sample_project_data.copy()
            data["name"] = f"项目{i+1}"
            project = project_service.create_project(**data)
            projects.append(project)
        
        # 获取项目列表
        project_list = project_service.list_projects()
        
        assert len(project_list) >= 3
        project_names = [p.name for p in project_list]
        for project in projects:
            assert project.name in project_names
    
    def test_search_projects(self, project_service, sample_project_data):
        """测试搜索项目"""
        # 创建测试项目
        data1 = sample_project_data.copy()
        data1["name"] = "科幻小说"
        data1["description"] = "关于未来世界的故事"
        project1 = project_service.create_project(**data1)
        
        data2 = sample_project_data.copy()
        data2["name"] = "爱情小说"
        data2["description"] = "浪漫的爱情故事"
        project2 = project_service.create_project(**data2)
        
        # 搜索项目
        results = project_service.search_projects("科幻")
        assert len(results) >= 1
        assert any(p.name == "科幻小说" for p in results)
        
        results = project_service.search_projects("爱情")
        assert len(results) >= 1
        assert any(p.name == "爱情小说" for p in results)
    
    @patch('src.services.project_service.ProjectService._save_to_file')
    def test_save_project(self, mock_save, project_service, sample_project_data):
        """测试保存项目"""
        project = project_service.create_project(**sample_project_data)
        
        result = project_service.save_project(project)
        
        assert result is True
        mock_save.assert_called_once_with(project)


class TestStoryService:
    """故事服务测试"""
    
    @pytest.fixture
    def story_service(self):
        """故事服务fixture"""
        return StoryService()
    
    @pytest.fixture
    def sample_story_data(self):
        """示例故事数据"""
        return {
            "title": "测试故事",
            "synopsis": "这是一个测试故事",
            "genre": "科幻",
            "project_id": "test-project-id"
        }
    
    def test_create_story(self, story_service, sample_story_data):
        """测试创建故事"""
        story = story_service.create_story(**sample_story_data)
        
        assert isinstance(story, Story)
        assert story.title == sample_story_data["title"]
        assert story.genre == sample_story_data["genre"]
        assert story.id is not None
    
    def test_add_chapter(self, story_service, sample_story_data):
        """测试添加章节"""
        story = story_service.create_story(**sample_story_data)
        
        chapter_data = {
            "title": "第一章",
            "content": "这是第一章的内容" * 50,
            "order": 1
        }
        
        chapter = story_service.add_chapter(story.id, **chapter_data)
        
        assert chapter.title == chapter_data["title"]
        assert chapter.story_id == story.id
        assert chapter.order == 1
        
        # 验证故事中包含章节
        updated_story = story_service.get_story(story.id)
        assert len(updated_story.chapters) == 1
        assert updated_story.chapters[0].id == chapter.id
    
    def test_update_chapter(self, story_service, sample_story_data):
        """测试更新章节"""
        story = story_service.create_story(**sample_story_data)
        chapter = story_service.add_chapter(
            story.id,
            title="原标题",
            content="原内容",
            order=1
        )
        
        # 更新章节
        updates = {
            "title": "新标题",
            "content": "新内容" * 100
        }
        updated_chapter = story_service.update_chapter(chapter.id, updates)
        
        assert updated_chapter.title == updates["title"]
        assert updates["content"] in updated_chapter.content
    
    def test_delete_chapter(self, story_service, sample_story_data):
        """测试删除章节"""
        story = story_service.create_story(**sample_story_data)
        chapter = story_service.add_chapter(
            story.id,
            title="要删除的章节",
            content="内容",
            order=1
        )
        
        # 删除章节
        result = story_service.delete_chapter(chapter.id)
        assert result is True
        
        # 验证章节已从故事中移除
        updated_story = story_service.get_story(story.id)
        assert len(updated_story.chapters) == 0
    
    def test_add_character(self, story_service, sample_story_data):
        """测试添加角色"""
        story = story_service.create_story(**sample_story_data)
        
        character_data = {
            "name": "主角",
            "description": "故事的主人公",
            "age": 25,
            "gender": "男",
            "occupation": "程序员"
        }
        
        character = story_service.add_character(story.id, **character_data)
        
        assert character.name == character_data["name"]
        assert character.age == character_data["age"]
        
        # 验证故事中包含角色
        updated_story = story_service.get_story(story.id)
        assert len(updated_story.characters) == 1
        assert updated_story.characters[0].id == character.id
    
    def test_story_statistics(self, story_service, sample_story_data):
        """测试故事统计"""
        story = story_service.create_story(**sample_story_data)
        
        # 添加章节
        for i in range(3):
            story_service.add_chapter(
                story.id,
                title=f"第{i+1}章",
                content="测试内容" * 200,  # 增加字数
                order=i+1
            )
        
        # 获取统计信息
        stats = story_service.get_story_statistics(story.id)
        
        assert stats["chapter_count"] == 3
        assert stats["total_word_count"] > 0
        assert stats["average_words_per_chapter"] > 0


class TestAIService:
    """AI服务测试"""
    
    @pytest.fixture
    def ai_service(self):
        """AI服务fixture"""
        return AIService()
    
    @pytest.fixture
    def mock_ai_client(self):
        """模拟AI客户端"""
        return Mock()
    
    def test_create_request(self, ai_service):
        """测试创建AI请求"""
        request_data = {
            "function": AIFunction.CONTINUE_WRITING,
            "prompt": "请继续写这个故事",
            "context": "故事的前文内容",
            "parameters": {
                "temperature": 0.7,
                "max_tokens": 1000
            }
        }
        
        request = ai_service.create_request(**request_data)
        
        assert isinstance(request, AIRequest)
        assert request.function == AIFunction.CONTINUE_WRITING
        assert request.prompt == request_data["prompt"]
        assert request.status == ProcessStatus.PENDING
    
    @pytest.mark.asyncio
    async def test_process_request(self, ai_service, mock_ai_client):
        """测试处理AI请求"""
        # 模拟AI响应
        mock_response = {
            "content": "这是AI生成的内容",
            "tokens_used": 150,
            "model": "gpt-3.5-turbo"
        }
        mock_ai_client.generate_text.return_value = mock_response
        
        # 创建请求
        request = ai_service.create_request(
            function=AIFunction.CONTINUE_WRITING,
            prompt="继续写作",
            context="前文内容"
        )
        
        # 处理请求
        with patch.object(ai_service, '_get_ai_client', return_value=mock_ai_client):
            response = await ai_service.process_request(request.id)
        
        assert isinstance(response, AIResponse)
        assert response.content == mock_response["content"]
        assert response.tokens_used == mock_response["tokens_used"]
        assert response.request_id == request.id
    
    @pytest.mark.asyncio
    async def test_process_request_error(self, ai_service, mock_ai_client):
        """测试AI请求处理错误"""
        # 模拟AI服务错误
        mock_ai_client.generate_text.side_effect = Exception("API错误")
        
        request = ai_service.create_request(
            function=AIFunction.CONTINUE_WRITING,
            prompt="测试提示"
        )
        
        with patch.object(ai_service, '_get_ai_client', return_value=mock_ai_client):
            with pytest.raises(AIServiceError):
                await ai_service.process_request(request.id)
    
    def test_get_request_status(self, ai_service):
        """测试获取请求状态"""
        request = ai_service.create_request(
            function=AIFunction.GENERATE_OUTLINE,
            prompt="生成大纲"
        )
        
        status = ai_service.get_request_status(request.id)
        assert status == ProcessStatus.PENDING
    
    def test_cancel_request(self, ai_service):
        """测试取消请求"""
        request = ai_service.create_request(
            function=AIFunction.IMPROVE_TEXT,
            prompt="改进文本"
        )
        
        result = ai_service.cancel_request(request.id)
        assert result is True
        
        status = ai_service.get_request_status(request.id)
        assert status == ProcessStatus.CANCELLED
    
    def test_get_request_history(self, ai_service):
        """测试获取请求历史"""
        # 创建多个请求
        requests = []
        for i in range(3):
            request = ai_service.create_request(
                function=AIFunction.CONTINUE_WRITING,
                prompt=f"请求{i+1}"
            )
            requests.append(request)
        
        history = ai_service.get_request_history(limit=10)
        
        assert len(history) >= 3
        request_ids = [r.id for r in history]
        for request in requests:
            assert request.id in request_ids


class TestExportService:
    """导出服务测试"""
    
    @pytest.fixture
    def export_service(self):
        """导出服务fixture"""
        return ExportService()
    
    @pytest.fixture
    def sample_story(self):
        """示例故事"""
        story = Story(title="测试故事", synopsis="测试简介")
        
        # 添加章节
        for i in range(3):
            chapter = Chapter(
                title=f"第{i+1}章",
                content=f"这是第{i+1}章的内容" * 100,
                order=i+1
            )
            story.add_chapter(chapter)
        
        return story
    
    def test_export_to_docx(self, export_service, sample_story, tmp_path):
        """测试导出为DOCX格式"""
        output_path = tmp_path / "test_export.docx"
        
        result = export_service.export_story(
            story=sample_story,
            format="docx",
            output_path=str(output_path)
        )
        
        assert result is True
        assert output_path.exists()
        assert output_path.stat().st_size > 0
    
    def test_export_to_pdf(self, export_service, sample_story, tmp_path):
        """测试导出为PDF格式"""
        output_path = tmp_path / "test_export.pdf"
        
        with patch('src.services.export_service.ExportService._create_pdf') as mock_pdf:
            mock_pdf.return_value = True
            
            result = export_service.export_story(
                story=sample_story,
                format="pdf",
                output_path=str(output_path)
            )
            
            assert result is True
            mock_pdf.assert_called_once()
    
    def test_export_to_html(self, export_service, sample_story, tmp_path):
        """测试导出为HTML格式"""
        output_path = tmp_path / "test_export.html"
        
        result = export_service.export_story(
            story=sample_story,
            format="html",
            output_path=str(output_path)
        )
        
        assert result is True
        assert output_path.exists()
        
        # 验证HTML内容
        content = output_path.read_text(encoding='utf-8')
        assert sample_story.title in content
        assert "第1章" in content
    
    def test_export_with_custom_settings(self, export_service, sample_story, tmp_path):
        """测试使用自定义设置导出"""
        output_path = tmp_path / "custom_export.docx"
        
        custom_settings = {
            "include_toc": True,
            "font_family": "宋体",
            "font_size": 12,
            "line_spacing": 1.5,
            "page_margins": {"top": 2.5, "bottom": 2.5, "left": 2.0, "right": 2.0}
        }
        
        result = export_service.export_story(
            story=sample_story,
            format="docx",
            output_path=str(output_path),
            settings=custom_settings
        )
        
        assert result is True
        assert output_path.exists()
    
    def test_export_invalid_format(self, export_service, sample_story, tmp_path):
        """测试导出无效格式"""
        output_path = tmp_path / "test.invalid"
        
        with pytest.raises(ExportError, match="不支持的导出格式"):
            export_service.export_story(
                story=sample_story,
                format="invalid",
                output_path=str(output_path)
            )


class TestBackupService:
    """备份服务测试"""
    
    @pytest.fixture
    def backup_service(self):
        """备份服务fixture"""
        return BackupService()
    
    @pytest.fixture
    def sample_project(self, tmp_path):
        """示例项目"""
        project = Project(
            name="测试项目",
            author="测试作者",
            project_type=ProjectType.NOVEL
        )
        
        # 创建项目文件
        project_dir = tmp_path / "projects" / project.id
        project_dir.mkdir(parents=True)
        
        project_file = project_dir / "project.json"
        project_file.write_text(json.dumps({
            "id": project.id,
            "name": project.name,
            "author": project.author
        }), encoding='utf-8')
        
        project.file_path = str(project_file)
        return project
    
    def test_create_backup(self, backup_service, sample_project, tmp_path):
        """测试创建备份"""
        backup_dir = tmp_path / "backups"
        backup_dir.mkdir()
        
        backup_path = backup_service.create_backup(
            project=sample_project,
            backup_dir=str(backup_dir)
        )
        
        assert backup_path is not None
        assert Path(backup_path).exists()
        assert Path(backup_path).suffix == '.zip'
    
    def test_restore_backup(self, backup_service, sample_project, tmp_path):
        """测试恢复备份"""
        backup_dir = tmp_path / "backups"
        backup_dir.mkdir()
        restore_dir = tmp_path / "restore"
        restore_dir.mkdir()
        
        # 创建备份
        backup_path = backup_service.create_backup(
            project=sample_project,
            backup_dir=str(backup_dir)
        )
        
        # 恢复备份
        result = backup_service.restore_backup(
            backup_path=backup_path,
            restore_dir=str(restore_dir)
        )
        
        assert result is True
        
        # 验证恢复的文件
        restored_files = list(restore_dir.rglob("*"))
        assert len(restored_files) > 0
    
    def test_list_backups(self, backup_service, sample_project, tmp_path):
        """测试列出备份"""
        backup_dir = tmp_path / "backups"
        backup_dir.mkdir()
        
        # 创建多个备份
        backups = []
        for i in range(3):
            backup_path = backup_service.create_backup(
                project=sample_project,
                backup_dir=str(backup_dir)
            )
            backups.append(backup_path)
        
        # 列出备份
        backup_list = backup_service.list_backups(str(backup_dir))
        
        assert len(backup_list) >= 3
        for backup in backups:
            assert any(b['path'] == backup for b in backup_list)
    
    def test_cleanup_old_backups(self, backup_service, sample_project, tmp_path):
        """测试清理旧备份"""
        backup_dir = tmp_path / "backups"
        backup_dir.mkdir()
        
        # 创建多个备份
        for i in range(5):
            backup_service.create_backup(
                project=sample_project,
                backup_dir=str(backup_dir)
            )
        
        # 清理旧备份，只保留3个
        cleaned_count = backup_service.cleanup_old_backups(
            backup_dir=str(backup_dir),
            keep_count=3
        )
        
        assert cleaned_count == 2
        
        # 验证剩余备份数量
        remaining_backups = backup_service.list_backups(str(backup_dir))
        assert len(remaining_backups) == 3


class TestUserService:
    """用户服务测试"""
    
    @pytest.fixture
    def user_service(self):
        """用户服务fixture"""
        return UserService()
    
    def test_create_user(self, user_service):
        """测试创建用户"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "display_name": "测试用户"
        }
        
        user = user_service.create_user(**user_data)
        
        assert isinstance(user, User)
        assert user.username == user_data["username"]
        assert user.email == user_data["email"]
        assert user.is_active is True
    
    def test_get_user(self, user_service):
        """测试获取用户"""
        user = user_service.create_user(
            username="getuser",
            email="<EMAIL>"
        )
        
        retrieved_user = user_service.get_user(user.id)
        
        assert retrieved_user.id == user.id
        assert retrieved_user.username == user.username
    
    def test_update_user_preferences(self, user_service):
        """测试更新用户偏好"""
        user = user_service.create_user(
            username="prefuser",
            email="<EMAIL>"
        )
        
        preferences = {
            "theme": "dark",
            "language": "zh_CN",
            "auto_save_interval": 300,
            "font_size": 16
        }
        
        updated_prefs = user_service.update_user_preferences(
            user.id,
            preferences
        )
        
        assert updated_prefs.theme == "dark"
        assert updated_prefs.language == "zh_CN"
        assert updated_prefs.font_size == 16
    
    def test_user_statistics(self, user_service):
        """测试用户统计"""
        user = user_service.create_user(
            username="statsuser",
            email="<EMAIL>"
        )
        
        # 更新统计数据
        user_service.update_user_stats(
            user.id,
            words_written=1000,
            writing_time=60
        )
        
        stats = user_service.get_user_statistics(user.id)
        
        assert stats.total_words_written >= 1000
        assert stats.total_writing_time >= 60


class TestSearchService:
    """搜索服务测试"""
    
    @pytest.fixture
    def search_service(self):
        """搜索服务fixture"""
        return SearchService()
    
    @pytest.fixture
    def sample_data(self):
        """示例搜索数据"""
        return {
            "projects": [
                {"id": "1", "name": "科幻小说", "description": "关于未来的故事"},
                {"id": "2", "name": "爱情小说", "description": "浪漫的爱情故事"},
                {"id": "3", "name": "悬疑小说", "description": "充满悬念的推理故事"}
            ],
            "stories": [
                {"id": "1", "title": "星际旅行", "content": "在遥远的未来..."},
                {"id": "2", "title": "爱在春天", "content": "春天的爱情故事..."},
                {"id": "3", "title": "神秘案件", "content": "一个神秘的案件..."}
            ]
        }
    
    def test_search_projects(self, search_service, sample_data):
        """测试搜索项目"""
        # 索引数据
        for project in sample_data["projects"]:
            search_service.index_project(project)
        
        # 搜索
        results = search_service.search_projects("科幻")
        
        assert len(results) >= 1
        assert any(r["name"] == "科幻小说" for r in results)
    
    def test_search_stories(self, search_service, sample_data):
        """测试搜索故事"""
        # 索引数据
        for story in sample_data["stories"]:
            search_service.index_story(story)
        
        # 搜索
        results = search_service.search_stories("爱情")
        
        assert len(results) >= 1
        assert any(r["title"] == "爱在春天" for r in results)
    
    def test_full_text_search(self, search_service, sample_data):
        """测试全文搜索"""
        # 索引故事内容
        for story in sample_data["stories"]:
            search_service.index_story(story)
        
        # 全文搜索
        results = search_service.full_text_search("未来")
        
        assert len(results) >= 1
        assert any("星际旅行" in str(r) for r in results)
    
    def test_search_with_filters(self, search_service, sample_data):
        """测试带过滤器的搜索"""
        # 索引项目
        for project in sample_data["projects"]:
            project["type"] = "novel"
            search_service.index_project(project)
        
        # 带过滤器搜索
        results = search_service.search_projects(
            query="小说",
            filters={"type": "novel"}
        )
        
        assert len(results) >= 1
        for result in results:
            assert result.get("type") == "novel"


# 集成测试
class TestServiceIntegration:
    """服务集成测试"""
    
    @pytest.fixture
    def services(self):
        """服务集合"""
        return {
            "project": ProjectService(),
            "story": StoryService(),
            "ai": AIService(),
            "export": ExportService(),
            "backup": BackupService(),
            "user": UserService(),
            "search": SearchService()
        }
    
    def test_project_story_workflow(self, services):
        """测试项目-故事工作流"""
        project_service = services["project"]
        story_service = services["story"]
        
        # 创建项目
        project = project_service.create_project(
            name="集成测试项目",
            author="测试作者",
            project_type=ProjectType.NOVEL
        )
        
        # 创建故事
        story = story_service.create_story(
            title="集成测试故事",
            project_id=project.id
        )
        
        # 添加章节
        chapter = story_service.add_chapter(
            story.id,
            title="第一章",
            content="这是第一章的内容" * 100,
            order=1
        )
        
        # 验证集成
        assert story.project_id == project.id
        assert len(story.chapters) == 1
        assert story.get_total_word_count() > 0
    
    @pytest.mark.asyncio
    async def test_ai_story_integration(self, services):
        """测试AI-故事集成"""
        story_service = services["story"]
        ai_service = services["ai"]
        
        # 创建故事
        story = story_service.create_story(
            title="AI辅助故事",
            synopsis="使用AI辅助创作的故事"
        )
        
        # 创建AI请求
        request = ai_service.create_request(
            function=AIFunction.CONTINUE_WRITING,
            prompt="请继续写这个故事",
            context=story.synopsis
        )
        
        # 模拟AI响应
        with patch.object(ai_service, '_get_ai_client') as mock_client:
            mock_client.return_value.generate_text.return_value = {
                "content": "AI生成的故事内容",
                "tokens_used": 200
            }
            
            response = await ai_service.process_request(request.id)
        
        # 将AI生成的内容添加到故事
        chapter = story_service.add_chapter(
            story.id,
            title="AI生成章节",
            content=response.content,
            order=1
        )
        
        assert chapter.content == response.content
        assert story.get_total_word_count() > 0


# 性能测试
class TestServicePerformance:
    """服务性能测试"""
    
    def test_bulk_project_creation(self):
        """测试批量创建项目的性能"""
        project_service = ProjectService()
        
        start_time = datetime.now()
        
        # 创建100个项目
        projects = []
        for i in range(100):
            project = project_service.create_project(
                name=f"性能测试项目{i}",
                author="测试作者",
                project_type=ProjectType.NOVEL
            )
            projects.append(project)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 验证性能（应该在合理时间内完成）
        assert duration < 10.0  # 10秒内完成
        assert len(projects) == 100
    
    def test_large_text_processing(self):
        """测试大文本处理性能"""
        story_service = StoryService()
        
        # 创建故事
        story = story_service.create_story(
            title="大文本测试",
            synopsis="测试大文本处理性能"
        )
        
        # 创建大章节（约10万字）
        large_content = "这是一个很长的段落。" * 10000
        
        start_time = datetime.now()
        
        chapter = story_service.add_chapter(
            story.id,
            title="大章节",
            content=large_content,
            order=1
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 验证性能
        assert duration < 5.0  # 5秒内完成
        assert chapter.get_word_count() > 50000


if __name__ == "__main__":
    pytest.main([__file__, "-v"])