#!/usr/bin/env python3
"""笔落 - AI小说创作平台主入口

应用程序启动入口，负责初始化配置、日志系统和启动UI界面。
"""

import sys
import asyncio
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from config import settings

def setup_logging():
    """设置日志系统"""
    import logging
    from logging.handlers import RotatingFileHandler
    
    # 创建日志目录
    log_dir = Path(settings.logs_dir)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 配置日志格式
    formatter = logging.Formatter(settings.log_format)
    
    # 配置文件日志处理器
    file_handler = RotatingFileHandler(
        log_dir / "bamboofall.log",
        maxBytes=settings.log_max_size * 1024 * 1024,  # 转换为字节
        backupCount=settings.log_backup_count,
        encoding="utf-8"
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(getattr(logging, settings.log_level.upper()))
    
    # 配置控制台日志处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO if not settings.debug else logging.DEBUG)
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    return root_logger

def main():
    """主函数"""
    try:
        # 设置日志系统
        logger = setup_logging()
        logger.info(f"启动 {settings.app_name} v{settings.app_version}")
        
        # 确保必要目录存在
        Path(settings.data_dir).mkdir(parents=True, exist_ok=True)
        Path(settings.temp_dir).mkdir(parents=True, exist_ok=True)
        
        logger.info("应用程序初始化完成")
        logger.info(f"数据目录: {Path(settings.data_dir).absolute()}")
        logger.info(f"日志目录: {Path(settings.logs_dir).absolute()}")
        logger.info(f"调试模式: {settings.debug}")
        
        # TODO: 启动UI界面
        # 这里将在后续任务中实现Flet UI界面启动
        print(f"欢迎使用 {settings.app_name}!")
        print("UI界面将在后续开发中实现...")
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()