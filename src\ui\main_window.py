"""主窗口和导航系统

提供应用程序的主界面框架，包括窗口管理、导航系统和页面路由。
"""

import flet as ft
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass
from .components.base import BaseComponent, theme_manager, But<PERSON>, ThemeMode
from .layouts.flex import FlexLayout, FlexDirection, JustifyContent, AlignItems
from .layouts.container import Container, ContainerConfig, PaddingConfig

class NavigationMode(str, Enum):
    """导航模式"""
    SIDEBAR = "sidebar"  # 侧边栏导航
    TABS = "tabs"  # 标签页导航
    DRAWER = "drawer"  # 抽屉导航
    BOTTOM = "bottom"  # 底部导航

class PageTransition(str, Enum):
    """页面转换动画"""
    NONE = "none"
    FADE = "fade"
    SLIDE_LEFT = "slide_left"
    SLIDE_RIGHT = "slide_right"
    SLIDE_UP = "slide_up"
    SLIDE_DOWN = "slide_down"

@dataclass
class NavigationItem:
    """导航项"""
    id: str
    title: str
    icon: Optional[str] = None
    page_builder: Optional[Callable[[], ft.Control]] = None
    children: Optional[List['NavigationItem']] = None
    badge: Optional[str] = None
    enabled: bool = True
    visible: bool = True
    
    def __post_init__(self):
        if self.children is None:
            self.children = []

@dataclass
class WindowConfig:
    """窗口配置"""
    title: str = "AI小说写作平台"
    width: int = 1200
    height: int = 800
    min_width: int = 800
    min_height: int = 600
    resizable: bool = True
    maximizable: bool = True
    minimizable: bool = True
    always_on_top: bool = False
    center: bool = True
    icon: Optional[str] = None

class NavigationManager:
    """导航管理器"""
    
    def __init__(self):
        self.items: List[NavigationItem] = []
        self.current_page_id: Optional[str] = None
        self.history: List[str] = []
        self.on_navigate: Optional[Callable[[str], None]] = None
    
    def add_item(self, item: NavigationItem):
        """添加导航项"""
        self.items.append(item)
    
    def remove_item(self, item_id: str):
        """移除导航项"""
        self.items = [item for item in self.items if item.id != item_id]
    
    def find_item(self, item_id: str) -> Optional[NavigationItem]:
        """查找导航项"""
        for item in self.items:
            if item.id == item_id:
                return item
            # 递归查找子项
            if item.children:
                for child in item.children:
                    if child.id == item_id:
                        return child
        return None
    
    def navigate_to(self, page_id: str):
        """导航到指定页面"""
        item = self.find_item(page_id)
        if item and item.enabled:
            # 添加到历史记录
            if self.current_page_id:
                self.history.append(self.current_page_id)
            
            self.current_page_id = page_id
            
            # 触发导航回调
            if self.on_navigate:
                self.on_navigate(page_id)
    
    def go_back(self):
        """返回上一页"""
        if self.history:
            previous_page = self.history.pop()
            self.current_page_id = previous_page
            
            if self.on_navigate:
                self.on_navigate(previous_page)
    
    def can_go_back(self) -> bool:
        """是否可以返回"""
        return len(self.history) > 0

class PageManager:
    """页面管理器"""
    
    def __init__(self):
        self.pages: Dict[str, ft.Control] = {}
        self.page_builders: Dict[str, Callable[[], ft.Control]] = {}
        self.current_page: Optional[ft.Control] = None
        self.current_page_id: Optional[str] = None
        self.transition: PageTransition = PageTransition.FADE
    
    def register_page(self, page_id: str, page_builder: Callable[[], ft.Control]):
        """注册页面构建器"""
        self.page_builders[page_id] = page_builder
    
    def get_page(self, page_id: str) -> Optional[ft.Control]:
        """获取页面"""
        # 如果页面已缓存，直接返回
        if page_id in self.pages:
            return self.pages[page_id]
        
        # 如果有构建器，创建页面
        if page_id in self.page_builders:
            page = self.page_builders[page_id]()
            self.pages[page_id] = page
            return page
        
        return None
    
    def show_page(self, page_id: str) -> bool:
        """显示页面"""
        page = self.get_page(page_id)
        if page:
            self.current_page = page
            self.current_page_id = page_id
            return True
        return False
    
    def clear_cache(self, page_id: Optional[str] = None):
        """清除页面缓存"""
        if page_id:
            self.pages.pop(page_id, None)
        else:
            self.pages.clear()

class Sidebar(BaseComponent):
    """侧边栏组件"""
    
    def __init__(
        self,
        navigation_manager: NavigationManager,
        width: float = 250,
        collapsible: bool = True,
        collapsed: bool = False,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.navigation_manager = navigation_manager
        self.width = width
        self.collapsible = collapsible
        self.collapsed = collapsed
        self.navigation_manager.on_navigate = self._on_navigate
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            self._control.bgcolor = self.theme.colors.surface_variant
    
    def _on_navigate(self, page_id: str):
        """导航回调"""
        # 更新选中状态
        if self._control:
            self.update()
    
    def _create_nav_item(self, item: NavigationItem, level: int = 0) -> ft.Control:
        """创建导航项"""
        if not item.visible:
            return ft.Container(height=0)
        
        # 判断是否为当前页面
        is_current = self.navigation_manager.current_page_id == item.id
        
        # 创建主按钮
        button_content = []
        
        # 图标
        if item.icon and not self.collapsed:
            button_content.append(
                ft.Icon(item.icon, size=20)
            )
        elif item.icon and self.collapsed:
            button_content.append(
                ft.Icon(item.icon, size=24)
            )
        
        # 标题（非折叠状态）
        if not self.collapsed:
            title_text = ft.Text(
                item.title,
                size=14,
                weight=ft.FontWeight.BOLD if is_current else ft.FontWeight.NORMAL
            )
            button_content.append(title_text)
            
            # 徽章
            if item.badge:
                badge = ft.Container(
                    content=ft.Text(item.badge, size=10, color="white"),
                    bgcolor="red",
                    border_radius=10,
                    padding=ft.padding.symmetric(horizontal=6, vertical=2),
                    margin=ft.margin.only(left=8)
                )
                button_content.append(badge)
        
        # 创建按钮行
        button_row = ft.Row(
            button_content,
            spacing=12,
            alignment=ft.MainAxisAlignment.START
        )
        
        # 创建导航按钮
        nav_button = ft.Container(
            content=button_row,
            padding=ft.padding.symmetric(
                horizontal=16 if not self.collapsed else 8,
                vertical=12
            ),
            margin=ft.margin.symmetric(horizontal=8, vertical=2),
            border_radius=8,
            bgcolor=self.theme.colors.primary_container if is_current else None,
            on_click=lambda e, item_id=item.id: self._on_item_click(item_id) if item.enabled else None,
            ink=True
        )
        
        # 如果有子项且未折叠
        if item.children and not self.collapsed:
            children_controls = [nav_button]
            for child in item.children:
                child_control = self._create_nav_item(child, level + 1)
                children_controls.append(
                    ft.Container(
                        content=child_control,
                        margin=ft.margin.only(left=20)
                    )
                )
            
            return ft.Column(children_controls, spacing=0)
        
        return nav_button
    
    def _on_item_click(self, item_id: str):
        """导航项点击事件"""
        self.navigation_manager.navigate_to(item_id)
    
    def _toggle_collapse(self, e):
        """切换折叠状态"""
        self.collapsed = not self.collapsed
        if self._control:
            self._control = self.build()
            self.update()
    
    def build(self) -> ft.Control:
        """构建侧边栏"""
        children = []
        
        # 标题栏
        if not self.collapsed:
            header = ft.Container(
                content=ft.Row([
                    ft.Text(
                        "AI写作平台",
                        size=18,
                        weight=ft.FontWeight.BOLD
                    ),
                    ft.IconButton(
                        icon=ft.icons.MENU,
                        on_click=self._toggle_collapse
                    ) if self.collapsible else ft.Container()
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                padding=ft.padding.all(16),
                border=ft.border.only(bottom=ft.BorderSide(1, self.theme.colors.outline_variant))
            )
        else:
            header = ft.Container(
                content=ft.IconButton(
                    icon=ft.icons.MENU,
                    on_click=self._toggle_collapse
                ),
                padding=ft.padding.all(8),
                alignment=ft.alignment.center
            )
        
        children.append(header)
        
        # 导航项
        nav_items = []
        for item in self.navigation_manager.items:
            nav_control = self._create_nav_item(item)
            nav_items.append(nav_control)
        
        # 导航区域
        nav_area = ft.Column(
            nav_items,
            spacing=4,
            scroll=ft.ScrollMode.AUTO,
            expand=True
        )
        
        children.append(
            ft.Container(
                content=nav_area,
                padding=ft.padding.symmetric(vertical=8),
                expand=True
            )
        )
        
        # 底部区域（用户信息等）
        if not self.collapsed:
            footer = ft.Container(
                content=ft.Row([
                    ft.Icon(ft.icons.ACCOUNT_CIRCLE, size=24),
                    ft.Text("用户名", size=12)
                ], spacing=8),
                padding=ft.padding.all(16),
                border=ft.border.only(top=ft.BorderSide(1, self.theme.colors.outline_variant))
            )
            children.append(footer)
        
        # 创建侧边栏容器
        sidebar = ft.Container(
            content=ft.Column(children, spacing=0),
            width=self.width if not self.collapsed else 64,
            bgcolor=self.theme.colors.surface_variant,
            border=ft.border.only(right=ft.BorderSide(1, self.theme.colors.outline_variant))
        )
        
        return sidebar

class StatusBar(BaseComponent):
    """状态栏组件"""
    
    def __init__(
        self,
        height: float = 32,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.height = height
        self.status_text = "就绪"
        self.progress_visible = False
        self.progress_value = 0.0
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            self._control.bgcolor = self.theme.colors.surface_variant
    
    def build(self) -> ft.Control:
        """构建状态栏"""
        left_items = [
            ft.Text(
                self.status_text,
                size=12,
                color=self.theme.colors.on_surface_variant
            )
        ]
        
        if self.progress_visible:
            left_items.append(
                ft.ProgressBar(
                    value=self.progress_value,
                    width=100,
                    height=4
                )
            )
        
        right_items = [
            ft.Text(
                f"主题: {theme_manager.current_mode.value}",
                size=12,
                color=self.theme.colors.on_surface_variant
            ),
            ft.VerticalDivider(width=1),
            ft.Text(
                "行: 1, 列: 1",
                size=12,
                color=self.theme.colors.on_surface_variant
            )
        ]
        
        status_bar = ft.Container(
            content=ft.Row([
                ft.Row(left_items, spacing=8),
                ft.Row(right_items, spacing=8)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            height=self.height,
            padding=ft.padding.symmetric(horizontal=16, vertical=4),
            bgcolor=self.theme.colors.surface_variant,
            border=ft.border.only(top=ft.BorderSide(1, self.theme.colors.outline_variant))
        )
        
        return status_bar
    
    def set_status(self, text: str):
        """设置状态文本"""
        self.status_text = text
        if self._control:
            # 更新状态文本
            self.update()
    
    def show_progress(self, value: float = 0.0):
        """显示进度条"""
        self.progress_visible = True
        self.progress_value = value
        if self._control:
            self.update()
    
    def hide_progress(self):
        """隐藏进度条"""
        self.progress_visible = False
        if self._control:
            self.update()
    
    def update_progress(self, value: float):
        """更新进度值"""
        self.progress_value = max(0.0, min(1.0, value))
        if self._control:
            self.update()

class MenuBar(BaseComponent):
    """菜单栏组件"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.menus: List[Dict[str, Any]] = []
    
    def add_menu(self, title: str, items: List[Dict[str, Any]]):
        """添加菜单"""
        self.menus.append({
            'title': title,
            'items': items
        })
    
    def build(self) -> ft.Control:
        """构建菜单栏"""
        menu_buttons = []
        
        for menu in self.menus:
            # 创建菜单按钮
            menu_button = ft.PopupMenuButton(
                content=ft.Text(menu['title'], size=14),
                items=[
                    ft.PopupMenuItem(
                        text=item.get('text', ''),
                        icon=item.get('icon'),
                        on_click=item.get('on_click')
                    ) for item in menu['items']
                ]
            )
            menu_buttons.append(menu_button)
        
        menu_bar = ft.Container(
            content=ft.Row(menu_buttons, spacing=0),
            height=40,
            padding=ft.padding.symmetric(horizontal=8),
            bgcolor=self.theme.colors.surface,
            border=ft.border.only(bottom=ft.BorderSide(1, self.theme.colors.outline_variant))
        )
        
        return menu_bar

class MainWindow(BaseComponent):
    """主窗口组件"""
    
    def __init__(
        self,
        config: Optional[WindowConfig] = None,
        navigation_mode: NavigationMode = NavigationMode.SIDEBAR,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.config = config or WindowConfig()
        self.navigation_mode = navigation_mode
        
        # 管理器
        self.navigation_manager = NavigationManager()
        self.page_manager = PageManager()
        
        # 组件
        self.sidebar: Optional[Sidebar] = None
        self.status_bar: Optional[StatusBar] = None
        self.menu_bar: Optional[MenuBar] = None
        
        # 设置导航回调
        self.navigation_manager.on_navigate = self._on_navigate
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            # 更新子组件主题
            if self.sidebar:
                self.sidebar.set_theme(self.theme)
            if self.status_bar:
                self.status_bar.set_theme(self.theme)
            if self.menu_bar:
                self.menu_bar.set_theme(self.theme)
    
    def _on_navigate(self, page_id: str):
        """导航回调"""
        # 显示对应页面
        if self.page_manager.show_page(page_id):
            if self._control:
                self.update()
    
    def setup_navigation(self):
        """设置导航项"""
        # 默认导航项
        nav_items = [
            NavigationItem(
                id="dashboard",
                title="仪表板",
                icon=ft.icons.DASHBOARD,
                page_builder=self._create_dashboard_page
            ),
            NavigationItem(
                id="writing",
                title="写作",
                icon=ft.icons.EDIT,
                children=[
                    NavigationItem(
                        id="new_project",
                        title="新建项目",
                        icon=ft.icons.ADD,
                        page_builder=self._create_new_project_page
                    ),
                    NavigationItem(
                        id="my_projects",
                        title="我的项目",
                        icon=ft.icons.FOLDER,
                        page_builder=self._create_projects_page
                    )
                ]
            ),
            NavigationItem(
                id="ai_assistant",
                title="AI助手",
                icon=ft.icons.SMART_TOY,
                page_builder=self._create_ai_assistant_page
            ),
            NavigationItem(
                id="export",
                title="导出",
                icon=ft.icons.DOWNLOAD,
                page_builder=self._create_export_page
            ),
            NavigationItem(
                id="settings",
                title="设置",
                icon=ft.icons.SETTINGS,
                page_builder=self._create_settings_page
            )
        ]
        
        for item in nav_items:
            self.navigation_manager.add_item(item)
            if item.page_builder:
                self.page_manager.register_page(item.id, item.page_builder)
            
            # 注册子项
            if item.children:
                for child in item.children:
                    if child.page_builder:
                        self.page_manager.register_page(child.id, child.page_builder)
    
    def setup_menu(self):
        """设置菜单栏"""
        if not self.menu_bar:
            self.menu_bar = MenuBar()
        
        # 文件菜单
        self.menu_bar.add_menu("文件", [
            {'text': '新建项目', 'icon': ft.icons.ADD, 'on_click': self._new_project},
            {'text': '打开项目', 'icon': ft.icons.FOLDER_OPEN, 'on_click': self._open_project},
            {'text': '保存', 'icon': ft.icons.SAVE, 'on_click': self._save},
            {'text': '退出', 'icon': ft.icons.EXIT_TO_APP, 'on_click': self._exit}
        ])
        
        # 编辑菜单
        self.menu_bar.add_menu("编辑", [
            {'text': '撤销', 'icon': ft.icons.UNDO, 'on_click': self._undo},
            {'text': '重做', 'icon': ft.icons.REDO, 'on_click': self._redo},
            {'text': '查找', 'icon': ft.icons.SEARCH, 'on_click': self._find}
        ])
        
        # 视图菜单
        self.menu_bar.add_menu("视图", [
            {'text': '切换主题', 'icon': ft.icons.BRIGHTNESS_6, 'on_click': self._toggle_theme},
            {'text': '全屏', 'icon': ft.icons.FULLSCREEN, 'on_click': self._toggle_fullscreen}
        ])
        
        # 帮助菜单
        self.menu_bar.add_menu("帮助", [
            {'text': '用户指南', 'icon': ft.icons.HELP, 'on_click': self._show_help},
            {'text': '关于', 'icon': ft.icons.INFO, 'on_click': self._show_about}
        ])
    
    def build(self) -> ft.Control:
        """构建主窗口"""
        # 设置导航和菜单
        self.setup_navigation()
        self.setup_menu()
        
        # 创建组件
        if self.navigation_mode == NavigationMode.SIDEBAR:
            self.sidebar = Sidebar(self.navigation_manager)
        
        self.status_bar = StatusBar()
        
        # 主内容区域
        content_area = ft.Container(
            content=ft.Text("请选择一个页面", size=16),
            expand=True,
            padding=ft.padding.all(20),
            alignment=ft.alignment.center
        )
        
        # 如果有当前页面，显示它
        if self.page_manager.current_page:
            content_area.content = self.page_manager.current_page
        
        # 布局结构
        main_content = ft.Row([
            self.sidebar.get_control() if self.sidebar else ft.Container(),
            content_area
        ], expand=True, spacing=0)
        
        # 完整布局
        layout = ft.Column([
            self.menu_bar.get_control() if self.menu_bar else ft.Container(),
            main_content,
            self.status_bar.get_control()
        ], expand=True, spacing=0)
        
        return layout
    
    # 页面构建器
    def _create_dashboard_page(self) -> ft.Control:
        """创建仪表板页面"""
        return ft.Container(
            content=ft.Column([
                ft.Text("仪表板", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("欢迎使用AI小说写作平台！", size=16),
                ft.Divider(),
                ft.Text("这里将显示项目统计、最近活动等信息。", size=14)
            ], spacing=16),
            padding=ft.padding.all(20)
        )
    
    def _create_new_project_page(self) -> ft.Control:
        """创建新建项目页面"""
        return ft.Container(
            content=ft.Column([
                ft.Text("新建项目", size=24, weight=ft.FontWeight.BOLD),
                ft.TextField(label="项目名称", width=400),
                ft.TextField(label="项目描述", multiline=True, width=400, height=100),
                ft.ElevatedButton("创建项目", icon=ft.icons.ADD)
            ], spacing=16),
            padding=ft.padding.all(20)
        )
    
    def _create_projects_page(self) -> ft.Control:
        """创建项目列表页面"""
        return ft.Container(
            content=ft.Column([
                ft.Text("我的项目", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("这里将显示您的所有项目。", size=14)
            ], spacing=16),
            padding=ft.padding.all(20)
        )
    
    def _create_ai_assistant_page(self) -> ft.Control:
        """创建AI助手页面"""
        return ft.Container(
            content=ft.Column([
                ft.Text("AI助手", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("AI写作助手功能即将推出。", size=14)
            ], spacing=16),
            padding=ft.padding.all(20)
        )
    
    def _create_export_page(self) -> ft.Control:
        """创建导出页面"""
        return ft.Container(
            content=ft.Column([
                ft.Text("导出", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("在这里可以导出您的作品为各种格式。", size=14)
            ], spacing=16),
            padding=ft.padding.all(20)
        )
    
    def _create_settings_page(self) -> ft.Control:
        """创建设置页面"""
        return ft.Container(
            content=ft.Column([
                ft.Text("设置", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("应用程序设置和偏好。", size=14)
            ], spacing=16),
            padding=ft.padding.all(20)
        )
    
    # 菜单事件处理
    def _new_project(self, e):
        """新建项目"""
        self.navigation_manager.navigate_to("new_project")
    
    def _open_project(self, e):
        """打开项目"""
        pass
    
    def _save(self, e):
        """保存"""
        self.status_bar.set_status("已保存")
    
    def _exit(self, e):
        """退出应用"""
        pass
    
    def _undo(self, e):
        """撤销"""
        pass
    
    def _redo(self, e):
        """重做"""
        pass
    
    def _find(self, e):
        """查找"""
        pass
    
    def _toggle_theme(self, e):
        """切换主题"""
        current_mode = theme_manager.current_mode
        new_mode = ThemeMode.DARK if current_mode == ThemeMode.LIGHT else ThemeMode.LIGHT
        theme_manager.set_mode(new_mode)
        
        # 更新状态栏
        self.status_bar.set_status(f"已切换到{new_mode.value}主题")
    
    def _toggle_fullscreen(self, e):
        """切换全屏"""
        pass
    
    def _show_help(self, e):
        """显示帮助"""
        pass
    
    def _show_about(self, e):
        """显示关于"""
        pass
    
    # 公共方法
    def navigate_to(self, page_id: str):
        """导航到指定页面"""
        self.navigation_manager.navigate_to(page_id)
    
    def add_navigation_item(self, item: NavigationItem):
        """添加导航项"""
        self.navigation_manager.add_item(item)
        if item.page_builder:
            self.page_manager.register_page(item.id, item.page_builder)
    
    def set_status(self, text: str):
        """设置状态栏文本"""
        if self.status_bar:
            self.status_bar.set_status(text)
    
    def show_progress(self, value: float = 0.0):
        """显示进度条"""
        if self.status_bar:
            self.status_bar.show_progress(value)
    
    def hide_progress(self):
        """隐藏进度条"""
        if self.status_bar:
            self.status_bar.hide_progress()

# 导出
__all__ = [
    'NavigationMode',
    'PageTransition',
    'NavigationItem',
    'WindowConfig',
    'NavigationManager',
    'PageManager',
    'Sidebar',
    'StatusBar',
    'MenuBar',
    'MainWindow',
]