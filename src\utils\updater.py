#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动更新系统模块

提供应用自动更新功能，包括版本检查、增量更新、回滚机制等。
支持用户可控的更新策略和通知系统。
"""

import os
import json
import shutil
import hashlib
import zipfile
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
import asyncio
import aiohttp
import aiofiles
from packaging import version

from .logger import LoggerManager
from .error_handler import <PERSON>rror<PERSON><PERSON><PERSON>, BambooFallError


class UpdateStatus(Enum):
    """更新状态"""
    CHECKING = "checking"
    AVAILABLE = "available"
    DOWNLOADING = "downloading"
    INSTALLING = "installing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    ROLLBACK = "rollback"


class UpdateStrategy(Enum):
    """更新策略"""
    AUTOMATIC = "automatic"  # 自动更新
    NOTIFY_ONLY = "notify_only"  # 仅通知
    MANUAL = "manual"  # 手动更新
    DISABLED = "disabled"  # 禁用更新


class UpdateChannel(Enum):
    """更新渠道"""
    STABLE = "stable"
    BETA = "beta"
    ALPHA = "alpha"
    DEV = "dev"


@dataclass
class VersionInfo:
    """版本信息"""
    version: str
    build_number: int
    release_date: datetime
    channel: UpdateChannel
    changelog: List[str]
    download_url: str
    file_size: int
    checksum: str
    min_version: Optional[str] = None  # 最低兼容版本
    
    def is_newer_than(self, other_version: str) -> bool:
        """检查是否比指定版本更新"""
        return version.parse(self.version) > version.parse(other_version)
    
    def is_compatible_with(self, current_version: str) -> bool:
        """检查是否与当前版本兼容"""
        if not self.min_version:
            return True
        return version.parse(current_version) >= version.parse(self.min_version)


@dataclass
class UpdateConfig:
    """更新配置"""
    strategy: UpdateStrategy = UpdateStrategy.NOTIFY_ONLY
    channel: UpdateChannel = UpdateChannel.STABLE
    check_interval: int = 24  # 小时
    auto_download: bool = False
    backup_count: int = 3
    update_server_url: str = "https://api.bamboofall.com/updates"
    proxy_settings: Optional[Dict[str, str]] = None
    

@dataclass
class UpdateProgress:
    """更新进度"""
    status: UpdateStatus
    progress: float  # 0.0 - 1.0
    message: str
    current_step: str
    total_steps: int
    current_step_index: int
    bytes_downloaded: int = 0
    total_bytes: int = 0
    speed: float = 0.0  # bytes/second
    eta: Optional[int] = None  # seconds


class UpdateError(BambooFallError):
    """更新相关错误"""
    pass


class VersionChecker:
    """版本检查器"""
    
    def __init__(self, config: UpdateConfig):
        self.config = config
        self.logger = LoggerManager().get_logger("updater.version_checker")
        self.error_handler = ErrorHandler()
        
    async def check_for_updates(self, current_version: str) -> Optional[VersionInfo]:
        """检查更新"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.config.update_server_url}/check"
                params = {
                    "current_version": current_version,
                    "channel": self.config.channel.value,
                    "platform": self._get_platform_info()
                }
                
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("has_update"):
                            return self._parse_version_info(data["version_info"])
                    elif response.status == 204:
                        # 没有更新
                        return None
                    else:
                        raise UpdateError(f"检查更新失败: HTTP {response.status}")
                        
        except Exception as e:
            self.error_handler.handle_error(e, {"action": "check_updates"})
            raise UpdateError(f"检查更新时发生错误: {str(e)}")
            
        return None
    
    def _get_platform_info(self) -> str:
        """获取平台信息"""
        import platform
        return f"{platform.system()}-{platform.machine()}"
    
    def _parse_version_info(self, data: Dict[str, Any]) -> VersionInfo:
        """解析版本信息"""
        return VersionInfo(
            version=data["version"],
            build_number=data["build_number"],
            release_date=datetime.fromisoformat(data["release_date"]),
            channel=UpdateChannel(data["channel"]),
            changelog=data["changelog"],
            download_url=data["download_url"],
            file_size=data["file_size"],
            checksum=data["checksum"],
            min_version=data.get("min_version")
        )


class UpdateDownloader:
    """更新下载器"""
    
    def __init__(self, config: UpdateConfig):
        self.config = config
        self.logger = LoggerManager().get_logger("updater.downloader")
        self.error_handler = ErrorHandler()
        self._cancelled = False
        
    async def download_update(
        self, 
        version_info: VersionInfo, 
        download_path: Path,
        progress_callback: Optional[Callable[[UpdateProgress], None]] = None
    ) -> Path:
        """下载更新文件"""
        self._cancelled = False
        
        try:
            # 创建下载目录
            download_path.mkdir(parents=True, exist_ok=True)
            
            # 生成文件名
            filename = f"update_{version_info.version}.zip"
            file_path = download_path / filename
            
            # 开始下载
            async with aiohttp.ClientSession() as session:
                async with session.get(version_info.download_url) as response:
                    if response.status != 200:
                        raise UpdateError(f"下载失败: HTTP {response.status}")
                    
                    total_size = int(response.headers.get('content-length', 0))
                    downloaded = 0
                    start_time = datetime.now()
                    
                    async with aiofiles.open(file_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            if self._cancelled:
                                raise UpdateError("下载已取消")
                                
                            await f.write(chunk)
                            downloaded += len(chunk)
                            
                            # 计算进度和速度
                            if progress_callback and total_size > 0:
                                progress = downloaded / total_size
                                elapsed = (datetime.now() - start_time).total_seconds()
                                speed = downloaded / elapsed if elapsed > 0 else 0
                                eta = (total_size - downloaded) / speed if speed > 0 else None
                                
                                progress_info = UpdateProgress(
                                    status=UpdateStatus.DOWNLOADING,
                                    progress=progress,
                                    message=f"正在下载更新文件... {downloaded}/{total_size} bytes",
                                    current_step="下载更新文件",
                                    total_steps=4,
                                    current_step_index=1,
                                    bytes_downloaded=downloaded,
                                    total_bytes=total_size,
                                    speed=speed,
                                    eta=int(eta) if eta else None
                                )
                                progress_callback(progress_info)
            
            # 验证文件完整性
            if not await self._verify_checksum(file_path, version_info.checksum):
                raise UpdateError("文件校验失败")
                
            self.logger.info(f"更新文件下载完成: {file_path}")
            return file_path
            
        except Exception as e:
            self.error_handler.handle_error(e, {"action": "download_update"})
            raise
    
    async def _verify_checksum(self, file_path: Path, expected_checksum: str) -> bool:
        """验证文件校验和"""
        try:
            sha256_hash = hashlib.sha256()
            async with aiofiles.open(file_path, 'rb') as f:
                async for chunk in f:
                    sha256_hash.update(chunk)
            
            actual_checksum = sha256_hash.hexdigest()
            return actual_checksum == expected_checksum
            
        except Exception as e:
            self.logger.error(f"校验文件时发生错误: {e}")
            return False
    
    def cancel_download(self):
        """取消下载"""
        self._cancelled = True


class BackupManager:
    """备份管理器"""
    
    def __init__(self, config: UpdateConfig, app_path: Path):
        self.config = config
        self.app_path = app_path
        self.backup_dir = app_path.parent / "backups"
        self.logger = LoggerManager().get_logger("updater.backup")
        
    def create_backup(self, version: str) -> Path:
        """创建备份"""
        try:
            # 创建备份目录
            self.backup_dir.mkdir(exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{version}_{timestamp}"
            backup_path = self.backup_dir / backup_name
            
            # 创建备份
            shutil.copytree(self.app_path, backup_path, ignore=self._backup_ignore)
            
            # 清理旧备份
            self._cleanup_old_backups()
            
            self.logger.info(f"备份创建完成: {backup_path}")
            return backup_path
            
        except Exception as e:
            self.logger.error(f"创建备份失败: {e}")
            raise UpdateError(f"创建备份失败: {str(e)}")
    
    def restore_backup(self, backup_path: Path) -> bool:
        """恢复备份"""
        try:
            if not backup_path.exists():
                raise UpdateError(f"备份不存在: {backup_path}")
            
            # 创建临时目录
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir) / "temp_app"
                
                # 先移动当前应用到临时目录
                shutil.move(str(self.app_path), str(temp_path))
                
                try:
                    # 恢复备份
                    shutil.copytree(backup_path, self.app_path)
                    self.logger.info(f"备份恢复完成: {backup_path}")
                    return True
                    
                except Exception as e:
                    # 恢复失败，回滚
                    shutil.move(str(temp_path), str(self.app_path))
                    raise e
                    
        except Exception as e:
            self.logger.error(f"恢复备份失败: {e}")
            return False
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """列出所有备份"""
        backups = []
        
        if not self.backup_dir.exists():
            return backups
            
        for backup_path in self.backup_dir.iterdir():
            if backup_path.is_dir() and backup_path.name.startswith("backup_"):
                try:
                    parts = backup_path.name.split("_")
                    if len(parts) >= 3:
                        version = parts[1]
                        timestamp = "_".join(parts[2:])
                        
                        backups.append({
                            "path": backup_path,
                            "version": version,
                            "timestamp": timestamp,
                            "size": self._get_directory_size(backup_path),
                            "created": datetime.fromtimestamp(backup_path.stat().st_ctime)
                        })
                except Exception as e:
                    self.logger.warning(f"解析备份信息失败: {backup_path}, {e}")
        
        # 按创建时间排序
        backups.sort(key=lambda x: x["created"], reverse=True)
        return backups
    
    def _backup_ignore(self, dir_path: str, names: List[str]) -> List[str]:
        """备份时忽略的文件和目录"""
        ignore_patterns = {
            "__pycache__", ".pyc", ".pyo", ".git", ".svn", 
            "node_modules", "logs", "temp", "cache", "backups"
        }
        
        ignored = []
        for name in names:
            if any(pattern in name for pattern in ignore_patterns):
                ignored.append(name)
                
        return ignored
    
    def _cleanup_old_backups(self):
        """清理旧备份"""
        backups = self.list_backups()
        
        if len(backups) > self.config.backup_count:
            # 删除最旧的备份
            for backup in backups[self.config.backup_count:]:
                try:
                    shutil.rmtree(backup["path"])
                    self.logger.info(f"删除旧备份: {backup['path']}")
                except Exception as e:
                    self.logger.warning(f"删除备份失败: {backup['path']}, {e}")
    
    def _get_directory_size(self, path: Path) -> int:
        """获取目录大小"""
        total_size = 0
        try:
            for file_path in path.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception:
            pass
        return total_size


class UpdateInstaller:
    """更新安装器"""
    
    def __init__(self, config: UpdateConfig, app_path: Path):
        self.config = config
        self.app_path = app_path
        self.backup_manager = BackupManager(config, app_path)
        self.logger = LoggerManager().get_logger("updater.installer")
        self.error_handler = ErrorHandler()
        
    async def install_update(
        self, 
        update_file: Path, 
        current_version: str,
        progress_callback: Optional[Callable[[UpdateProgress], None]] = None
    ) -> bool:
        """安装更新"""
        backup_path = None
        
        try:
            # 步骤1: 创建备份
            if progress_callback:
                progress_callback(UpdateProgress(
                    status=UpdateStatus.INSTALLING,
                    progress=0.1,
                    message="正在创建备份...",
                    current_step="创建备份",
                    total_steps=4,
                    current_step_index=2
                ))
            
            backup_path = self.backup_manager.create_backup(current_version)
            
            # 步骤2: 解压更新文件
            if progress_callback:
                progress_callback(UpdateProgress(
                    status=UpdateStatus.INSTALLING,
                    progress=0.3,
                    message="正在解压更新文件...",
                    current_step="解压更新文件",
                    total_steps=4,
                    current_step_index=3
                ))
            
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir) / "update"
                
                # 解压更新文件
                with zipfile.ZipFile(update_file, 'r') as zip_ref:
                    zip_ref.extractall(temp_path)
                
                # 步骤3: 应用更新
                if progress_callback:
                    progress_callback(UpdateProgress(
                        status=UpdateStatus.INSTALLING,
                        progress=0.7,
                        message="正在应用更新...",
                        current_step="应用更新",
                        total_steps=4,
                        current_step_index=4
                    ))
                
                await self._apply_update(temp_path, progress_callback)
            
            # 步骤4: 完成安装
            if progress_callback:
                progress_callback(UpdateProgress(
                    status=UpdateStatus.COMPLETED,
                    progress=1.0,
                    message="更新安装完成",
                    current_step="完成安装",
                    total_steps=4,
                    current_step_index=4
                ))
            
            self.logger.info("更新安装完成")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, {"action": "install_update"})
            
            # 尝试回滚
            if backup_path and backup_path.exists():
                self.logger.info("安装失败，正在回滚...")
                if progress_callback:
                    progress_callback(UpdateProgress(
                        status=UpdateStatus.ROLLBACK,
                        progress=0.0,
                        message="安装失败，正在回滚...",
                        current_step="回滚",
                        total_steps=1,
                        current_step_index=1
                    ))
                
                if self.backup_manager.restore_backup(backup_path):
                    self.logger.info("回滚完成")
                else:
                    self.logger.error("回滚失败")
            
            raise UpdateError(f"安装更新失败: {str(e)}")
    
    async def _apply_update(self, update_path: Path, progress_callback: Optional[Callable] = None):
        """应用更新"""
        # 获取所有需要更新的文件
        update_files = list(update_path.rglob("*"))
        total_files = len([f for f in update_files if f.is_file()])
        processed_files = 0
        
        for file_path in update_files:
            if file_path.is_file():
                # 计算相对路径
                rel_path = file_path.relative_to(update_path)
                target_path = self.app_path / rel_path
                
                # 创建目标目录
                target_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 复制文件
                shutil.copy2(file_path, target_path)
                
                processed_files += 1
                
                # 更新进度
                if progress_callback and total_files > 0:
                    progress = 0.7 + (processed_files / total_files) * 0.2
                    progress_callback(UpdateProgress(
                        status=UpdateStatus.INSTALLING,
                        progress=progress,
                        message=f"正在更新文件... {processed_files}/{total_files}",
                        current_step="应用更新",
                        total_steps=4,
                        current_step_index=4
                    ))


class UpdateNotifier:
    """更新通知器"""
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("updater.notifier")
        self._callbacks: List[Callable[[str, Dict[str, Any]], None]] = []
    
    def add_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """添加通知回调"""
        self._callbacks.append(callback)
    
    def remove_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """移除通知回调"""
        if callback in self._callbacks:
            self._callbacks.remove(callback)
    
    def notify(self, event_type: str, data: Dict[str, Any]):
        """发送通知"""
        for callback in self._callbacks:
            try:
                callback(event_type, data)
            except Exception as e:
                self.logger.error(f"通知回调执行失败: {e}")


class AutoUpdater:
    """自动更新器主类"""
    
    def __init__(self, app_path: Path, current_version: str, config: Optional[UpdateConfig] = None):
        self.app_path = Path(app_path)
        self.current_version = current_version
        self.config = config or UpdateConfig()
        
        # 初始化组件
        self.version_checker = VersionChecker(self.config)
        self.downloader = UpdateDownloader(self.config)
        self.installer = UpdateInstaller(self.config, self.app_path)
        self.notifier = UpdateNotifier()
        
        # 状态管理
        self._is_checking = False
        self._is_updating = False
        self._last_check_time: Optional[datetime] = None
        self._available_update: Optional[VersionInfo] = None
        
        # 日志和错误处理
        self.logger = LoggerManager().get_logger("updater")
        self.error_handler = ErrorHandler()
        
        # 进度回调
        self._progress_callbacks: List[Callable[[UpdateProgress], None]] = []
    
    def add_progress_callback(self, callback: Callable[[UpdateProgress], None]):
        """添加进度回调"""
        self._progress_callbacks.append(callback)
    
    def remove_progress_callback(self, callback: Callable[[UpdateProgress], None]):
        """移除进度回调"""
        if callback in self._progress_callbacks:
            self._progress_callbacks.remove(callback)
    
    def _notify_progress(self, progress: UpdateProgress):
        """通知进度更新"""
        for callback in self._progress_callbacks:
            try:
                callback(progress)
            except Exception as e:
                self.logger.error(f"进度回调执行失败: {e}")
    
    async def check_for_updates(self, force: bool = False) -> Optional[VersionInfo]:
        """检查更新"""
        if self._is_checking and not force:
            return self._available_update
        
        # 检查是否需要检查更新
        if not force and self._last_check_time:
            time_since_check = datetime.now() - self._last_check_time
            if time_since_check < timedelta(hours=self.config.check_interval):
                return self._available_update
        
        self._is_checking = True
        
        try:
            self._notify_progress(UpdateProgress(
                status=UpdateStatus.CHECKING,
                progress=0.0,
                message="正在检查更新...",
                current_step="检查更新",
                total_steps=1,
                current_step_index=1
            ))
            
            version_info = await self.version_checker.check_for_updates(self.current_version)
            
            if version_info and version_info.is_newer_than(self.current_version):
                if version_info.is_compatible_with(self.current_version):
                    self._available_update = version_info
                    self.notifier.notify("update_available", {
                        "version_info": asdict(version_info)
                    })
                else:
                    self.logger.warning(f"发现不兼容的更新版本: {version_info.version}")
                    self.notifier.notify("incompatible_update", {
                        "version_info": asdict(version_info)
                    })
            else:
                self._available_update = None
                self.notifier.notify("no_update_available", {})
            
            self._last_check_time = datetime.now()
            return self._available_update
            
        except Exception as e:
            self.error_handler.handle_error(e, {"action": "check_updates"})
            self.notifier.notify("update_check_failed", {"error": str(e)})
            return None
            
        finally:
            self._is_checking = False
    
    async def download_update(self, version_info: Optional[VersionInfo] = None) -> Optional[Path]:
        """下载更新"""
        if not version_info:
            version_info = self._available_update
        
        if not version_info:
            raise UpdateError("没有可用的更新")
        
        try:
            download_dir = self.app_path.parent / "downloads"
            return await self.downloader.download_update(
                version_info, 
                download_dir, 
                self._notify_progress
            )
            
        except Exception as e:
            self.notifier.notify("download_failed", {"error": str(e)})
            raise
    
    async def install_update(self, update_file: Path) -> bool:
        """安装更新"""
        if self._is_updating:
            raise UpdateError("更新正在进行中")
        
        self._is_updating = True
        
        try:
            result = await self.installer.install_update(
                update_file, 
                self.current_version, 
                self._notify_progress
            )
            
            if result:
                self.notifier.notify("update_installed", {})
                self._available_update = None
            
            return result
            
        except Exception as e:
            self.notifier.notify("install_failed", {"error": str(e)})
            raise
            
        finally:
            self._is_updating = False
    
    async def auto_update(self) -> bool:
        """自动更新流程"""
        if self.config.strategy == UpdateStrategy.DISABLED:
            return False
        
        try:
            # 检查更新
            version_info = await self.check_for_updates()
            
            if not version_info:
                return False
            
            # 根据策略处理更新
            if self.config.strategy == UpdateStrategy.AUTOMATIC:
                # 自动下载和安装
                update_file = await self.download_update(version_info)
                if update_file:
                    return await self.install_update(update_file)
                    
            elif self.config.strategy == UpdateStrategy.NOTIFY_ONLY:
                # 仅通知，不自动安装
                return True
                
            elif self.config.strategy == UpdateStrategy.MANUAL:
                # 手动更新，仅下载
                if self.config.auto_download:
                    await self.download_update(version_info)
                return True
            
            return False
            
        except Exception as e:
            self.error_handler.handle_error(e, {"action": "auto_update"})
            return False
    
    def cancel_update(self):
        """取消更新"""
        self.downloader.cancel_download()
        self.notifier.notify("update_cancelled", {})
    
    def get_update_status(self) -> Dict[str, Any]:
        """获取更新状态"""
        return {
            "is_checking": self._is_checking,
            "is_updating": self._is_updating,
            "last_check_time": self._last_check_time.isoformat() if self._last_check_time else None,
            "available_update": asdict(self._available_update) if self._available_update else None,
            "current_version": self.current_version,
            "config": asdict(self.config)
        }
    
    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        
        self.notifier.notify("config_updated", {"config": asdict(self.config)})


# 便捷函数
def create_updater(
    app_path: str, 
    current_version: str, 
    config: Optional[Dict[str, Any]] = None
) -> AutoUpdater:
    """创建自动更新器实例"""
    update_config = UpdateConfig()
    
    if config:
        for key, value in config.items():
            if hasattr(update_config, key):
                # 处理枚举类型
                if key == "strategy" and isinstance(value, str):
                    value = UpdateStrategy(value)
                elif key == "channel" and isinstance(value, str):
                    value = UpdateChannel(value)
                    
                setattr(update_config, key, value)
    
    return AutoUpdater(Path(app_path), current_version, update_config)


async def check_updates(
    app_path: str, 
    current_version: str, 
    server_url: str = "https://api.bamboofall.com/updates"
) -> Optional[VersionInfo]:
    """快速检查更新"""
    config = UpdateConfig(update_server_url=server_url)
    updater = AutoUpdater(Path(app_path), current_version, config)
    return await updater.check_for_updates()


if __name__ == "__main__":
    # 示例用法
    async def main():
        # 创建更新器
        updater = create_updater(
            app_path="/path/to/app",
            current_version="1.0.0",
            config={
                "strategy": "notify_only",
                "channel": "stable",
                "check_interval": 12
            }
        )
        
        # 添加进度回调
        def on_progress(progress: UpdateProgress):
            print(f"[{progress.status.value}] {progress.message} ({progress.progress:.1%})")
        
        updater.add_progress_callback(on_progress)
        
        # 添加通知回调
        def on_notification(event_type: str, data: Dict[str, Any]):
            print(f"通知: {event_type}, 数据: {data}")
        
        updater.notifier.add_callback(on_notification)
        
        # 检查更新
        version_info = await updater.check_for_updates()
        
        if version_info:
            print(f"发现新版本: {version_info.version}")
            
            # 下载更新
            update_file = await updater.download_update()
            
            if update_file:
                print(f"更新文件已下载: {update_file}")
                
                # 安装更新
                success = await updater.install_update(update_file)
                
                if success:
                    print("更新安装成功")
                else:
                    print("更新安装失败")
        else:
            print("没有可用更新")
    
    asyncio.run(main())