"""容器布局组件

提供各种容器组件，支持内容包装、边距、填充和装饰。
"""

import flet as ft
from typing import Optional, Union, List
from enum import Enum
from dataclasses import dataclass
from ..components.base import BaseComponent, theme_manager

class ContainerSize(str, Enum):
    """容器尺寸"""
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"
    FULL = "full"
    AUTO = "auto"

class ContainerVariant(str, Enum):
    """容器变体"""
    DEFAULT = "default"
    OUTLINED = "outlined"
    FILLED = "filled"
    ELEVATED = "elevated"
    TRANSPARENT = "transparent"

class BorderStyle(str, Enum):
    """边框样式"""
    SOLID = "solid"
    DASHED = "dashed"
    DOTTED = "dotted"
    NONE = "none"

@dataclass
class BorderConfig:
    """边框配置"""
    width: float = 1.0
    color: Optional[str] = None
    style: BorderStyle = BorderStyle.SOLID
    radius: Optional[float] = None
    
    def get_flet_border(self, theme) -> Optional[ft.border]:
        """获取Flet边框"""
        if self.style == BorderStyle.NONE:
            return None
        
        color = self.color or theme.colors.outline
        return ft.border.all(self.width, color)
    
    def get_flet_border_radius(self, theme) -> Optional[ft.border_radius]:
        """获取Flet边框圆角"""
        if self.radius is None:
            return None
        return ft.border_radius.all(self.radius)

@dataclass
class PaddingConfig:
    """内边距配置"""
    all: Optional[float] = None
    left: Optional[float] = None
    top: Optional[float] = None
    right: Optional[float] = None
    bottom: Optional[float] = None
    horizontal: Optional[float] = None
    vertical: Optional[float] = None
    
    def get_flet_padding(self) -> Optional[ft.padding]:
        """获取Flet内边距"""
        if self.all is not None:
            return ft.padding.all(self.all)
        
        if self.horizontal is not None or self.vertical is not None:
            h = self.horizontal or 0
            v = self.vertical or 0
            return ft.padding.symmetric(horizontal=h, vertical=v)
        
        if any([self.left, self.top, self.right, self.bottom]):
            return ft.padding.only(
                left=self.left or 0,
                top=self.top or 0,
                right=self.right or 0,
                bottom=self.bottom or 0
            )
        
        return None

@dataclass
class MarginConfig:
    """外边距配置"""
    all: Optional[float] = None
    left: Optional[float] = None
    top: Optional[float] = None
    right: Optional[float] = None
    bottom: Optional[float] = None
    horizontal: Optional[float] = None
    vertical: Optional[float] = None
    
    def get_flet_margin(self) -> Optional[ft.margin]:
        """获取Flet外边距"""
        if self.all is not None:
            return ft.margin.all(self.all)
        
        if self.horizontal is not None or self.vertical is not None:
            h = self.horizontal or 0
            v = self.vertical or 0
            return ft.margin.symmetric(horizontal=h, vertical=v)
        
        if any([self.left, self.top, self.right, self.bottom]):
            return ft.margin.only(
                left=self.left or 0,
                top=self.top or 0,
                right=self.right or 0,
                bottom=self.bottom or 0
            )
        
        return None

@dataclass
class ShadowConfig:
    """阴影配置"""
    blur_radius: float = 4.0
    spread_radius: float = 0.0
    offset_x: float = 0.0
    offset_y: float = 2.0
    color: Optional[str] = None
    
    def get_flet_shadow(self, theme) -> ft.BoxShadow:
        """获取Flet阴影"""
        color = self.color or f"rgba(0,0,0,0.1)"
        return ft.BoxShadow(
            spread_radius=self.spread_radius,
            blur_radius=self.blur_radius,
            color=color,
            offset=ft.Offset(self.offset_x, self.offset_y)
        )

@dataclass
class ContainerConfig:
    """容器配置"""
    size: ContainerSize = ContainerSize.AUTO
    variant: ContainerVariant = ContainerVariant.DEFAULT
    width: Optional[float] = None
    height: Optional[float] = None
    min_width: Optional[float] = None
    min_height: Optional[float] = None
    max_width: Optional[float] = None
    max_height: Optional[float] = None
    padding: Optional[PaddingConfig] = None
    margin: Optional[MarginConfig] = None
    border: Optional[BorderConfig] = None
    shadow: Optional[ShadowConfig] = None
    background_color: Optional[str] = None
    alignment: Optional[ft.alignment] = None
    expand: bool = False
    clip_behavior: bool = False
    
    def get_size_dimensions(self, theme) -> tuple[Optional[float], Optional[float]]:
        """根据尺寸获取宽高"""
        if self.width is not None and self.height is not None:
            return self.width, self.height
        
        size_map = {
            ContainerSize.SMALL: (200, 150),
            ContainerSize.MEDIUM: (400, 300),
            ContainerSize.LARGE: (600, 450),
            ContainerSize.FULL: (None, None),  # 由expand控制
            ContainerSize.AUTO: (None, None),
        }
        
        default_width, default_height = size_map.get(self.size, (None, None))
        return (
            self.width or default_width,
            self.height or default_height
        )

class Container(BaseComponent):
    """容器组件"""
    
    def __init__(
        self,
        content: Optional[ft.Control] = None,
        config: Optional[ContainerConfig] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.content = content
        self.config = config or ContainerConfig()
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            # 根据变体更新样式
            if self.config.variant == ContainerVariant.OUTLINED:
                if not self.config.border:
                    self.config.border = BorderConfig()
                self.config.border.color = self.theme.colors.outline
            elif self.config.variant == ContainerVariant.FILLED:
                self.config.background_color = self.theme.colors.surface_variant
            elif self.config.variant == ContainerVariant.ELEVATED:
                if not self.config.shadow:
                    self.config.shadow = ShadowConfig()
                self.config.background_color = self.theme.colors.surface
            elif self.config.variant == ContainerVariant.TRANSPARENT:
                self.config.background_color = "transparent"
            
            # 更新控件样式
            self._apply_styles()
    
    def _apply_styles(self):
        """应用样式到控件"""
        if not self._control:
            return
        
        # 背景色
        if self.config.background_color:
            self._control.bgcolor = self.config.background_color
        
        # 边框
        if self.config.border:
            self._control.border = self.config.border.get_flet_border(self.theme)
            border_radius = self.config.border.get_flet_border_radius(self.theme)
            if border_radius:
                self._control.border_radius = border_radius
        
        # 阴影
        if self.config.shadow:
            self._control.shadow = self.config.shadow.get_flet_shadow(self.theme)
        
        # 内边距
        if self.config.padding:
            self._control.padding = self.config.padding.get_flet_padding()
        
        # 外边距
        if self.config.margin:
            self._control.margin = self.config.margin.get_flet_margin()
    
    def build(self) -> ft.Control:
        """构建容器"""
        width, height = self.config.get_size_dimensions(self.theme)
        
        container = ft.Container(
            content=self.content,
            width=width,
            height=height,
            alignment=self.config.alignment,
            expand=self.config.expand,
            clip_behavior=ft.ClipBehavior.HARD_EDGE if self.config.clip_behavior else ft.ClipBehavior.NONE
        )
        
        # 应用样式
        self._control = container
        self._apply_styles()
        
        return container
    
    def set_content(self, content: ft.Control):
        """设置内容"""
        self.content = content
        if self._control:
            self._control.content = content
            self.update()
    
    def set_size(self, width: Optional[float] = None, height: Optional[float] = None):
        """设置尺寸"""
        if width is not None:
            self.config.width = width
        if height is not None:
            self.config.height = height
        
        if self._control:
            if width is not None:
                self._control.width = width
            if height is not None:
                self._control.height = height
            self.update()
    
    def set_padding(self, padding: Union[float, PaddingConfig]):
        """设置内边距"""
        if isinstance(padding, (int, float)):
            self.config.padding = PaddingConfig(all=padding)
        else:
            self.config.padding = padding
        
        if self._control:
            self._control.padding = self.config.padding.get_flet_padding()
            self.update()
    
    def set_margin(self, margin: Union[float, MarginConfig]):
        """设置外边距"""
        if isinstance(margin, (int, float)):
            self.config.margin = MarginConfig(all=margin)
        else:
            self.config.margin = margin
        
        if self._control:
            self._control.margin = self.config.margin.get_flet_margin()
            self.update()
    
    def set_background_color(self, color: str):
        """设置背景色"""
        self.config.background_color = color
        if self._control:
            self._control.bgcolor = color
            self.update()
    
    def set_border(self, border: BorderConfig):
        """设置边框"""
        self.config.border = border
        if self._control:
            self._control.border = border.get_flet_border(self.theme)
            border_radius = border.get_flet_border_radius(self.theme)
            if border_radius:
                self._control.border_radius = border_radius
            self.update()
    
    def set_shadow(self, shadow: ShadowConfig):
        """设置阴影"""
        self.config.shadow = shadow
        if self._control:
            self._control.shadow = shadow.get_flet_shadow(self.theme)
            self.update()

class Section(Container):
    """章节容器"""
    
    def __init__(
        self,
        title: Optional[str] = None,
        content: Optional[ft.Control] = None,
        collapsible: bool = False,
        collapsed: bool = False,
        **kwargs
    ):
        # 默认配置
        default_config = ContainerConfig(
            variant=ContainerVariant.OUTLINED,
            padding=PaddingConfig(all=16),
            margin=MarginConfig(vertical=8)
        )
        config = kwargs.pop('config', default_config)
        
        super().__init__(content=None, config=config, **kwargs)
        
        self.title = title
        self.section_content = content
        self.collapsible = collapsible
        self.collapsed = collapsed
    
    def build(self) -> ft.Control:
        """构建章节"""
        children = []
        
        # 标题
        if self.title:
            title_row = ft.Row([
                ft.Text(
                    self.title,
                    style=ft.TextThemeStyle.TITLE_MEDIUM,
                    weight=ft.FontWeight.BOLD
                )
            ])
            
            if self.collapsible:
                # 添加折叠按钮
                toggle_button = ft.IconButton(
                    icon=ft.icons.EXPAND_LESS if not self.collapsed else ft.icons.EXPAND_MORE,
                    on_click=self._toggle_collapse
                )
                title_row.controls.append(toggle_button)
            
            children.append(title_row)
        
        # 内容
        if self.section_content and not self.collapsed:
            if self.title:
                children.append(ft.Divider(height=1))
            children.append(self.section_content)
        
        # 创建列布局
        column = ft.Column(
            children,
            spacing=8,
            tight=True
        )
        
        # 设置为容器内容
        self.content = column
        
        return super().build()
    
    def _toggle_collapse(self, e):
        """切换折叠状态"""
        self.collapsed = not self.collapsed
        # 重新构建
        if self._control:
            self._control = self.build()
            self.update()
    
    def set_title(self, title: str):
        """设置标题"""
        self.title = title
        if self._control:
            self._control = self.build()
            self.update()
    
    def set_content(self, content: ft.Control):
        """设置内容"""
        self.section_content = content
        if self._control:
            self._control = self.build()
            self.update()

class Card(Container):
    """卡片容器"""
    
    def __init__(
        self,
        content: Optional[ft.Control] = None,
        elevated: bool = True,
        **kwargs
    ):
        # 默认配置
        default_config = ContainerConfig(
            variant=ContainerVariant.ELEVATED if elevated else ContainerVariant.OUTLINED,
            padding=PaddingConfig(all=16),
            border=BorderConfig(radius=8) if not elevated else None,
            shadow=ShadowConfig() if elevated else None
        )
        config = kwargs.pop('config', default_config)
        
        super().__init__(content=content, config=config, **kwargs)

class Panel(Container):
    """面板容器"""
    
    def __init__(
        self,
        content: Optional[ft.Control] = None,
        **kwargs
    ):
        # 默认配置
        default_config = ContainerConfig(
            variant=ContainerVariant.FILLED,
            padding=PaddingConfig(all=12),
            border=BorderConfig(radius=4)
        )
        config = kwargs.pop('config', default_config)
        
        super().__init__(content=content, config=config, **kwargs)

# 容器工具函数

def create_centered_container(
    content: ft.Control,
    width: Optional[float] = None,
    height: Optional[float] = None,
    background_color: Optional[str] = None
) -> Container:
    """创建居中容器"""
    config = ContainerConfig(
        width=width,
        height=height,
        background_color=background_color,
        alignment=ft.alignment.center
    )
    
    return Container(content=content, config=config)

def create_scrollable_container(
    content: ft.Control,
    width: Optional[float] = None,
    height: Optional[float] = None
) -> ft.Container:
    """创建可滚动容器"""
    scrollable_content = ft.Column(
        [content],
        scroll=ft.ScrollMode.AUTO,
        expand=True
    )
    
    return ft.Container(
        content=scrollable_content,
        width=width,
        height=height
    )

def create_responsive_container(
    content: ft.Control,
    mobile_width: Optional[float] = None,
    tablet_width: Optional[float] = None,
    desktop_width: Optional[float] = None
) -> Container:
    """创建响应式容器"""
    # 这里简化实现，实际应该根据屏幕尺寸动态调整
    config = ContainerConfig(
        width=desktop_width or tablet_width or mobile_width,
        size=ContainerSize.AUTO
    )
    
    return Container(content=content, config=config)

def create_loading_container(
    loading: bool = True,
    content: Optional[ft.Control] = None,
    loading_text: str = "加载中..."
) -> Container:
    """创建加载容器"""
    if loading:
        loading_content = ft.Column([
            ft.ProgressRing(),
            ft.Text(loading_text)
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
        
        display_content = loading_content
    else:
        display_content = content
    
    config = ContainerConfig(
        alignment=ft.alignment.center,
        padding=PaddingConfig(all=20)
    )
    
    return Container(content=display_content, config=config)

# 导出
__all__ = [
    'ContainerSize',
    'ContainerVariant',
    'BorderStyle',
    'BorderConfig',
    'PaddingConfig',
    'MarginConfig',
    'ShadowConfig',
    'ContainerConfig',
    'Container',
    'Section',
    'Card',
    'Panel',
    'create_centered_container',
    'create_scrollable_container',
    'create_responsive_container',
    'create_loading_container',
]