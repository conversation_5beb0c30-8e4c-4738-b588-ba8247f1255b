"""创作工作台界面

提供核心的创作界面，包括三栏式布局、富文本编辑器、分屏显示、禅模式等功能。
"""

import flet as ft
from typing import List, Optional, Dict, Any, Callable
from enum import Enum
from dataclasses import dataclass
import json

from ..components.base import BaseComponent, Button, Input, TextArea, Card, Modal, theme_manager
from ..layouts.flex import FlexLayout, FlexDirection, JustifyContent, AlignItems
from ..layouts.container import Container, ContainerConfig, PaddingConfig
from ..layouts.stack import StackLayout, StackDirection
from ...services.project_service import ProjectService, Project
from ...services.chapter_service import ChapterService, Chapter
from ...services.ai_service import AIService
from ...models.chapter import ChapterStatus

class WorkspaceMode(str, Enum):
    """工作台模式"""
    NORMAL = "normal"  # 正常模式
    ZEN = "zen"  # 禅模式
    SPLIT = "split"  # 分屏模式
    PREVIEW = "preview"  # 预览模式

class PanelType(str, Enum):
    """面板类型"""
    NAVIGATION = "navigation"  # 导航面板
    EDITOR = "editor"  # 编辑器面板
    TOOLS = "tools"  # 工具面板
    OUTLINE = "outline"  # 大纲面板
    AI_ASSISTANT = "ai_assistant"  # AI助手面板
    RESEARCH = "research"  # 资料面板

class EditorTheme(str, Enum):
    """编辑器主题"""
    LIGHT = "light"  # 浅色主题
    DARK = "dark"  # 深色主题
    SEPIA = "sepia"  # 护眼主题
    HIGH_CONTRAST = "high_contrast"  # 高对比度主题

@dataclass
class EditorSettings:
    """编辑器设置"""
    font_family: str = "Microsoft YaHei"
    font_size: int = 14
    line_height: float = 1.6
    theme: EditorTheme = EditorTheme.LIGHT
    word_wrap: bool = True
    show_line_numbers: bool = False
    auto_save: bool = True
    auto_save_interval: int = 30  # 秒
    spell_check: bool = True
    typewriter_mode: bool = False
    focus_mode: bool = False

@dataclass
class WorkspaceLayout:
    """工作台布局"""
    navigation_width: int = 250
    tools_width: int = 300
    editor_width: Optional[int] = None  # 自适应
    show_navigation: bool = True
    show_tools: bool = True
    mode: WorkspaceMode = WorkspaceMode.NORMAL

class RichTextEditor(BaseComponent):
    """富文本编辑器组件"""
    
    def __init__(
        self,
        content: str = "",
        settings: Optional[EditorSettings] = None,
        on_change: Optional[Callable[[str], None]] = None,
        on_save: Optional[Callable[[str], None]] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.content = content
        self.settings = settings or EditorSettings()
        self.on_change = on_change
        self.on_save = on_save
        
        # 编辑器状态
        self.is_modified = False
        self.word_count = 0
        self.character_count = 0
        self.cursor_position = 0
        
        # 创建编辑器控件
        self.text_field = ft.TextField(
            value=content,
            multiline=True,
            expand=True,
            border=ft.InputBorder.NONE,
            content_padding=ft.padding.all(20),
            text_style=ft.TextStyle(
                font_family=self.settings.font_family,
                size=self.settings.font_size
            ),
            on_change=self._on_text_change,
            on_focus=self._on_focus,
            on_blur=self._on_blur
        )
    
    def _on_text_change(self, e):
        """文本变化事件"""
        self.content = e.control.value
        self.is_modified = True
        self._update_statistics()
        
        if self.on_change:
            self.on_change(self.content)
    
    def _on_focus(self, e):
        """获得焦点事件"""
        if self.settings.focus_mode:
            self._enter_focus_mode()
    
    def _on_blur(self, e):
        """失去焦点事件"""
        if self.settings.focus_mode:
            self._exit_focus_mode()
    
    def _update_statistics(self):
        """更新统计信息"""
        self.character_count = len(self.content)
        self.word_count = len(self.content.split()) if self.content.strip() else 0
    
    def _enter_focus_mode(self):
        """进入专注模式"""
        # 这里可以添加专注模式的视觉效果
        pass
    
    def _exit_focus_mode(self):
        """退出专注模式"""
        # 这里可以添加退出专注模式的视觉效果
        pass
    
    def set_content(self, content: str):
        """设置内容"""
        self.content = content
        self.text_field.value = content
        self.is_modified = False
        self._update_statistics()
        if self._control:
            self.update()
    
    def get_content(self) -> str:
        """获取内容"""
        return self.content
    
    def save(self):
        """保存内容"""
        if self.on_save and self.is_modified:
            self.on_save(self.content)
            self.is_modified = False
    
    def insert_text(self, text: str):
        """插入文本"""
        # 这里应该在光标位置插入文本
        current_value = self.text_field.value or ""
        self.text_field.value = current_value + text
        self._on_text_change(type('obj', (object,), {'control': self.text_field})())
    
    def apply_formatting(self, format_type: str, value: Any = None):
        """应用格式化"""
        # 这里可以实现各种格式化功能
        # 由于Flet的TextField比较基础，这里只是示例
        pass
    
    def build(self) -> ft.Control:
        """构建编辑器"""
        return ft.Container(
            content=self.text_field,
            bgcolor=self._get_theme_background(),
            border_radius=8,
            expand=True
        )
    
    def _get_theme_background(self) -> str:
        """获取主题背景色"""
        theme_colors = {
            EditorTheme.LIGHT: "white",
            EditorTheme.DARK: "#1e1e1e",
            EditorTheme.SEPIA: "#f4f1ea",
            EditorTheme.HIGH_CONTRAST: "black"
        }
        return theme_colors.get(self.settings.theme, "white")

class NavigationPanel(BaseComponent):
    """导航面板组件"""
    
    def __init__(
        self,
        project: Project,
        chapters: List[Chapter],
        current_chapter: Optional[Chapter] = None,
        on_chapter_select: Optional[Callable[[Chapter], None]] = None,
        on_chapter_create: Optional[Callable[[], None]] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.project = project
        self.chapters = chapters
        self.current_chapter = current_chapter
        self.on_chapter_select = on_chapter_select
        self.on_chapter_create = on_chapter_create
    
    def _create_chapter_item(self, chapter: Chapter) -> ft.Control:
        """创建章节项"""
        is_current = self.current_chapter and chapter.id == self.current_chapter.id
        
        # 状态图标
        status_icons = {
            ChapterStatus.DRAFT: ft.icons.EDIT,
            ChapterStatus.IN_PROGRESS: ft.icons.CREATE,
            ChapterStatus.COMPLETED: ft.icons.CHECK_CIRCLE,
            ChapterStatus.PUBLISHED: ft.icons.PUBLISH
        }
        
        status_colors = {
            ChapterStatus.DRAFT: "orange",
            ChapterStatus.IN_PROGRESS: "blue",
            ChapterStatus.COMPLETED: "green",
            ChapterStatus.PUBLISHED: "purple"
        }
        
        # 章节信息
        chapter_info = ft.Column([
            ft.Text(
                chapter.title,
                size=14,
                weight=ft.FontWeight.BOLD if is_current else ft.FontWeight.NORMAL,
                max_lines=2,
                overflow=ft.TextOverflow.ELLIPSIS
            ),
            ft.Row([
                ft.Icon(
                    status_icons.get(chapter.status, ft.icons.HELP),
                    size=12,
                    color=status_colors.get(chapter.status, "grey")
                ),
                ft.Text(
                    f"{chapter.word_count:,}字",
                    size=10,
                    color=self.theme.colors.on_surface_variant
                )
            ], spacing=4)
        ], spacing=4)
        
        # 章节项容器
        item = ft.Container(
            content=chapter_info,
            padding=ft.padding.all(12),
            bgcolor=self.theme.colors.primary_container if is_current else None,
            border_radius=8,
            on_click=lambda e, c=chapter: self._on_chapter_click(c)
        )
        
        return item
    
    def _on_chapter_click(self, chapter: Chapter):
        """章节点击事件"""
        if self.on_chapter_select:
            self.on_chapter_select(chapter)
    
    def _create_project_info(self) -> ft.Control:
        """创建项目信息"""
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    self.project.title,
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    max_lines=2
                ),
                ft.Text(
                    f"总字数: {self.project.word_count:,}",
                    size=12,
                    color=self.theme.colors.on_surface_variant
                ),
                ft.Text(
                    f"章节数: {len(self.chapters)}",
                    size=12,
                    color=self.theme.colors.on_surface_variant
                )
            ], spacing=4),
            padding=ft.padding.all(16),
            bgcolor=self.theme.colors.surface_variant,
            border_radius=8
        )
    
    def build(self) -> ft.Control:
        """构建导航面板"""
        # 项目信息
        project_info = self._create_project_info()
        
        # 章节列表
        chapter_items = []
        for chapter in self.chapters:
            item = self._create_chapter_item(chapter)
            chapter_items.append(item)
        
        # 添加章节按钮
        add_button = ft.ElevatedButton(
            "新建章节",
            icon=ft.icons.ADD,
            on_click=lambda e: self.on_chapter_create() if self.on_chapter_create else None,
            width=200
        )
        
        # 章节列表容器
        chapters_list = ft.Column(
            chapter_items,
            spacing=8,
            scroll=ft.ScrollMode.AUTO
        )
        
        return ft.Container(
            content=ft.Column([
                project_info,
                ft.Text("章节列表", size=14, weight=ft.FontWeight.BOLD),
                ft.Container(content=chapters_list, expand=True),
                add_button
            ], spacing=16),
            padding=ft.padding.all(16),
            width=250,
            bgcolor=self.theme.colors.surface,
            border=ft.border.only(right=ft.BorderSide(1, self.theme.colors.outline_variant))
        )

class ToolsPanel(BaseComponent):
    """工具面板组件"""
    
    def __init__(
        self,
        ai_service: Optional[AIService] = None,
        on_ai_assist: Optional[Callable[[str], None]] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.ai_service = ai_service
        self.on_ai_assist = on_ai_assist
        self.current_tab = "outline"
    
    def _create_outline_tab(self) -> ft.Control:
        """创建大纲标签页"""
        return ft.Container(
            content=ft.Column([
                ft.Text("章节大纲", size=14, weight=ft.FontWeight.BOLD),
                ft.TextField(
                    label="大纲内容",
                    multiline=True,
                    min_lines=10,
                    max_lines=20,
                    hint_text="在这里编写章节大纲..."
                ),
                ft.Row([
                    ft.ElevatedButton("保存大纲", icon=ft.icons.SAVE),
                    ft.OutlinedButton("生成大纲", icon=ft.icons.AUTO_AWESOME)
                ], spacing=8)
            ], spacing=12),
            padding=ft.padding.all(16)
        )
    
    def _create_ai_tab(self) -> ft.Control:
        """创建AI助手标签页"""
        return ft.Container(
            content=ft.Column([
                ft.Text("AI写作助手", size=14, weight=ft.FontWeight.BOLD),
                ft.TextField(
                    label="输入提示",
                    hint_text="描述你想要AI帮助的内容...",
                    multiline=True,
                    min_lines=3
                ),
                ft.Row([
                    ft.ElevatedButton(
                        "续写",
                        icon=ft.icons.EDIT,
                        on_click=self._on_ai_continue
                    ),
                    ft.ElevatedButton(
                        "润色",
                        icon=ft.icons.AUTO_FIX_HIGH,
                        on_click=self._on_ai_polish
                    )
                ], spacing=8),
                ft.Row([
                    ft.ElevatedButton(
                        "扩写",
                        icon=ft.icons.EXPAND_MORE,
                        on_click=self._on_ai_expand
                    ),
                    ft.ElevatedButton(
                        "总结",
                        icon=ft.icons.SUMMARIZE,
                        on_click=self._on_ai_summarize
                    )
                ], spacing=8),
                ft.Divider(),
                ft.Text("AI建议", size=12, weight=ft.FontWeight.BOLD),
                ft.Container(
                    content=ft.Text(
                        "AI建议将在这里显示...",
                        size=12,
                        color=self.theme.colors.on_surface_variant
                    ),
                    height=150,
                    padding=ft.padding.all(8),
                    bgcolor=self.theme.colors.surface_variant,
                    border_radius=8
                )
            ], spacing=12),
            padding=ft.padding.all(16)
        )
    
    def _create_research_tab(self) -> ft.Control:
        """创建资料标签页"""
        return ft.Container(
            content=ft.Column([
                ft.Text("写作资料", size=14, weight=ft.FontWeight.BOLD),
                ft.TextField(
                    label="搜索资料",
                    prefix_icon=ft.icons.SEARCH,
                    hint_text="搜索人物、设定、情节..."
                ),
                ft.Row([
                    ft.Chip(label=ft.Text("人物设定")),
                    ft.Chip(label=ft.Text("世界观")),
                    ft.Chip(label=ft.Text("情节线"))
                ], wrap=True),
                ft.Container(
                    content=ft.Column([
                        ft.ListTile(
                            leading=ft.Icon(ft.icons.PERSON),
                            title=ft.Text("主角设定"),
                            subtitle=ft.Text("主要角色的背景和性格")
                        ),
                        ft.ListTile(
                            leading=ft.Icon(ft.icons.LOCATION_ON),
                            title=ft.Text("场景描述"),
                            subtitle=ft.Text("重要场景的详细描述")
                        )
                    ]),
                    height=200,
                    bgcolor=self.theme.colors.surface_variant,
                    border_radius=8,
                    padding=ft.padding.all(8)
                )
            ], spacing=12),
            padding=ft.padding.all(16)
        )
    
    def _on_ai_continue(self, e):
        """AI续写"""
        if self.on_ai_assist:
            self.on_ai_assist("continue")
    
    def _on_ai_polish(self, e):
        """AI润色"""
        if self.on_ai_assist:
            self.on_ai_assist("polish")
    
    def _on_ai_expand(self, e):
        """AI扩写"""
        if self.on_ai_assist:
            self.on_ai_assist("expand")
    
    def _on_ai_summarize(self, e):
        """AI总结"""
        if self.on_ai_assist:
            self.on_ai_assist("summarize")
    
    def build(self) -> ft.Control:
        """构建工具面板"""
        # 标签页
        tabs = ft.Tabs(
            selected_index=0,
            tabs=[
                ft.Tab(
                    text="大纲",
                    icon=ft.icons.LIST_ALT,
                    content=self._create_outline_tab()
                ),
                ft.Tab(
                    text="AI助手",
                    icon=ft.icons.SMART_TOY,
                    content=self._create_ai_tab()
                ),
                ft.Tab(
                    text="资料",
                    icon=ft.icons.FOLDER_OPEN,
                    content=self._create_research_tab()
                )
            ],
            expand=True
        )
        
        return ft.Container(
            content=tabs,
            width=300,
            bgcolor=self.theme.colors.surface,
            border=ft.border.only(left=ft.BorderSide(1, self.theme.colors.outline_variant))
        )

class WorkspaceToolbar(BaseComponent):
    """工作台工具栏"""
    
    def __init__(
        self,
        current_mode: WorkspaceMode = WorkspaceMode.NORMAL,
        on_mode_change: Optional[Callable[[WorkspaceMode], None]] = None,
        on_save: Optional[Callable[[], None]] = None,
        on_settings: Optional[Callable[[], None]] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.current_mode = current_mode
        self.on_mode_change = on_mode_change
        self.on_save = on_save
        self.on_settings = on_settings
    
    def build(self) -> ft.Control:
        """构建工具栏"""
        # 模式切换按钮
        mode_buttons = ft.Row([
            ft.IconButton(
                icon=ft.icons.VIEW_COLUMN,
                tooltip="正常模式",
                selected=self.current_mode == WorkspaceMode.NORMAL,
                on_click=lambda e: self._change_mode(WorkspaceMode.NORMAL)
            ),
            ft.IconButton(
                icon=ft.icons.FULLSCREEN,
                tooltip="禅模式",
                selected=self.current_mode == WorkspaceMode.ZEN,
                on_click=lambda e: self._change_mode(WorkspaceMode.ZEN)
            ),
            ft.IconButton(
                icon=ft.icons.VIEW_AGENDA,
                tooltip="分屏模式",
                selected=self.current_mode == WorkspaceMode.SPLIT,
                on_click=lambda e: self._change_mode(WorkspaceMode.SPLIT)
            ),
            ft.IconButton(
                icon=ft.icons.PREVIEW,
                tooltip="预览模式",
                selected=self.current_mode == WorkspaceMode.PREVIEW,
                on_click=lambda e: self._change_mode(WorkspaceMode.PREVIEW)
            )
        ])
        
        # 操作按钮
        action_buttons = ft.Row([
            ft.IconButton(
                icon=ft.icons.SAVE,
                tooltip="保存",
                on_click=lambda e: self.on_save() if self.on_save else None
            ),
            ft.IconButton(
                icon=ft.icons.UNDO,
                tooltip="撤销"
            ),
            ft.IconButton(
                icon=ft.icons.REDO,
                tooltip="重做"
            ),
            ft.VerticalDivider(width=1),
            ft.IconButton(
                icon=ft.icons.SETTINGS,
                tooltip="设置",
                on_click=lambda e: self.on_settings() if self.on_settings else None
            )
        ])
        
        return ft.Container(
            content=ft.Row([
                mode_buttons,
                ft.Container(expand=True),  # 占位符
                action_buttons
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.symmetric(horizontal=16, vertical=8),
            bgcolor=self.theme.colors.surface_variant,
            border=ft.border.only(bottom=ft.BorderSide(1, self.theme.colors.outline_variant))
        )
    
    def _change_mode(self, mode: WorkspaceMode):
        """切换模式"""
        self.current_mode = mode
        if self.on_mode_change:
            self.on_mode_change(mode)
        if self._control:
            self.update()

class WritingWorkspace(BaseComponent):
    """创作工作台主组件"""
    
    def __init__(
        self,
        project: Project,
        project_service: ProjectService,
        chapter_service: ChapterService,
        ai_service: Optional[AIService] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.project = project
        self.project_service = project_service
        self.chapter_service = chapter_service
        self.ai_service = ai_service
        
        # 状态
        self.current_chapter: Optional[Chapter] = None
        self.chapters: List[Chapter] = []
        self.workspace_mode = WorkspaceMode.NORMAL
        self.layout = WorkspaceLayout()
        self.editor_settings = EditorSettings()
        
        # 组件
        self.editor = RichTextEditor(
            settings=self.editor_settings,
            on_change=self._on_content_change,
            on_save=self._on_save_content
        )
        
        self.toolbar = WorkspaceToolbar(
            current_mode=self.workspace_mode,
            on_mode_change=self._on_mode_change,
            on_save=self._save_current_chapter,
            on_settings=self._show_settings
        )
        
        # 加载数据
        self._load_chapters()
    
    def _load_chapters(self):
        """加载章节列表"""
        try:
            self.chapters = self.chapter_service.get_chapters_by_project(self.project.id)
            if self.chapters and not self.current_chapter:
                self.current_chapter = self.chapters[0]
                self._load_chapter_content()
        except Exception as e:
            print(f"加载章节失败: {e}")
            self.chapters = []
    
    def _load_chapter_content(self):
        """加载章节内容"""
        if self.current_chapter:
            try:
                content = self.chapter_service.get_chapter_content(self.current_chapter.id)
                self.editor.set_content(content)
            except Exception as e:
                print(f"加载章节内容失败: {e}")
    
    def _on_content_change(self, content: str):
        """内容变化事件"""
        # 自动保存逻辑
        if self.editor_settings.auto_save:
            # 这里可以实现定时自动保存
            pass
    
    def _on_save_content(self, content: str):
        """保存内容事件"""
        self._save_current_chapter()
    
    def _save_current_chapter(self):
        """保存当前章节"""
        if self.current_chapter and self.editor.is_modified:
            try:
                content = self.editor.get_content()
                self.chapter_service.update_chapter_content(self.current_chapter.id, content)
                self.editor.is_modified = False
                print("章节已保存")
            except Exception as e:
                print(f"保存章节失败: {e}")
    
    def _on_chapter_select(self, chapter: Chapter):
        """章节选择事件"""
        # 保存当前章节
        if self.current_chapter and self.editor.is_modified:
            self._save_current_chapter()
        
        # 切换到新章节
        self.current_chapter = chapter
        self._load_chapter_content()
        
        # 更新UI
        if self._control:
            self.update()
    
    def _on_chapter_create(self):
        """创建新章节"""
        try:
            # 创建新章节
            chapter_data = {
                'title': f"第{len(self.chapters) + 1}章",
                'project_id': self.project.id,
                'order': len(self.chapters) + 1
            }
            new_chapter = self.chapter_service.create_chapter(**chapter_data)
            
            # 更新章节列表
            self.chapters.append(new_chapter)
            self.current_chapter = new_chapter
            self.editor.set_content("")
            
            # 更新UI
            if self._control:
                self.update()
                
        except Exception as e:
            print(f"创建章节失败: {e}")
    
    def _on_mode_change(self, mode: WorkspaceMode):
        """模式变化事件"""
        self.workspace_mode = mode
        
        # 更新布局
        if mode == WorkspaceMode.ZEN:
            self.layout.show_navigation = False
            self.layout.show_tools = False
        elif mode == WorkspaceMode.SPLIT:
            self.layout.show_navigation = True
            self.layout.show_tools = True
        else:
            self.layout.show_navigation = True
            self.layout.show_tools = True
        
        # 更新UI
        if self._control:
            self.update()
    
    def _on_ai_assist(self, assist_type: str):
        """AI助手事件"""
        if not self.ai_service:
            print("AI服务不可用")
            return
        
        try:
            current_content = self.editor.get_content()
            
            if assist_type == "continue":
                # AI续写
                result = self.ai_service.continue_writing(current_content)
                self.editor.insert_text(result)
            elif assist_type == "polish":
                # AI润色
                result = self.ai_service.polish_text(current_content)
                self.editor.set_content(result)
            elif assist_type == "expand":
                # AI扩写
                result = self.ai_service.expand_text(current_content)
                self.editor.set_content(result)
            elif assist_type == "summarize":
                # AI总结
                result = self.ai_service.summarize_text(current_content)
                # 显示总结结果
                print(f"AI总结: {result}")
                
        except Exception as e:
            print(f"AI助手失败: {e}")
    
    def _show_settings(self):
        """显示设置对话框"""
        # 这里应该显示编辑器设置对话框
        print("显示设置对话框")
    
    def _create_navigation_panel(self) -> ft.Control:
        """创建导航面板"""
        return NavigationPanel(
            project=self.project,
            chapters=self.chapters,
            current_chapter=self.current_chapter,
            on_chapter_select=self._on_chapter_select,
            on_chapter_create=self._on_chapter_create
        ).get_control()
    
    def _create_tools_panel(self) -> ft.Control:
        """创建工具面板"""
        return ToolsPanel(
            ai_service=self.ai_service,
            on_ai_assist=self._on_ai_assist
        ).get_control()
    
    def _create_editor_panel(self) -> ft.Control:
        """创建编辑器面板"""
        # 编辑器状态栏
        status_bar = ft.Container(
            content=ft.Row([
                ft.Text(
                    f"字数: {self.editor.word_count:,}",
                    size=12,
                    color=self.theme.colors.on_surface_variant
                ),
                ft.Text(
                    f"字符: {self.editor.character_count:,}",
                    size=12,
                    color=self.theme.colors.on_surface_variant
                ),
                ft.Text(
                    self.current_chapter.title if self.current_chapter else "未选择章节",
                    size=12,
                    weight=ft.FontWeight.BOLD
                )
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.symmetric(horizontal=16, vertical=8),
            bgcolor=self.theme.colors.surface_variant,
            border=ft.border.only(top=ft.BorderSide(1, self.theme.colors.outline_variant))
        )
        
        return ft.Column([
            ft.Container(content=self.editor.get_control(), expand=True),
            status_bar
        ], spacing=0)
    
    def build(self) -> ft.Control:
        """构建工作台"""
        # 根据模式构建不同的布局
        if self.workspace_mode == WorkspaceMode.ZEN:
            # 禅模式：只显示编辑器
            return ft.Column([
                self.toolbar.get_control(),
                self._create_editor_panel()
            ], spacing=0)
        
        # 构建面板列表
        panels = []
        
        # 导航面板
        if self.layout.show_navigation:
            panels.append(self._create_navigation_panel())
        
        # 编辑器面板
        panels.append(ft.Container(
            content=self._create_editor_panel(),
            expand=True
        ))
        
        # 工具面板
        if self.layout.show_tools:
            panels.append(self._create_tools_panel())
        
        # 主布局
        main_content = ft.Row(panels, spacing=0, expand=True)
        
        return ft.Column([
            self.toolbar.get_control(),
            main_content
        ], spacing=0)

# 导出
__all__ = [
    'WorkspaceMode',
    'PanelType',
    'EditorTheme',
    'EditorSettings',
    'WorkspaceLayout',
    'RichTextEditor',
    'NavigationPanel',
    'ToolsPanel',
    'WorkspaceToolbar',
    'WritingWorkspace',
]