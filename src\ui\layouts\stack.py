"""堆叠布局组件

提供堆叠布局系统，支持层叠显示和Z轴排序。
"""

import flet as ft
from typing import List, Optional, Union
from enum import Enum
from dataclasses import dataclass
from ..components.base import BaseComponent, theme_manager

class StackDirection(str, Enum):
    """堆叠方向"""
    VERTICAL = "vertical"
    HORIZONTAL = "horizontal"

class StackAlignment(str, Enum):
    """堆叠对齐方式"""
    TOP_LEFT = "top_left"
    TOP_CENTER = "top_center"
    TOP_RIGHT = "top_right"
    CENTER_LEFT = "center_left"
    CENTER = "center"
    CENTER_RIGHT = "center_right"
    BOTTOM_LEFT = "bottom_left"
    BOTTOM_CENTER = "bottom_center"
    BOTTOM_RIGHT = "bottom_right"

@dataclass
class StackConfig:
    """堆叠布局配置"""
    alignment: StackAlignment = StackAlignment.TOP_LEFT
    fit: bool = True  # 是否适应内容大小
    clip_behavior: bool = True  # 是否裁剪溢出内容
    
    def get_flet_alignment(self) -> ft.alignment:
        """获取Flet对齐方式"""
        alignment_map = {
            StackAlignment.TOP_LEFT: ft.alignment.top_left,
            StackAlignment.TOP_CENTER: ft.alignment.top_center,
            StackAlignment.TOP_RIGHT: ft.alignment.top_right,
            StackAlignment.CENTER_LEFT: ft.alignment.center_left,
            StackAlignment.CENTER: ft.alignment.center,
            StackAlignment.CENTER_RIGHT: ft.alignment.center_right,
            StackAlignment.BOTTOM_LEFT: ft.alignment.bottom_left,
            StackAlignment.BOTTOM_CENTER: ft.alignment.bottom_center,
            StackAlignment.BOTTOM_RIGHT: ft.alignment.bottom_right,
        }
        return alignment_map.get(self.alignment, ft.alignment.top_left)

@dataclass
class StackItemConfig:
    """堆叠项配置"""
    left: Optional[float] = None  # 左边距
    top: Optional[float] = None  # 上边距
    right: Optional[float] = None  # 右边距
    bottom: Optional[float] = None  # 下边距
    width: Optional[float] = None  # 宽度
    height: Optional[float] = None  # 高度
    z_index: int = 0  # Z轴索引
    alignment: Optional[StackAlignment] = None  # 对齐方式
    
    def get_flet_alignment(self) -> Optional[ft.alignment]:
        """获取Flet对齐方式"""
        if self.alignment is None:
            return None
        
        alignment_map = {
            StackAlignment.TOP_LEFT: ft.alignment.top_left,
            StackAlignment.TOP_CENTER: ft.alignment.top_center,
            StackAlignment.TOP_RIGHT: ft.alignment.top_right,
            StackAlignment.CENTER_LEFT: ft.alignment.center_left,
            StackAlignment.CENTER: ft.alignment.center,
            StackAlignment.CENTER_RIGHT: ft.alignment.center_right,
            StackAlignment.BOTTOM_LEFT: ft.alignment.bottom_left,
            StackAlignment.BOTTOM_CENTER: ft.alignment.bottom_center,
            StackAlignment.BOTTOM_RIGHT: ft.alignment.bottom_right,
        }
        return alignment_map.get(self.alignment, ft.alignment.top_left)

class StackItem(BaseComponent):
    """堆叠项组件"""
    
    def __init__(
        self,
        content: ft.Control,
        config: Optional[StackItemConfig] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.content = content
        self.config = config or StackItemConfig()
    
    def _update_theme_styles(self):
        """更新主题样式"""
        # 堆叠项的样式更新由内容组件处理
        pass
    
    def build(self) -> ft.Control:
        """构建堆叠项"""
        # 在Flet中，堆叠项通过Positioned控件实现
        positioned = ft.Container(
            content=self.content,
            left=self.config.left,
            top=self.config.top,
            right=self.config.right,
            bottom=self.config.bottom,
            width=self.config.width,
            height=self.config.height,
            alignment=self.config.get_flet_alignment()
        )
        
        return positioned
    
    def set_position(self, left: Optional[float] = None, top: Optional[float] = None,
                    right: Optional[float] = None, bottom: Optional[float] = None):
        """设置位置"""
        if left is not None:
            self.config.left = left
        if top is not None:
            self.config.top = top
        if right is not None:
            self.config.right = right
        if bottom is not None:
            self.config.bottom = bottom
        
        if self._control:
            self._control.left = self.config.left
            self._control.top = self.config.top
            self._control.right = self.config.right
            self._control.bottom = self.config.bottom
            self.update()
    
    def set_size(self, width: Optional[float] = None, height: Optional[float] = None):
        """设置尺寸"""
        if width is not None:
            self.config.width = width
        if height is not None:
            self.config.height = height
        
        if self._control:
            self._control.width = self.config.width
            self._control.height = self.config.height
            self.update()
    
    def set_z_index(self, z_index: int):
        """设置Z轴索引"""
        self.config.z_index = z_index

class StackLayout(BaseComponent):
    """堆叠布局组件"""
    
    def __init__(
        self,
        children: List[Union[ft.Control, StackItem]] = None,
        config: Optional[StackConfig] = None,
        width: Optional[float] = None,
        height: Optional[float] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.children = children or []
        self.config = config or StackConfig()
        self.width = width
        self.height = height
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            # 更新堆叠布局样式
            pass
    
    def _prepare_children(self) -> List[ft.Control]:
        """准备子组件"""
        prepared_children = []
        
        # 按z_index排序
        sorted_children = sorted(
            self.children,
            key=lambda x: x.config.z_index if isinstance(x, StackItem) else 0
        )
        
        for child in sorted_children:
            if isinstance(child, StackItem):
                prepared_children.append(child.get_control())
            else:
                # 普通控件包装为容器
                container = ft.Container(
                    content=child,
                    alignment=self.config.get_flet_alignment()
                )
                prepared_children.append(container)
        
        return prepared_children
    
    def build(self) -> ft.Control:
        """构建堆叠布局"""
        if not self.children:
            return ft.Container(
                width=self.width,
                height=self.height
            )
        
        prepared_children = self._prepare_children()
        
        # 创建堆叠容器
        stack = ft.Stack(
            prepared_children,
            width=self.width,
            height=self.height,
            alignment=self.config.get_flet_alignment(),
            expand=not self.config.fit
        )
        
        return stack
    
    def add_child(self, child: Union[ft.Control, StackItem], index: Optional[int] = None):
        """添加子组件"""
        if index is None:
            self.children.append(child)
        else:
            self.children.insert(index, child)
        
        # 重新构建布局
        if self._control:
            self._control = self.build()
            self.update()
    
    def remove_child(self, index: int):
        """移除子组件"""
        if 0 <= index < len(self.children):
            self.children.pop(index)
            
            # 重新构建布局
            if self._control:
                self._control = self.build()
                self.update()
    
    def clear_children(self):
        """清空所有子组件"""
        self.children.clear()
        
        # 重新构建布局
        if self._control:
            self._control = self.build()
            self.update()
    
    def bring_to_front(self, index: int):
        """将指定项移到最前"""
        if 0 <= index < len(self.children):
            child = self.children[index]
            if isinstance(child, StackItem):
                # 找到最大的z_index并加1
                max_z = max(
                    (c.config.z_index for c in self.children if isinstance(c, StackItem)),
                    default=0
                )
                child.set_z_index(max_z + 1)
                
                # 重新构建布局
                if self._control:
                    self._control = self.build()
                    self.update()
    
    def send_to_back(self, index: int):
        """将指定项移到最后"""
        if 0 <= index < len(self.children):
            child = self.children[index]
            if isinstance(child, StackItem):
                # 找到最小的z_index并减1
                min_z = min(
                    (c.config.z_index for c in self.children if isinstance(c, StackItem)),
                    default=0
                )
                child.set_z_index(min_z - 1)
                
                # 重新构建布局
                if self._control:
                    self._control = self.build()
                    self.update()

# 堆叠布局工具函数

def create_overlay_layout(
    background: ft.Control,
    overlay: ft.Control,
    overlay_alignment: StackAlignment = StackAlignment.CENTER,
    width: Optional[float] = None,
    height: Optional[float] = None
) -> StackLayout:
    """创建覆盖层布局"""
    background_item = StackItem(
        content=background,
        config=StackItemConfig(z_index=0)
    )
    
    overlay_item = StackItem(
        content=overlay,
        config=StackItemConfig(
            z_index=1,
            alignment=overlay_alignment
        )
    )
    
    return StackLayout(
        children=[background_item, overlay_item],
        width=width,
        height=height
    )

def create_floating_action_layout(
    main_content: ft.Control,
    floating_button: ft.Control,
    button_position: StackAlignment = StackAlignment.BOTTOM_RIGHT,
    button_margin: float = 16
) -> StackLayout:
    """创建浮动按钮布局"""
    main_item = StackItem(
        content=main_content,
        config=StackItemConfig(z_index=0)
    )
    
    # 根据位置设置边距
    button_config = StackItemConfig(z_index=1, alignment=button_position)
    
    if "right" in button_position.value:
        button_config.right = button_margin
    if "left" in button_position.value:
        button_config.left = button_margin
    if "top" in button_position.value:
        button_config.top = button_margin
    if "bottom" in button_position.value:
        button_config.bottom = button_margin
    
    button_item = StackItem(
        content=floating_button,
        config=button_config
    )
    
    return StackLayout(
        children=[main_item, button_item],
        config=StackConfig(fit=False)
    )

def create_modal_layout(
    background: ft.Control,
    modal: ft.Control,
    backdrop_color: str = "rgba(0,0,0,0.5)",
    modal_alignment: StackAlignment = StackAlignment.CENTER
) -> StackLayout:
    """创建模态框布局"""
    # 背景层
    background_item = StackItem(
        content=background,
        config=StackItemConfig(z_index=0)
    )
    
    # 遮罩层
    backdrop = ft.Container(
        bgcolor=backdrop_color,
        expand=True
    )
    backdrop_item = StackItem(
        content=backdrop,
        config=StackItemConfig(z_index=1)
    )
    
    # 模态框
    modal_item = StackItem(
        content=modal,
        config=StackItemConfig(
            z_index=2,
            alignment=modal_alignment
        )
    )
    
    return StackLayout(
        children=[background_item, backdrop_item, modal_item],
        config=StackConfig(fit=False)
    )

def create_badge_layout(
    main_content: ft.Control,
    badge: ft.Control,
    badge_position: StackAlignment = StackAlignment.TOP_RIGHT,
    badge_offset_x: float = -8,
    badge_offset_y: float = 8
) -> StackLayout:
    """创建徽章布局"""
    main_item = StackItem(
        content=main_content,
        config=StackItemConfig(z_index=0)
    )
    
    # 根据位置设置徽章偏移
    badge_config = StackItemConfig(z_index=1, alignment=badge_position)
    
    if "right" in badge_position.value:
        badge_config.right = badge_offset_x
    if "left" in badge_position.value:
        badge_config.left = badge_offset_x
    if "top" in badge_position.value:
        badge_config.top = badge_offset_y
    if "bottom" in badge_position.value:
        badge_config.bottom = badge_offset_y
    
    badge_item = StackItem(
        content=badge,
        config=badge_config
    )
    
    return StackLayout(
        children=[main_item, badge_item]
    )

# 导出
__all__ = [
    'StackDirection',
    'StackAlignment',
    'StackConfig',
    'StackItemConfig',
    'StackItem',
    'StackLayout',
    'create_overlay_layout',
    'create_floating_action_layout',
    'create_modal_layout',
    'create_badge_layout',
]