"""国产大模型适配器实现

提供智谱AI、百度文心、阿里通义等国产AI模型的适配器实现。
支持统一的调用接口和参数映射，降低成本和依赖。
"""

import asyncio
import logging
from typing import Dict, List, Optional, AsyncGenerator, Any
from decimal import Decimal
import json
import aiohttp
from abc import ABC, abstractmethod

from .base_adapter import (
    BaseAIAdapter,
    AIModelType,
    AITaskType,
    AIMessage,
    AIRequest,
    AIResponse,
    AIResponseStatus,
    AIUsageStats,
    AIServiceError,
    AIRateLimitError,
    AIQuotaExceededError,
    AIModelError,
    AINetworkError
)

logger = logging.getLogger(__name__)


class BaseDomesticAdapter(BaseAIAdapter, ABC):
    """国产大模型适配器基类"""
    
    def __init__(self, api_key: str, base_url: str, **kwargs):
        super().__init__(**kwargs)
        self.api_key = api_key
        self.base_url = base_url
        self.session = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=60)
            )
        return self.session
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session and not self.session.closed:
            await self.session.close()
    
    def _handle_http_error(self, status_code: int, response_text: str) -> AIServiceError:
        """处理HTTP错误"""
        if status_code == 429:
            return AIRateLimitError(f"API限流: {response_text}")
        elif status_code == 402 or "quota" in response_text.lower():
            return AIQuotaExceededError(f"配额不足: {response_text}")
        elif status_code == 400:
            return AIModelError(f"请求参数错误: {response_text}")
        elif status_code >= 500:
            return AINetworkError(f"服务器错误: {response_text}")
        else:
            return AIServiceError(f"API错误 ({status_code}): {response_text}")


class ZhipuAIAdapter(BaseDomesticAdapter):
    """智谱AI适配器
    
    支持GLM-4、GLM-3-turbo等模型的API调用。
    """
    
    MODEL_CONFIGS = {
        AIModelType.GLM_4: {
            "name": "glm-4",
            "max_tokens": 8192,
            "context_window": 128000,
            "cost_per_1k_input": Decimal("0.01"),
            "cost_per_1k_output": Decimal("0.03")
        },
        AIModelType.GLM_3_TURBO: {
            "name": "glm-3-turbo",
            "max_tokens": 4096,
            "context_window": 32000,
            "cost_per_1k_input": Decimal("0.005"),
            "cost_per_1k_output": Decimal("0.015")
        }
    }
    
    def __init__(self, api_key: str, **kwargs):
        super().__init__(
            api_key=api_key,
            base_url="https://open.bigmodel.cn/api/paas/v4/chat/completions",
            **kwargs
        )
        logger.info(f"智谱AI适配器初始化完成，支持模型: {list(self.MODEL_CONFIGS.keys())}")
    
    @property
    def supported_models(self) -> List[AIModelType]:
        return list(self.MODEL_CONFIGS.keys())
    
    @property
    def supported_tasks(self) -> List[AITaskType]:
        return [
            AITaskType.TEXT_GENERATION,
            AITaskType.CREATIVE_WRITING,
            AITaskType.DIALOGUE_GENERATION,
            AITaskType.CONTENT_OPTIMIZATION,
            AITaskType.CONTENT_EXPANSION,
            AITaskType.CONTENT_SUMMARIZATION,
            AITaskType.TRANSLATION,
            AITaskType.QUESTION_ANSWERING,
            AITaskType.CODE_GENERATION,
            AITaskType.ANALYSIS
        ]
    
    def _get_model_config(self, model_type: AIModelType) -> Dict[str, Any]:
        if model_type not in self.MODEL_CONFIGS:
            raise AIModelError(f"不支持的模型类型: {model_type}")
        return self.MODEL_CONFIGS[model_type]
    
    def _convert_messages(self, messages: List[AIMessage]) -> List[Dict[str, str]]:
        """转换消息格式"""
        converted = []
        for msg in messages:
            converted.append({
                "role": msg.role,
                "content": msg.content
            })
        return converted
    
    async def _make_request(self, request: AIRequest) -> AIResponse:
        """发送请求到智谱AI"""
        try:
            model_config = self._get_model_config(request.model_type)
            session = await self._get_session()
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": model_config["name"],
                "messages": self._convert_messages(request.messages),
                "max_tokens": min(request.max_tokens or model_config["max_tokens"], 
                                model_config["max_tokens"]),
                "temperature": request.temperature,
                "top_p": request.top_p,
                "stream": request.stream
            }
            
            if request.stop_sequences:
                payload["stop"] = request.stop_sequences
            
            logger.debug(f"发送智谱AI请求: {payload['model']}")
            
            async with session.post(self.base_url, headers=headers, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise self._handle_http_error(response.status, error_text)
                
                if request.stream:
                    return await self._handle_stream_response(response, model_config)
                else:
                    response_data = await response.json()
                    return self._parse_response(response_data, model_config)
                    
        except Exception as e:
            if isinstance(e, AIServiceError):
                raise
            logger.error(f"智谱AI请求失败: {e}")
            raise AIServiceError(f"智谱AI服务错误: {e}")
    
    async def _handle_stream_response(self, response, model_config: Dict[str, Any]) -> AIResponse:
        """处理流式响应"""
        content_parts = []
        usage_stats = AIUsageStats()
        
        async for line in response.content:
            line = line.decode('utf-8').strip()
            if line.startswith('data: '):
                data = line[6:]
                if data == '[DONE]':
                    break
                try:
                    chunk = json.loads(data)
                    if 'choices' in chunk and len(chunk['choices']) > 0:
                        delta = chunk['choices'][0].get('delta', {})
                        if 'content' in delta:
                            content_parts.append(delta['content'])
                            yield delta['content']
                    
                    if 'usage' in chunk:
                        usage = chunk['usage']
                        usage_stats.input_tokens = usage.get('prompt_tokens', 0)
                        usage_stats.output_tokens = usage.get('completion_tokens', 0)
                        usage_stats.total_tokens = usage.get('total_tokens', 0)
                except json.JSONDecodeError:
                    continue
        
        full_content = "".join(content_parts)
        
        # 计算成本
        if usage_stats.input_tokens > 0 and usage_stats.output_tokens > 0:
            input_cost = (Decimal(usage_stats.input_tokens) / 1000) * model_config["cost_per_1k_input"]
            output_cost = (Decimal(usage_stats.output_tokens) / 1000) * model_config["cost_per_1k_output"]
            usage_stats.total_cost = input_cost + output_cost
        
        return AIResponse(
            content=full_content,
            status=AIResponseStatus.SUCCESS,
            model_type=request.model_type,
            usage_stats=usage_stats,
            metadata={"model": model_config["name"], "stream": True}
        )
    
    def _parse_response(self, response_data: Dict[str, Any], model_config: Dict[str, Any]) -> AIResponse:
        """解析响应"""
        try:
            content = response_data['choices'][0]['message']['content']
            
            usage_stats = AIUsageStats()
            if 'usage' in response_data:
                usage = response_data['usage']
                usage_stats.input_tokens = usage.get('prompt_tokens', 0)
                usage_stats.output_tokens = usage.get('completion_tokens', 0)
                usage_stats.total_tokens = usage.get('total_tokens', 0)
                
                # 计算成本
                input_cost = (Decimal(usage_stats.input_tokens) / 1000) * model_config["cost_per_1k_input"]
                output_cost = (Decimal(usage_stats.output_tokens) / 1000) * model_config["cost_per_1k_output"]
                usage_stats.total_cost = input_cost + output_cost
            
            return AIResponse(
                content=content,
                status=AIResponseStatus.SUCCESS,
                model_type=request.model_type,
                usage_stats=usage_stats,
                metadata={
                    "model": model_config["name"],
                    "finish_reason": response_data['choices'][0].get('finish_reason')
                }
            )
            
        except Exception as e:
            logger.error(f"解析智谱AI响应失败: {e}")
            raise AIServiceError(f"响应解析错误: {e}")
    
    async def generate_stream(self, request: AIRequest) -> AsyncGenerator[str, None]:
        """生成流式文本"""
        request.stream = True
        response = await self._make_request(request)
        # 在_make_request中已经yield了内容
        return
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            test_request = AIRequest(
                messages=[AIMessage(role="user", content="Hello")],
                model_type=AIModelType.GLM_3_TURBO,
                max_tokens=5,
                temperature=0.1
            )
            response = await self.generate(test_request)
            return response.status == AIResponseStatus.SUCCESS
        except Exception as e:
            logger.warning(f"智谱AI健康检查失败: {e}")
            return False
    
    def get_model_info(self, model_type: AIModelType) -> Dict[str, Any]:
        """获取模型信息"""
        if model_type not in self.MODEL_CONFIGS:
            raise AIModelError(f"不支持的模型类型: {model_type}")
        
        config = self.MODEL_CONFIGS[model_type]
        return {
            "name": config["name"],
            "max_tokens": config["max_tokens"],
            "context_window": config["context_window"],
            "cost_per_1k_input": float(config["cost_per_1k_input"]),
            "cost_per_1k_output": float(config["cost_per_1k_output"]),
            "provider": "智谱AI",
            "type": "chat",
            "strengths": ["中文理解", "代码生成", "逻辑推理", "创意写作"]
        }


class WenxinAdapter(BaseDomesticAdapter):
    """百度文心一言适配器
    
    支持ERNIE-4.0、ERNIE-3.5等模型的API调用。
    """
    
    MODEL_CONFIGS = {
        AIModelType.ERNIE_4: {
            "name": "completions_pro",
            "max_tokens": 8192,
            "context_window": 128000,
            "cost_per_1k_input": Decimal("0.012"),
            "cost_per_1k_output": Decimal("0.036")
        },
        AIModelType.ERNIE_3_5: {
            "name": "completions",
            "max_tokens": 4096,
            "context_window": 32000,
            "cost_per_1k_input": Decimal("0.008"),
            "cost_per_1k_output": Decimal("0.024")
        }
    }
    
    def __init__(self, api_key: str, secret_key: str, **kwargs):
        super().__init__(
            api_key=api_key,
            base_url="https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat",
            **kwargs
        )
        self.secret_key = secret_key
        self.access_token = None
        logger.info(f"百度文心适配器初始化完成，支持模型: {list(self.MODEL_CONFIGS.keys())}")
    
    @property
    def supported_models(self) -> List[AIModelType]:
        return list(self.MODEL_CONFIGS.keys())
    
    @property
    def supported_tasks(self) -> List[AITaskType]:
        return [
            AITaskType.TEXT_GENERATION,
            AITaskType.CREATIVE_WRITING,
            AITaskType.DIALOGUE_GENERATION,
            AITaskType.CONTENT_OPTIMIZATION,
            AITaskType.CONTENT_SUMMARIZATION,
            AITaskType.TRANSLATION,
            AITaskType.QUESTION_ANSWERING,
            AITaskType.ANALYSIS
        ]
    
    async def _get_access_token(self) -> str:
        """获取访问令牌"""
        if self.access_token:
            return self.access_token
        
        session = await self._get_session()
        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {
            "grant_type": "client_credentials",
            "client_id": self.api_key,
            "client_secret": self.secret_key
        }
        
        async with session.post(url, params=params) as response:
            if response.status != 200:
                error_text = await response.text()
                raise AIServiceError(f"获取百度访问令牌失败: {error_text}")
            
            data = await response.json()
            self.access_token = data["access_token"]
            return self.access_token
    
    def _get_model_config(self, model_type: AIModelType) -> Dict[str, Any]:
        if model_type not in self.MODEL_CONFIGS:
            raise AIModelError(f"不支持的模型类型: {model_type}")
        return self.MODEL_CONFIGS[model_type]
    
    def _convert_messages(self, messages: List[AIMessage]) -> List[Dict[str, str]]:
        """转换消息格式"""
        converted = []
        for msg in messages:
            converted.append({
                "role": msg.role,
                "content": msg.content
            })
        return converted
    
    async def _make_request(self, request: AIRequest) -> AIResponse:
        """发送请求到百度文心"""
        try:
            model_config = self._get_model_config(request.model_type)
            access_token = await self._get_access_token()
            session = await self._get_session()
            
            url = f"{self.base_url}/{model_config['name']}"
            params = {"access_token": access_token}
            
            payload = {
                "messages": self._convert_messages(request.messages),
                "max_output_tokens": min(request.max_tokens or model_config["max_tokens"], 
                                       model_config["max_tokens"]),
                "temperature": request.temperature,
                "top_p": request.top_p,
                "stream": request.stream
            }
            
            logger.debug(f"发送百度文心请求: {model_config['name']}")
            
            async with session.post(url, params=params, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise self._handle_http_error(response.status, error_text)
                
                if request.stream:
                    return await self._handle_stream_response(response, model_config)
                else:
                    response_data = await response.json()
                    return self._parse_response(response_data, model_config)
                    
        except Exception as e:
            if isinstance(e, AIServiceError):
                raise
            logger.error(f"百度文心请求失败: {e}")
            raise AIServiceError(f"百度文心服务错误: {e}")
    
    async def _handle_stream_response(self, response, model_config: Dict[str, Any]) -> AIResponse:
        """处理流式响应"""
        content_parts = []
        usage_stats = AIUsageStats()
        
        async for line in response.content:
            line = line.decode('utf-8').strip()
            if line.startswith('data: '):
                data = line[6:]
                if data == '[DONE]':
                    break
                try:
                    chunk = json.loads(data)
                    if 'result' in chunk:
                        content_parts.append(chunk['result'])
                        yield chunk['result']
                    
                    if 'usage' in chunk:
                        usage = chunk['usage']
                        usage_stats.input_tokens = usage.get('prompt_tokens', 0)
                        usage_stats.output_tokens = usage.get('completion_tokens', 0)
                        usage_stats.total_tokens = usage.get('total_tokens', 0)
                except json.JSONDecodeError:
                    continue
        
        full_content = "".join(content_parts)
        
        # 计算成本
        if usage_stats.input_tokens > 0 and usage_stats.output_tokens > 0:
            input_cost = (Decimal(usage_stats.input_tokens) / 1000) * model_config["cost_per_1k_input"]
            output_cost = (Decimal(usage_stats.output_tokens) / 1000) * model_config["cost_per_1k_output"]
            usage_stats.total_cost = input_cost + output_cost
        
        return AIResponse(
            content=full_content,
            status=AIResponseStatus.SUCCESS,
            model_type=request.model_type,
            usage_stats=usage_stats,
            metadata={"model": model_config["name"], "stream": True}
        )
    
    def _parse_response(self, response_data: Dict[str, Any], model_config: Dict[str, Any]) -> AIResponse:
        """解析响应"""
        try:
            content = response_data['result']
            
            usage_stats = AIUsageStats()
            if 'usage' in response_data:
                usage = response_data['usage']
                usage_stats.input_tokens = usage.get('prompt_tokens', 0)
                usage_stats.output_tokens = usage.get('completion_tokens', 0)
                usage_stats.total_tokens = usage.get('total_tokens', 0)
                
                # 计算成本
                input_cost = (Decimal(usage_stats.input_tokens) / 1000) * model_config["cost_per_1k_input"]
                output_cost = (Decimal(usage_stats.output_tokens) / 1000) * model_config["cost_per_1k_output"]
                usage_stats.total_cost = input_cost + output_cost
            
            return AIResponse(
                content=content,
                status=AIResponseStatus.SUCCESS,
                model_type=request.model_type,
                usage_stats=usage_stats,
                metadata={
                    "model": model_config["name"],
                    "finish_reason": response_data.get('finish_reason')
                }
            )
            
        except Exception as e:
            logger.error(f"解析百度文心响应失败: {e}")
            raise AIServiceError(f"响应解析错误: {e}")
    
    async def generate_stream(self, request: AIRequest) -> AsyncGenerator[str, None]:
        """生成流式文本"""
        request.stream = True
        response = await self._make_request(request)
        return
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            test_request = AIRequest(
                messages=[AIMessage(role="user", content="Hello")],
                model_type=AIModelType.ERNIE_3_5,
                max_tokens=5,
                temperature=0.1
            )
            response = await self.generate(test_request)
            return response.status == AIResponseStatus.SUCCESS
        except Exception as e:
            logger.warning(f"百度文心健康检查失败: {e}")
            return False
    
    def get_model_info(self, model_type: AIModelType) -> Dict[str, Any]:
        """获取模型信息"""
        if model_type not in self.MODEL_CONFIGS:
            raise AIModelError(f"不支持的模型类型: {model_type}")
        
        config = self.MODEL_CONFIGS[model_type]
        return {
            "name": config["name"],
            "max_tokens": config["max_tokens"],
            "context_window": config["context_window"],
            "cost_per_1k_input": float(config["cost_per_1k_input"]),
            "cost_per_1k_output": float(config["cost_per_1k_output"]),
            "provider": "百度文心",
            "type": "chat",
            "strengths": ["中文理解", "知识问答", "文本生成", "内容创作"]
        }


class TongyiAdapter(BaseDomesticAdapter):
    """阿里通义千问适配器
    
    支持Qwen-Max、Qwen-Plus等模型的API调用。
    """
    
    MODEL_CONFIGS = {
        AIModelType.QWEN_MAX: {
            "name": "qwen-max",
            "max_tokens": 8192,
            "context_window": 128000,
            "cost_per_1k_input": Decimal("0.02"),
            "cost_per_1k_output": Decimal("0.06")
        },
        AIModelType.QWEN_PLUS: {
            "name": "qwen-plus",
            "max_tokens": 4096,
            "context_window": 32000,
            "cost_per_1k_input": Decimal("0.008"),
            "cost_per_1k_output": Decimal("0.024")
        }
    }
    
    def __init__(self, api_key: str, **kwargs):
        super().__init__(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
            **kwargs
        )
        logger.info(f"阿里通义适配器初始化完成，支持模型: {list(self.MODEL_CONFIGS.keys())}")
    
    @property
    def supported_models(self) -> List[AIModelType]:
        return list(self.MODEL_CONFIGS.keys())
    
    @property
    def supported_tasks(self) -> List[AITaskType]:
        return [
            AITaskType.TEXT_GENERATION,
            AITaskType.CREATIVE_WRITING,
            AITaskType.DIALOGUE_GENERATION,
            AITaskType.CONTENT_OPTIMIZATION,
            AITaskType.CONTENT_SUMMARIZATION,
            AITaskType.TRANSLATION,
            AITaskType.QUESTION_ANSWERING,
            AITaskType.CODE_GENERATION,
            AITaskType.ANALYSIS
        ]
    
    def _get_model_config(self, model_type: AIModelType) -> Dict[str, Any]:
        if model_type not in self.MODEL_CONFIGS:
            raise AIModelError(f"不支持的模型类型: {model_type}")
        return self.MODEL_CONFIGS[model_type]
    
    def _convert_messages(self, messages: List[AIMessage]) -> List[Dict[str, str]]:
        """转换消息格式"""
        converted = []
        for msg in messages:
            converted.append({
                "role": msg.role,
                "content": msg.content
            })
        return converted
    
    async def _make_request(self, request: AIRequest) -> AIResponse:
        """发送请求到阿里通义"""
        try:
            model_config = self._get_model_config(request.model_type)
            session = await self._get_session()
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": model_config["name"],
                "input": {
                    "messages": self._convert_messages(request.messages)
                },
                "parameters": {
                    "max_tokens": min(request.max_tokens or model_config["max_tokens"], 
                                    model_config["max_tokens"]),
                    "temperature": request.temperature,
                    "top_p": request.top_p,
                    "incremental_output": request.stream
                }
            }
            
            if request.stop_sequences:
                payload["parameters"]["stop"] = request.stop_sequences
            
            logger.debug(f"发送阿里通义请求: {model_config['name']}")
            
            async with session.post(self.base_url, headers=headers, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise self._handle_http_error(response.status, error_text)
                
                if request.stream:
                    return await self._handle_stream_response(response, model_config)
                else:
                    response_data = await response.json()
                    return self._parse_response(response_data, model_config)
                    
        except Exception as e:
            if isinstance(e, AIServiceError):
                raise
            logger.error(f"阿里通义请求失败: {e}")
            raise AIServiceError(f"阿里通义服务错误: {e}")
    
    async def _handle_stream_response(self, response, model_config: Dict[str, Any]) -> AIResponse:
        """处理流式响应"""
        content_parts = []
        usage_stats = AIUsageStats()
        
        async for line in response.content:
            line = line.decode('utf-8').strip()
            if line.startswith('data:'):
                data = line[5:].strip()
                if not data or data == '[DONE]':
                    continue
                try:
                    chunk = json.loads(data)
                    if 'output' in chunk and 'text' in chunk['output']:
                        content_parts.append(chunk['output']['text'])
                        yield chunk['output']['text']
                    
                    if 'usage' in chunk:
                        usage = chunk['usage']
                        usage_stats.input_tokens = usage.get('input_tokens', 0)
                        usage_stats.output_tokens = usage.get('output_tokens', 0)
                        usage_stats.total_tokens = usage.get('total_tokens', 0)
                except json.JSONDecodeError:
                    continue
        
        full_content = "".join(content_parts)
        
        # 计算成本
        if usage_stats.input_tokens > 0 and usage_stats.output_tokens > 0:
            input_cost = (Decimal(usage_stats.input_tokens) / 1000) * model_config["cost_per_1k_input"]
            output_cost = (Decimal(usage_stats.output_tokens) / 1000) * model_config["cost_per_1k_output"]
            usage_stats.total_cost = input_cost + output_cost
        
        return AIResponse(
            content=full_content,
            status=AIResponseStatus.SUCCESS,
            model_type=request.model_type,
            usage_stats=usage_stats,
            metadata={"model": model_config["name"], "stream": True}
        )
    
    def _parse_response(self, response_data: Dict[str, Any], model_config: Dict[str, Any]) -> AIResponse:
        """解析响应"""
        try:
            content = response_data['output']['text']
            
            usage_stats = AIUsageStats()
            if 'usage' in response_data:
                usage = response_data['usage']
                usage_stats.input_tokens = usage.get('input_tokens', 0)
                usage_stats.output_tokens = usage.get('output_tokens', 0)
                usage_stats.total_tokens = usage.get('total_tokens', 0)
                
                # 计算成本
                input_cost = (Decimal(usage_stats.input_tokens) / 1000) * model_config["cost_per_1k_input"]
                output_cost = (Decimal(usage_stats.output_tokens) / 1000) * model_config["cost_per_1k_output"]
                usage_stats.total_cost = input_cost + output_cost
            
            return AIResponse(
                content=content,
                status=AIResponseStatus.SUCCESS,
                model_type=request.model_type,
                usage_stats=usage_stats,
                metadata={
                    "model": model_config["name"],
                    "finish_reason": response_data['output'].get('finish_reason')
                }
            )
            
        except Exception as e:
            logger.error(f"解析阿里通义响应失败: {e}")
            raise AIServiceError(f"响应解析错误: {e}")
    
    async def generate_stream(self, request: AIRequest) -> AsyncGenerator[str, None]:
        """生成流式文本"""
        request.stream = True
        response = await self._make_request(request)
        return
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            test_request = AIRequest(
                messages=[AIMessage(role="user", content="Hello")],
                model_type=AIModelType.QWEN_PLUS,
                max_tokens=5,
                temperature=0.1
            )
            response = await self.generate(test_request)
            return response.status == AIResponseStatus.SUCCESS
        except Exception as e:
            logger.warning(f"阿里通义健康检查失败: {e}")
            return False
    
    def get_model_info(self, model_type: AIModelType) -> Dict[str, Any]:
        """获取模型信息"""
        if model_type not in self.MODEL_CONFIGS:
            raise AIModelError(f"不支持的模型类型: {model_type}")
        
        config = self.MODEL_CONFIGS[model_type]
        return {
            "name": config["name"],
            "max_tokens": config["max_tokens"],
            "context_window": config["context_window"],
            "cost_per_1k_input": float(config["cost_per_1k_input"]),
            "cost_per_1k_output": float(config["cost_per_1k_output"]),
            "provider": "阿里通义",
            "type": "chat",
            "strengths": ["中文理解", "多轮对话", "代码生成", "知识问答"]
        }


# 创建适配器实例的便捷函数
def create_zhipu_adapter(api_key: str, **kwargs) -> ZhipuAIAdapter:
    """创建智谱AI适配器实例"""
    return ZhipuAIAdapter(api_key=api_key, **kwargs)


def create_wenxin_adapter(api_key: str, secret_key: str, **kwargs) -> WenxinAdapter:
    """创建百度文心适配器实例"""
    return WenxinAdapter(api_key=api_key, secret_key=secret_key, **kwargs)


def create_tongyi_adapter(api_key: str, **kwargs) -> TongyiAdapter:
    """创建阿里通义适配器实例"""
    return TongyiAdapter(api_key=api_key, **kwargs)