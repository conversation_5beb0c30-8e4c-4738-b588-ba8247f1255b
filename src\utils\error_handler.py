"""错误处理模块

提供全局异常处理、用户友好错误提示和错误报告功能。
"""

import sys
import traceback
import functools
import asyncio
from typing import Dict, List, Any, Optional, Callable, Union, Type
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import logging
import json
from pathlib import Path

# 导入日志模块
from .logger import get_logger

logger = get_logger(__name__)


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """错误类别"""
    SYSTEM = "system"
    NETWORK = "network"
    DATABASE = "database"
    AI_SERVICE = "ai_service"
    FILE_IO = "file_io"
    USER_INPUT = "user_input"
    AUTHENTICATION = "authentication"
    PERMISSION = "permission"
    VALIDATION = "validation"
    BUSINESS_LOGIC = "business_logic"
    UNKNOWN = "unknown"


@dataclass
class ErrorContext:
    """错误上下文信息"""
    timestamp: datetime = field(default_factory=datetime.now)
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    url: Optional[str] = None
    method: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'user_id': self.user_id,
            'session_id': self.session_id,
            'request_id': self.request_id,
            'user_agent': self.user_agent,
            'ip_address': self.ip_address,
            'url': self.url,
            'method': self.method,
            'additional_data': self.additional_data
        }


@dataclass
class ErrorReport:
    """错误报告"""
    error_id: str
    error_type: str
    message: str
    severity: ErrorSeverity
    category: ErrorCategory
    context: ErrorContext
    stack_trace: Optional[str] = None
    user_message: Optional[str] = None
    suggested_actions: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'error_id': self.error_id,
            'error_type': self.error_type,
            'message': self.message,
            'severity': self.severity.value,
            'category': self.category.value,
            'context': self.context.to_dict(),
            'stack_trace': self.stack_trace,
            'user_message': self.user_message,
            'suggested_actions': self.suggested_actions,
            'metadata': self.metadata
        }


class BambooFallError(Exception):
    """应用基础异常类"""
    
    def __init__(self, message: str, category: ErrorCategory = ErrorCategory.UNKNOWN,
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 user_message: Optional[str] = None,
                 suggested_actions: Optional[List[str]] = None,
                 metadata: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.user_message = user_message or self._get_default_user_message()
        self.suggested_actions = suggested_actions or []
        self.metadata = metadata or {}
    
    def _get_default_user_message(self) -> str:
        """获取默认用户消息"""
        return "操作失败，请稍后重试。如果问题持续存在，请联系技术支持。"


class ValidationError(BambooFallError):
    """验证错误"""
    
    def __init__(self, message: str, field: Optional[str] = None, 
                 value: Optional[Any] = None, **kwargs):
        super().__init__(message, category=ErrorCategory.VALIDATION, 
                        severity=ErrorSeverity.LOW, **kwargs)
        self.field = field
        self.value = value
        self.metadata.update({'field': field, 'value': str(value) if value is not None else None})
    
    def _get_default_user_message(self) -> str:
        if self.field:
            return f"输入的{self.field}不符合要求，请检查后重新输入。"
        return "输入数据不符合要求，请检查后重新输入。"


class NetworkError(BambooFallError):
    """网络错误"""
    
    def __init__(self, message: str, status_code: Optional[int] = None,
                 url: Optional[str] = None, **kwargs):
        super().__init__(message, category=ErrorCategory.NETWORK,
                        severity=ErrorSeverity.MEDIUM, **kwargs)
        self.status_code = status_code
        self.url = url
        self.metadata.update({'status_code': status_code, 'url': url})
    
    def _get_default_user_message(self) -> str:
        return "网络连接异常，请检查网络设置后重试。"


class AIServiceError(BambooFallError):
    """AI服务错误"""
    
    def __init__(self, message: str, service_name: Optional[str] = None,
                 model_name: Optional[str] = None, **kwargs):
        super().__init__(message, category=ErrorCategory.AI_SERVICE,
                        severity=ErrorSeverity.HIGH, **kwargs)
        self.service_name = service_name
        self.model_name = model_name
        self.metadata.update({'service_name': service_name, 'model_name': model_name})
    
    def _get_default_user_message(self) -> str:
        return "AI服务暂时不可用，请稍后重试。"


class DatabaseError(BambooFallError):
    """数据库错误"""
    
    def __init__(self, message: str, operation: Optional[str] = None,
                 table: Optional[str] = None, **kwargs):
        super().__init__(message, category=ErrorCategory.DATABASE,
                        severity=ErrorSeverity.HIGH, **kwargs)
        self.operation = operation
        self.table = table
        self.metadata.update({'operation': operation, 'table': table})
    
    def _get_default_user_message(self) -> str:
        return "数据保存失败，请稍后重试。"


class FileIOError(BambooFallError):
    """文件IO错误"""
    
    def __init__(self, message: str, file_path: Optional[str] = None,
                 operation: Optional[str] = None, **kwargs):
        super().__init__(message, category=ErrorCategory.FILE_IO,
                        severity=ErrorSeverity.MEDIUM, **kwargs)
        self.file_path = file_path
        self.operation = operation
        self.metadata.update({'file_path': file_path, 'operation': operation})
    
    def _get_default_user_message(self) -> str:
        if self.operation == 'read':
            return "文件读取失败，请检查文件是否存在或权限是否正确。"
        elif self.operation == 'write':
            return "文件保存失败，请检查磁盘空间或权限设置。"
        return "文件操作失败，请检查文件路径和权限设置。"


class AuthenticationError(BambooFallError):
    """认证错误"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.AUTHENTICATION,
                        severity=ErrorSeverity.MEDIUM, **kwargs)
    
    def _get_default_user_message(self) -> str:
        return "身份验证失败，请重新登录。"


class PermissionError(BambooFallError):
    """权限错误"""
    
    def __init__(self, message: str, resource: Optional[str] = None,
                 action: Optional[str] = None, **kwargs):
        super().__init__(message, category=ErrorCategory.PERMISSION,
                        severity=ErrorSeverity.MEDIUM, **kwargs)
        self.resource = resource
        self.action = action
        self.metadata.update({'resource': resource, 'action': action})
    
    def _get_default_user_message(self) -> str:
        return "您没有执行此操作的权限，请联系管理员。"


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.error_callbacks: Dict[Type[Exception], List[Callable]] = {}
        self.global_callbacks: List[Callable] = []
        self.error_reports: List[ErrorReport] = []
        self.max_reports = 1000
        
        # 错误消息映射
        self.error_messages = {
            FileNotFoundError: "文件未找到",
            PermissionError: "权限不足",
            ConnectionError: "连接失败",
            TimeoutError: "操作超时",
            ValueError: "数据格式错误",
            KeyError: "缺少必要参数",
            TypeError: "数据类型错误",
            AttributeError: "属性访问错误",
            ImportError: "模块导入错误",
            MemoryError: "内存不足",
            OSError: "系统操作错误"
        }
    
    def register_callback(self, exception_type: Type[Exception], 
                         callback: Callable[[Exception, ErrorContext], None]):
        """注册错误回调"""
        if exception_type not in self.error_callbacks:
            self.error_callbacks[exception_type] = []
        self.error_callbacks[exception_type].append(callback)
    
    def register_global_callback(self, callback: Callable[[Exception, ErrorContext], None]):
        """注册全局错误回调"""
        self.global_callbacks.append(callback)
    
    def handle_error(self, error: Exception, context: Optional[ErrorContext] = None) -> ErrorReport:
        """处理错误"""
        if context is None:
            context = ErrorContext()
        
        # 生成错误ID
        error_id = self._generate_error_id(error, context)
        
        # 确定错误类别和严重程度
        if isinstance(error, BambooFallError):
            category = error.category
            severity = error.severity
            user_message = error.user_message
            suggested_actions = error.suggested_actions
            metadata = error.metadata
        else:
            category = self._categorize_error(error)
            severity = self._assess_severity(error)
            user_message = self._get_user_friendly_message(error)
            suggested_actions = self._get_suggested_actions(error)
            metadata = {}
        
        # 创建错误报告
        error_report = ErrorReport(
            error_id=error_id,
            error_type=type(error).__name__,
            message=str(error),
            severity=severity,
            category=category,
            context=context,
            stack_trace=traceback.format_exc(),
            user_message=user_message,
            suggested_actions=suggested_actions,
            metadata=metadata
        )
        
        # 记录错误报告
        self._record_error_report(error_report)
        
        # 调用回调函数
        self._call_callbacks(error, context)
        
        # 记录日志
        self._log_error(error_report)
        
        return error_report
    
    def _generate_error_id(self, error: Exception, context: ErrorContext) -> str:
        """生成错误ID"""
        import hashlib
        content = f"{type(error).__name__}_{str(error)}_{context.timestamp.isoformat()}"
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    def _categorize_error(self, error: Exception) -> ErrorCategory:
        """分类错误"""
        error_type = type(error)
        
        if issubclass(error_type, (ConnectionError, TimeoutError)):
            return ErrorCategory.NETWORK
        elif issubclass(error_type, (FileNotFoundError, IOError, OSError)):
            return ErrorCategory.FILE_IO
        elif issubclass(error_type, (ValueError, TypeError, KeyError)):
            return ErrorCategory.VALIDATION
        elif issubclass(error_type, PermissionError):
            return ErrorCategory.PERMISSION
        elif issubclass(error_type, (ImportError, AttributeError)):
            return ErrorCategory.SYSTEM
        else:
            return ErrorCategory.UNKNOWN
    
    def _assess_severity(self, error: Exception) -> ErrorSeverity:
        """评估错误严重程度"""
        error_type = type(error)
        
        if issubclass(error_type, (MemoryError, SystemError)):
            return ErrorSeverity.CRITICAL
        elif issubclass(error_type, (ConnectionError, IOError)):
            return ErrorSeverity.HIGH
        elif issubclass(error_type, (TimeoutError, PermissionError)):
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    def _get_user_friendly_message(self, error: Exception) -> str:
        """获取用户友好的错误消息"""
        error_type = type(error)
        return self.error_messages.get(error_type, "操作失败，请稍后重试。")
    
    def _get_suggested_actions(self, error: Exception) -> List[str]:
        """获取建议操作"""
        error_type = type(error)
        
        if issubclass(error_type, FileNotFoundError):
            return ["检查文件路径是否正确", "确认文件是否存在"]
        elif issubclass(error_type, PermissionError):
            return ["检查文件权限设置", "以管理员身份运行程序"]
        elif issubclass(error_type, ConnectionError):
            return ["检查网络连接", "确认服务器地址正确", "稍后重试"]
        elif issubclass(error_type, TimeoutError):
            return ["检查网络速度", "增加超时时间", "稍后重试"]
        elif issubclass(error_type, MemoryError):
            return ["关闭其他程序释放内存", "重启应用程序", "联系技术支持"]
        else:
            return ["稍后重试", "如果问题持续存在，请联系技术支持"]
    
    def _record_error_report(self, error_report: ErrorReport):
        """记录错误报告"""
        self.error_reports.append(error_report)
        
        # 限制报告数量
        if len(self.error_reports) > self.max_reports:
            self.error_reports = self.error_reports[-self.max_reports:]
    
    def _call_callbacks(self, error: Exception, context: ErrorContext):
        """调用回调函数"""
        # 调用特定类型的回调
        error_type = type(error)
        if error_type in self.error_callbacks:
            for callback in self.error_callbacks[error_type]:
                try:
                    callback(error, context)
                except Exception as e:
                    logger.error(f"Error in callback: {e}")
        
        # 调用全局回调
        for callback in self.global_callbacks:
            try:
                callback(error, context)
            except Exception as e:
                logger.error(f"Error in global callback: {e}")
    
    def _log_error(self, error_report: ErrorReport):
        """记录错误日志"""
        log_data = {
            'error_id': error_report.error_id,
            'error_type': error_report.error_type,
            'message': error_report.message,
            'severity': error_report.severity.value,
            'category': error_report.category.value,
            'user_id': error_report.context.user_id,
            'session_id': error_report.context.session_id
        }
        
        if error_report.severity == ErrorSeverity.CRITICAL:
            logger.critical(f"Critical error: {error_report.message}", extra=log_data)
        elif error_report.severity == ErrorSeverity.HIGH:
            logger.error(f"High severity error: {error_report.message}", extra=log_data)
        elif error_report.severity == ErrorSeverity.MEDIUM:
            logger.warning(f"Medium severity error: {error_report.message}", extra=log_data)
        else:
            logger.info(f"Low severity error: {error_report.message}", extra=log_data)
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        if not self.error_reports:
            return {
                'total_errors': 0,
                'by_severity': {},
                'by_category': {},
                'by_type': {},
                'recent_errors': []
            }
        
        # 按严重程度统计
        by_severity = {}
        for severity in ErrorSeverity:
            by_severity[severity.value] = sum(
                1 for report in self.error_reports 
                if report.severity == severity
            )
        
        # 按类别统计
        by_category = {}
        for category in ErrorCategory:
            by_category[category.value] = sum(
                1 for report in self.error_reports 
                if report.category == category
            )
        
        # 按类型统计
        by_type = {}
        for report in self.error_reports:
            error_type = report.error_type
            by_type[error_type] = by_type.get(error_type, 0) + 1
        
        # 最近的错误
        recent_errors = [
            {
                'error_id': report.error_id,
                'error_type': report.error_type,
                'message': report.message,
                'severity': report.severity.value,
                'timestamp': report.context.timestamp.isoformat()
            }
            for report in self.error_reports[-10:]
        ]
        
        return {
            'total_errors': len(self.error_reports),
            'by_severity': by_severity,
            'by_category': by_category,
            'by_type': by_type,
            'recent_errors': recent_errors
        }
    
    def export_error_reports(self, file_path: Path, format: str = 'json'):
        """导出错误报告"""
        if format.lower() == 'json':
            data = [report.to_dict() for report in self.error_reports]
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        else:
            raise ValueError(f"Unsupported format: {format}")


def error_handler_decorator(handler: Optional[ErrorHandler] = None):
    """错误处理装饰器"""
    if handler is None:
        handler = global_error_handler
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = ErrorContext(
                    additional_data={
                        'function': func.__name__,
                        'args': str(args)[:200],  # 限制长度
                        'kwargs': str(kwargs)[:200]
                    }
                )
                error_report = handler.handle_error(e, context)
                
                # 重新抛出BambooFallError，其他异常转换为BambooFallError
                if isinstance(e, BambooFallError):
                    raise
                else:
                    raise BambooFallError(
                        message=str(e),
                        category=error_report.category,
                        severity=error_report.severity,
                        user_message=error_report.user_message,
                        suggested_actions=error_report.suggested_actions
                    ) from e
        
        return wrapper
    return decorator


def async_error_handler_decorator(handler: Optional[ErrorHandler] = None):
    """异步错误处理装饰器"""
    if handler is None:
        handler = global_error_handler
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                context = ErrorContext(
                    additional_data={
                        'function': func.__name__,
                        'args': str(args)[:200],
                        'kwargs': str(kwargs)[:200]
                    }
                )
                error_report = handler.handle_error(e, context)
                
                # 重新抛出BambooFallError，其他异常转换为BambooFallError
                if isinstance(e, BambooFallError):
                    raise
                else:
                    raise BambooFallError(
                        message=str(e),
                        category=error_report.category,
                        severity=error_report.severity,
                        user_message=error_report.user_message,
                        suggested_actions=error_report.suggested_actions
                    ) from e
        
        return wrapper
    return decorator


class GlobalExceptionHandler:
    """全局异常处理器"""
    
    def __init__(self, error_handler: ErrorHandler):
        self.error_handler = error_handler
        self.original_excepthook = sys.excepthook
    
    def install(self):
        """安装全局异常处理"""
        sys.excepthook = self.handle_exception
        logger.info("全局异常处理器已安装")
    
    def uninstall(self):
        """卸载全局异常处理"""
        sys.excepthook = self.original_excepthook
        logger.info("全局异常处理器已卸载")
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """处理未捕获的异常"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许Ctrl+C正常退出
            self.original_excepthook(exc_type, exc_value, exc_traceback)
            return
        
        context = ErrorContext(
            additional_data={
                'uncaught_exception': True,
                'traceback': ''.join(traceback.format_tb(exc_traceback))
            }
        )
        
        self.error_handler.handle_error(exc_value, context)
        
        # 调用原始异常处理器
        self.original_excepthook(exc_type, exc_value, exc_traceback)


# 全局错误处理器实例
global_error_handler = ErrorHandler()
global_exception_handler = GlobalExceptionHandler(global_error_handler)


# 便捷函数
def handle_error(error: Exception, context: Optional[ErrorContext] = None) -> ErrorReport:
    """处理错误"""
    return global_error_handler.handle_error(error, context)


def get_error_statistics() -> Dict[str, Any]:
    """获取错误统计信息"""
    return global_error_handler.get_error_statistics()


def install_global_exception_handler():
    """安装全局异常处理器"""
    global_exception_handler.install()


def uninstall_global_exception_handler():
    """卸载全局异常处理器"""
    global_exception_handler.uninstall()


# 常用装饰器别名
handle_errors = error_handler_decorator
handle_async_errors = async_error_handler_decorator