"""章节数据模型

定义小说章节相关的数据模型，包括：
- 章节基本信息和内容
- 章节版本控制
- 章节排序和组织
- 章节统计信息
"""

import uuid
from datetime import datetime
from typing import Optional, List
from enum import Enum

from sqlalchemy import Column, String, Text, Integer, Float, Boolean, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from pydantic import BaseModel, Field, validator

from .base import BaseTable, BaseSchema, BaseResponseSchema, BaseCreateSchema, BaseUpdateSchema

class ChapterStatus(str, Enum):
    """章节状态枚举"""
    DRAFT = "draft"          # 草稿
    WRITING = "writing"      # 写作中
    REVIEW = "review"        # 待审阅
    COMPLETED = "completed"  # 已完成
    PUBLISHED = "published"  # 已发布
    ARCHIVED = "archived"    # 已归档

class Chapter(BaseTable):
    """章节数据模型
    
    存储小说章节的内容和元数据。
    """
    __tablename__ = "chapters"
    
    # 关联项目
    project_id = Column(
        UUID(as_uuid=True),
        ForeignKey("projects.id", ondelete="CASCADE"),
        nullable=False,
        comment="所属项目ID"
    )
    
    # 章节基本信息
    title = Column(
        String(255),
        nullable=False,
        comment="章节标题"
    )
    
    subtitle = Column(
        String(255),
        nullable=True,
        comment="章节副标题"
    )
    
    # 章节内容
    content = Column(
        Text,
        nullable=True,
        comment="章节正文内容"
    )
    
    # 章节摘要
    summary = Column(
        Text,
        nullable=True,
        comment="章节摘要"
    )
    
    # 章节排序
    order_index = Column(
        Integer,
        nullable=False,
        default=0,
        comment="章节排序索引"
    )
    
    # 章节编号（用户可见）
    chapter_number = Column(
        Integer,
        nullable=True,
        comment="章节编号"
    )
    
    # 章节状态
    status = Column(
        String(20),
        nullable=False,
        default=ChapterStatus.DRAFT.value,
        comment="章节状态"
    )
    
    # 字数统计
    word_count = Column(
        Integer,
        default=0,
        nullable=False,
        comment="字数统计"
    )
    
    # 字符数统计
    character_count = Column(
        Integer,
        default=0,
        nullable=False,
        comment="字符数统计"
    )
    
    # 段落数统计
    paragraph_count = Column(
        Integer,
        default=0,
        nullable=False,
        comment="段落数统计"
    )
    
    # 预计阅读时间（分钟）
    estimated_reading_time = Column(
        Integer,
        default=0,
        nullable=False,
        comment="预计阅读时间（分钟）"
    )
    
    # 版本信息
    version = Column(
        Integer,
        default=1,
        nullable=False,
        comment="版本号"
    )
    
    # 是否为当前版本
    is_current_version = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否为当前版本"
    )
    
    # 父版本ID（用于版本控制）
    parent_version_id = Column(
        UUID(as_uuid=True),
        ForeignKey("chapters.id"),
        nullable=True,
        comment="父版本ID"
    )
    
    # 标签和分类
    tags = Column(
        String(500),
        nullable=True,
        comment="章节标签（逗号分隔）"
    )
    
    # 写作笔记
    notes = Column(
        Text,
        nullable=True,
        comment="写作笔记"
    )
    
    # AI辅助信息
    ai_suggestions = Column(
        Text,
        nullable=True,
        comment="AI建议"
    )
    
    # 最后编辑时间
    last_edited_at = Column(
        "last_edited_at",
        nullable=True,
        comment="最后编辑时间"
    )
    
    # 发布时间
    published_at = Column(
        "published_at",
        nullable=True,
        comment="发布时间"
    )
    
    # 关联关系
    project = relationship("Project", back_populates="chapters")
    parent_version = relationship("Chapter", remote_side=["id"])
    child_versions = relationship("Chapter", back_populates="parent_version")
    
    # 索引
    __table_args__ = (
        Index('idx_chapter_project_order', 'project_id', 'order_index'),
        Index('idx_chapter_project_status', 'project_id', 'status'),
        Index('idx_chapter_current_version', 'project_id', 'is_current_version'),
    )
    
    def calculate_statistics(self):
        """计算章节统计信息"""
        if self.content:
            # 计算字数（去除空白字符）
            content_clean = ''.join(self.content.split())
            self.character_count = len(content_clean)
            self.word_count = len(content_clean)  # 中文按字符计算
            
            # 计算段落数
            self.paragraph_count = len([p for p in self.content.split('\n') if p.strip()])
            
            # 计算预计阅读时间（按每分钟300字计算）
            self.estimated_reading_time = max(1, self.word_count // 300)
        else:
            self.character_count = 0
            self.word_count = 0
            self.paragraph_count = 0
            self.estimated_reading_time = 0
    
    def create_new_version(self) -> 'Chapter':
        """创建新版本"""
        # 将当前版本标记为非当前版本
        self.is_current_version = False
        
        # 创建新版本
        new_version = Chapter(
            project_id=self.project_id,
            title=self.title,
            subtitle=self.subtitle,
            content=self.content,
            summary=self.summary,
            order_index=self.order_index,
            chapter_number=self.chapter_number,
            status=self.status,
            tags=self.tags,
            notes=self.notes,
            version=self.version + 1,
            is_current_version=True,
            parent_version_id=self.id,
            created_by=self.updated_by
        )
        
        new_version.calculate_statistics()
        return new_version
    
    def update_last_edited(self):
        """更新最后编辑时间"""
        self.last_edited_at = datetime.utcnow()
    
    def publish(self):
        """发布章节"""
        self.status = ChapterStatus.PUBLISHED.value
        self.published_at = datetime.utcnow()
    
    def get_tag_list(self) -> List[str]:
        """获取标签列表"""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        return []
    
    def set_tag_list(self, tags: List[str]):
        """设置标签列表"""
        self.tags = ','.join(tags) if tags else None
    
    def __repr__(self) -> str:
        return f"<Chapter(id={self.id}, title='{self.title}', project_id={self.project_id})>"

# Pydantic模式定义

class ChapterBaseSchema(BaseSchema):
    """章节基础模式"""
    title: str = Field(..., min_length=1, max_length=255, description="章节标题")
    subtitle: Optional[str] = Field(None, max_length=255, description="章节副标题")
    content: Optional[str] = Field(None, description="章节正文内容")
    summary: Optional[str] = Field(None, description="章节摘要")
    order_index: int = Field(0, ge=0, description="章节排序索引")
    chapter_number: Optional[int] = Field(None, ge=1, description="章节编号")
    status: ChapterStatus = Field(ChapterStatus.DRAFT, description="章节状态")
    tags: Optional[List[str]] = Field(None, description="章节标签")
    notes: Optional[str] = Field(None, description="写作笔记")
    
    @validator('tags')
    def validate_tags(cls, v):
        if v is not None:
            # 限制标签数量和长度
            if len(v) > 20:
                raise ValueError('标签数量不能超过20个')
            for tag in v:
                if len(tag) > 30:
                    raise ValueError('单个标签长度不能超过30个字符')
        return v

class ChapterCreateSchema(ChapterBaseSchema, BaseCreateSchema):
    """创建章节模式"""
    project_id: uuid.UUID = Field(..., description="所属项目ID")

class ChapterUpdateSchema(BaseUpdateSchema):
    """更新章节模式"""
    title: Optional[str] = Field(None, min_length=1, max_length=255, description="章节标题")
    subtitle: Optional[str] = Field(None, max_length=255, description="章节副标题")
    content: Optional[str] = Field(None, description="章节正文内容")
    summary: Optional[str] = Field(None, description="章节摘要")
    order_index: Optional[int] = Field(None, ge=0, description="章节排序索引")
    chapter_number: Optional[int] = Field(None, ge=1, description="章节编号")
    status: Optional[ChapterStatus] = Field(None, description="章节状态")
    tags: Optional[List[str]] = Field(None, description="章节标签")
    notes: Optional[str] = Field(None, description="写作笔记")

class ChapterResponseSchema(ChapterBaseSchema, BaseResponseSchema):
    """章节响应模式"""
    project_id: uuid.UUID = Field(..., description="所属项目ID")
    word_count: int = Field(0, description="字数统计")
    character_count: int = Field(0, description="字符数统计")
    paragraph_count: int = Field(0, description="段落数统计")
    estimated_reading_time: int = Field(0, description="预计阅读时间（分钟）")
    version: int = Field(1, description="版本号")
    is_current_version: bool = Field(True, description="是否为当前版本")
    parent_version_id: Optional[uuid.UUID] = Field(None, description="父版本ID")
    ai_suggestions: Optional[str] = Field(None, description="AI建议")
    last_edited_at: Optional[datetime] = Field(None, description="最后编辑时间")
    published_at: Optional[datetime] = Field(None, description="发布时间")

class ChapterListSchema(BaseSchema):
    """章节列表模式（简化版）"""
    id: uuid.UUID = Field(..., description="章节ID")
    project_id: uuid.UUID = Field(..., description="所属项目ID")
    title: str = Field(..., description="章节标题")
    subtitle: Optional[str] = Field(None, description="章节副标题")
    order_index: int = Field(0, description="章节排序索引")
    chapter_number: Optional[int] = Field(None, description="章节编号")
    status: ChapterStatus = Field(..., description="章节状态")
    word_count: int = Field(0, description="字数统计")
    estimated_reading_time: int = Field(0, description="预计阅读时间（分钟）")
    version: int = Field(1, description="版本号")
    is_current_version: bool = Field(True, description="是否为当前版本")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    last_edited_at: Optional[datetime] = Field(None, description="最后编辑时间")
    published_at: Optional[datetime] = Field(None, description="发布时间")

class ChapterStatisticsSchema(BaseSchema):
    """章节统计模式"""
    id: uuid.UUID = Field(..., description="章节ID")
    title: str = Field(..., description="章节标题")
    word_count: int = Field(0, description="字数统计")
    character_count: int = Field(0, description="字符数统计")
    paragraph_count: int = Field(0, description="段落数统计")
    estimated_reading_time: int = Field(0, description="预计阅读时间（分钟）")
    status: ChapterStatus = Field(..., description="章节状态")
    created_at: datetime = Field(..., description="创建时间")
    last_edited_at: Optional[datetime] = Field(None, description="最后编辑时间")

class ChapterVersionSchema(BaseSchema):
    """章节版本模式"""
    id: uuid.UUID = Field(..., description="版本ID")
    version: int = Field(..., description="版本号")
    is_current_version: bool = Field(..., description="是否为当前版本")
    parent_version_id: Optional[uuid.UUID] = Field(None, description="父版本ID")
    created_at: datetime = Field(..., description="创建时间")
    created_by: Optional[str] = Field(None, description="创建者")
    word_count: int = Field(0, description="字数统计")
    summary: Optional[str] = Field(None, description="版本摘要")

# 导出的类
__all__ = [
    "ChapterStatus",
    "Chapter",
    "ChapterBaseSchema",
    "ChapterCreateSchema",
    "ChapterUpdateSchema",
    "ChapterResponseSchema",
    "ChapterListSchema",
    "ChapterStatisticsSchema",
    "ChapterVersionSchema"
]