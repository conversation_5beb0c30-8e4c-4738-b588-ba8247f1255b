"""日志系统模块

提供结构化日志记录、日志轮转、性能监控和错误报告功能。
"""

import logging
import logging.handlers
import sys
import json
import time
import functools
import threading
from typing import Dict, Any, Optional, List, Union, Callable
from dataclasses import dataclass, field, asdict
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from enum import Enum
import traceback
import os
from contextlib import contextmanager


class LogLevel(Enum):
    """日志级别"""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


class LogCategory(Enum):
    """日志类别"""
    SYSTEM = "system"
    USER_ACTION = "user_action"
    API_REQUEST = "api_request"
    DATABASE = "database"
    AI_SERVICE = "ai_service"
    FILE_IO = "file_io"
    PERFORMANCE = "performance"
    SECURITY = "security"
    ERROR = "error"
    AUDIT = "audit"


@dataclass
class LogContext:
    """日志上下文"""
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    trace_id: Optional[str] = None
    operation: Optional[str] = None
    component: Optional[str] = None
    category: LogCategory = LogCategory.SYSTEM
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'user_id': self.user_id,
            'session_id': self.session_id,
            'request_id': self.request_id,
            'trace_id': self.trace_id,
            'operation': self.operation,
            'component': self.component,
            'category': self.category.value,
            'metadata': self.metadata
        }


@dataclass
class PerformanceMetrics:
    """性能指标"""
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    memory_usage: Optional[float] = None
    cpu_usage: Optional[float] = None
    operation: Optional[str] = None
    success: bool = True
    error_message: Optional[str] = None
    
    def finish(self, success: bool = True, error_message: Optional[str] = None):
        """完成性能测量"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.success = success
        self.error_message = error_message
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def __init__(self, include_context: bool = True):
        super().__init__()
        self.include_context = include_context
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        # 基础日志信息
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加上下文信息
        if self.include_context and hasattr(record, 'context'):
            log_data['context'] = record.context
        
        # 添加额外字段
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info', 'context']:
                log_data[key] = value
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        return json.dumps(log_data, ensure_ascii=False, default=str)


class ColoredConsoleFormatter(logging.Formatter):
    """彩色控制台格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    }
    
    def __init__(self, use_colors: bool = True):
        super().__init__()
        self.use_colors = use_colors and sys.stdout.isatty()
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        # 基础格式
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
        level = record.levelname
        logger_name = record.name
        message = record.getMessage()
        
        # 添加颜色
        if self.use_colors:
            color = self.COLORS.get(level, '')
            reset = self.COLORS['RESET']
            level = f"{color}{level}{reset}"
        
        # 构建日志行
        log_line = f"[{timestamp}] {level:8} {logger_name}: {message}"
        
        # 添加异常信息
        if record.exc_info:
            log_line += "\n" + self.formatException(record.exc_info)
        
        return log_line


class LoggerManager:
    """日志管理器"""
    
    def __init__(self, log_dir: Optional[Path] = None):
        self.log_dir = log_dir or Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        self.loggers: Dict[str, logging.Logger] = {}
        self.handlers: Dict[str, logging.Handler] = {}
        self.context_stack: List[LogContext] = []
        self.performance_metrics: List[PerformanceMetrics] = []
        
        # 配置根日志器
        self._setup_root_logger()
    
    def _setup_root_logger(self):
        """设置根日志器"""
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(ColoredConsoleFormatter())
        root_logger.addHandler(console_handler)
        self.handlers['console'] = console_handler
        
        # 文件处理器（所有日志）
        all_logs_file = self.log_dir / "all.log"
        file_handler = logging.handlers.RotatingFileHandler(
            all_logs_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(StructuredFormatter())
        root_logger.addHandler(file_handler)
        self.handlers['file'] = file_handler
        
        # 错误日志文件处理器
        error_logs_file = self.log_dir / "error.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_logs_file, maxBytes=5*1024*1024, backupCount=3, encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(StructuredFormatter())
        root_logger.addHandler(error_handler)
        self.handlers['error'] = error_handler
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取日志器"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        return self.loggers[name]
    
    def add_file_handler(self, name: str, filename: str, level: LogLevel = LogLevel.INFO,
                        max_bytes: int = 10*1024*1024, backup_count: int = 5):
        """添加文件处理器"""
        file_path = self.log_dir / filename
        handler = logging.handlers.RotatingFileHandler(
            file_path, maxBytes=max_bytes, backupCount=backup_count, encoding='utf-8'
        )
        handler.setLevel(level.value)
        handler.setFormatter(StructuredFormatter())
        
        # 添加到根日志器
        root_logger = logging.getLogger()
        root_logger.addHandler(handler)
        
        self.handlers[name] = handler
        return handler
    
    def set_level(self, level: LogLevel, handler_name: Optional[str] = None):
        """设置日志级别"""
        if handler_name:
            if handler_name in self.handlers:
                self.handlers[handler_name].setLevel(level.value)
        else:
            # 设置根日志器级别
            logging.getLogger().setLevel(level.value)
    
    @contextmanager
    def context(self, **kwargs):
        """日志上下文管理器"""
        context = LogContext(**kwargs)
        self.context_stack.append(context)
        try:
            yield context
        finally:
            if self.context_stack:
                self.context_stack.pop()
    
    def get_current_context(self) -> Optional[LogContext]:
        """获取当前上下文"""
        return self.context_stack[-1] if self.context_stack else None
    
    def log_with_context(self, logger: logging.Logger, level: LogLevel, 
                        message: str, context: Optional[LogContext] = None, **kwargs):
        """带上下文的日志记录"""
        # 使用提供的上下文或当前上下文
        log_context = context or self.get_current_context()
        
        # 创建日志记录
        record = logger.makeRecord(
            logger.name, level.value, '', 0, message, (), None
        )
        
        # 添加上下文
        if log_context:
            record.context = log_context.to_dict()
        
        # 添加额外字段
        for key, value in kwargs.items():
            setattr(record, key, value)
        
        logger.handle(record)
    
    def start_performance_tracking(self, operation: str) -> PerformanceMetrics:
        """开始性能跟踪"""
        metrics = PerformanceMetrics(
            start_time=time.time(),
            operation=operation
        )
        self.performance_metrics.append(metrics)
        return metrics
    
    def log_performance(self, metrics: PerformanceMetrics, logger_name: str = 'performance'):
        """记录性能指标"""
        logger = self.get_logger(logger_name)
        
        message = f"Performance: {metrics.operation}"
        if metrics.duration:
            message += f" took {metrics.duration:.3f}s"
        
        level = LogLevel.INFO if metrics.success else LogLevel.WARNING
        
        self.log_with_context(
            logger, level, message,
            category=LogCategory.PERFORMANCE,
            performance_metrics=metrics.to_dict()
        )
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = {
            'total_loggers': len(self.loggers),
            'total_handlers': len(self.handlers),
            'performance_metrics_count': len(self.performance_metrics),
            'log_files': []
        }
        
        # 统计日志文件信息
        for log_file in self.log_dir.glob('*.log'):
            if log_file.exists():
                stats['log_files'].append({
                    'name': log_file.name,
                    'size': log_file.stat().st_size,
                    'modified': datetime.fromtimestamp(log_file.stat().st_mtime).isoformat()
                })
        
        return stats
    
    def cleanup_old_logs(self, days: int = 30):
        """清理旧日志文件"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        for log_file in self.log_dir.glob('*.log*'):
            if log_file.exists():
                file_date = datetime.fromtimestamp(log_file.stat().st_mtime)
                if file_date < cutoff_date:
                    try:
                        log_file.unlink()
                        logging.info(f"Deleted old log file: {log_file}")
                    except Exception as e:
                        logging.error(f"Failed to delete log file {log_file}: {e}")


class AuditLogger:
    """审计日志器"""
    
    def __init__(self, logger_manager: LoggerManager):
        self.logger_manager = logger_manager
        self.logger = logger_manager.get_logger('audit')
        
        # 添加专门的审计日志文件
        logger_manager.add_file_handler('audit', 'audit.log', LogLevel.INFO)
    
    def log_user_action(self, user_id: str, action: str, resource: Optional[str] = None,
                       details: Optional[Dict[str, Any]] = None, success: bool = True):
        """记录用户操作"""
        context = LogContext(
            user_id=user_id,
            category=LogCategory.USER_ACTION,
            metadata={
                'action': action,
                'resource': resource,
                'details': details or {},
                'success': success
            }
        )
        
        message = f"User {user_id} {action}"
        if resource:
            message += f" on {resource}"
        
        level = LogLevel.INFO if success else LogLevel.WARNING
        
        self.logger_manager.log_with_context(
            self.logger, level, message, context
        )
    
    def log_security_event(self, event_type: str, user_id: Optional[str] = None,
                          ip_address: Optional[str] = None, 
                          details: Optional[Dict[str, Any]] = None):
        """记录安全事件"""
        context = LogContext(
            user_id=user_id,
            category=LogCategory.SECURITY,
            metadata={
                'event_type': event_type,
                'ip_address': ip_address,
                'details': details or {}
            }
        )
        
        message = f"Security event: {event_type}"
        if user_id:
            message += f" (user: {user_id})"
        if ip_address:
            message += f" (IP: {ip_address})"
        
        self.logger_manager.log_with_context(
            self.logger, LogLevel.WARNING, message, context
        )


def performance_logger(operation: Optional[str] = None, logger_name: str = 'performance'):
    """性能日志装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            op_name = operation or f"{func.__module__}.{func.__name__}"
            metrics = logger_manager.start_performance_tracking(op_name)
            
            try:
                result = func(*args, **kwargs)
                metrics.finish(success=True)
                return result
            except Exception as e:
                metrics.finish(success=False, error_message=str(e))
                raise
            finally:
                logger_manager.log_performance(metrics, logger_name)
        
        return wrapper
    return decorator


def async_performance_logger(operation: Optional[str] = None, logger_name: str = 'performance'):
    """异步性能日志装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            op_name = operation or f"{func.__module__}.{func.__name__}"
            metrics = logger_manager.start_performance_tracking(op_name)
            
            try:
                result = await func(*args, **kwargs)
                metrics.finish(success=True)
                return result
            except Exception as e:
                metrics.finish(success=False, error_message=str(e))
                raise
            finally:
                logger_manager.log_performance(metrics, logger_name)
        
        return wrapper
    return decorator


class RequestLogger:
    """请求日志器"""
    
    def __init__(self, logger_manager: LoggerManager):
        self.logger_manager = logger_manager
        self.logger = logger_manager.get_logger('requests')
        
        # 添加请求日志文件
        logger_manager.add_file_handler('requests', 'requests.log', LogLevel.INFO)
    
    def log_request(self, method: str, url: str, status_code: int,
                   duration: float, user_id: Optional[str] = None,
                   request_id: Optional[str] = None,
                   user_agent: Optional[str] = None,
                   ip_address: Optional[str] = None):
        """记录HTTP请求"""
        context = LogContext(
            user_id=user_id,
            request_id=request_id,
            category=LogCategory.API_REQUEST,
            metadata={
                'method': method,
                'url': url,
                'status_code': status_code,
                'duration': duration,
                'user_agent': user_agent,
                'ip_address': ip_address
            }
        )
        
        message = f"{method} {url} {status_code} {duration:.3f}s"
        
        # 根据状态码确定日志级别
        if status_code < 400:
            level = LogLevel.INFO
        elif status_code < 500:
            level = LogLevel.WARNING
        else:
            level = LogLevel.ERROR
        
        self.logger_manager.log_with_context(
            self.logger, level, message, context
        )


# 全局日志管理器实例
logger_manager = LoggerManager()
audit_logger = AuditLogger(logger_manager)
request_logger = RequestLogger(logger_manager)


# 便捷函数
def get_logger(name: str) -> logging.Logger:
    """获取日志器"""
    return logger_manager.get_logger(name)


def log_performance(operation: str) -> PerformanceMetrics:
    """开始性能跟踪"""
    return logger_manager.start_performance_tracking(operation)


def setup_logging(log_dir: Optional[Path] = None, level: LogLevel = LogLevel.INFO):
    """设置日志系统"""
    global logger_manager
    if log_dir:
        logger_manager = LoggerManager(log_dir)
    logger_manager.set_level(level)


def cleanup_logs(days: int = 30):
    """清理旧日志"""
    logger_manager.cleanup_old_logs(days)


def get_log_stats() -> Dict[str, Any]:
    """获取日志统计"""
    return logger_manager.get_log_statistics()


# 常用装饰器别名
log_performance_sync = performance_logger
log_performance_async = async_performance_logger