"""模型单元测试

测试所有数据模型的功能和验证逻辑。
"""

import pytest
from datetime import datetime, timedelta
from pathlib import Path
import tempfile
import json
from unittest.mock import Mock, patch

# 导入要测试的模型
from src.models.project import (
    Project, ProjectSettings, ProjectTemplate, ProjectType,
    ProjectStatus, BackupSettings, ExportSettings
)
from src.models.story import (
    Story, Chapter, Scene, Character, Location, Event,
    StoryElement, ElementType, RelationType, StoryStructure
)
from src.models.ai_service import (
    AIModel, AIProvider, AIRequest, AIResponse, AIFunction,
    ProcessStatus, ModelCapability
)
from src.models.user import (
    User, UserPreferences, Theme, Language, WritingGoal,
    UserStats, Achievement
)


class TestProject:
    """项目模型测试"""
    
    def test_project_creation(self):
        """测试项目创建"""
        project = Project(
            name="测试小说",
            description="这是一个测试项目",
            project_type=ProjectType.NOVEL,
            author="测试作者"
        )
        
        assert project.name == "测试小说"
        assert project.description == "这是一个测试项目"
        assert project.project_type == ProjectType.NOVEL
        assert project.author == "测试作者"
        assert project.status == ProjectStatus.ACTIVE
        assert isinstance(project.created_at, datetime)
        assert isinstance(project.updated_at, datetime)
        assert project.word_count == 0
        assert project.target_word_count == 50000
    
    def test_project_validation(self):
        """测试项目验证"""
        # 测试空名称
        with pytest.raises(ValueError, match="项目名称不能为空"):
            Project(name="", author="作者")
        
        # 测试空作者
        with pytest.raises(ValueError, match="作者不能为空"):
            Project(name="项目", author="")
        
        # 测试无效的目标字数
        with pytest.raises(ValueError, match="目标字数必须大于0"):
            Project(name="项目", author="作者", target_word_count=-1)
    
    def test_project_progress(self):
        """测试项目进度计算"""
        project = Project(
            name="测试项目",
            author="作者",
            word_count=25000,
            target_word_count=50000
        )
        
        assert project.get_progress() == 0.5
        assert project.get_progress_percentage() == 50.0
    
    def test_project_settings(self):
        """测试项目设置"""
        settings = ProjectSettings(
            auto_save_interval=300,
            backup_enabled=True,
            version_control=True
        )
        
        assert settings.auto_save_interval == 300
        assert settings.backup_enabled is True
        assert settings.version_control is True
        assert settings.export_format == "docx"
    
    def test_project_template(self):
        """测试项目模板"""
        template = ProjectTemplate(
            name="小说模板",
            description="标准小说写作模板",
            project_type=ProjectType.NOVEL,
            structure={
                "chapters": 20,
                "scenes_per_chapter": 3,
                "target_words_per_scene": 1000
            }
        )
        
        assert template.name == "小说模板"
        assert template.project_type == ProjectType.NOVEL
        assert template.structure["chapters"] == 20
        
        # 测试模板应用
        project = template.create_project("新小说", "作者")
        assert project.name == "新小说"
        assert project.project_type == ProjectType.NOVEL


class TestStory:
    """故事模型测试"""
    
    def test_story_creation(self):
        """测试故事创建"""
        story = Story(
            title="测试故事",
            synopsis="这是一个测试故事的简介",
            genre="科幻"
        )
        
        assert story.title == "测试故事"
        assert story.synopsis == "这是一个测试故事的简介"
        assert story.genre == "科幻"
        assert len(story.chapters) == 0
        assert len(story.characters) == 0
        assert isinstance(story.created_at, datetime)
    
    def test_chapter_management(self):
        """测试章节管理"""
        story = Story(title="测试故事")
        
        # 添加章节
        chapter1 = Chapter(
            title="第一章",
            content="这是第一章的内容",
            order=1
        )
        story.add_chapter(chapter1)
        
        assert len(story.chapters) == 1
        assert story.chapters[0].title == "第一章"
        assert story.get_total_word_count() > 0
        
        # 添加第二章
        chapter2 = Chapter(
            title="第二章",
            content="这是第二章的内容",
            order=2
        )
        story.add_chapter(chapter2)
        
        assert len(story.chapters) == 2
        assert story.chapters[1].order == 2
        
        # 删除章节
        story.remove_chapter(chapter1.id)
        assert len(story.chapters) == 1
        assert story.chapters[0].title == "第二章"
    
    def test_character_management(self):
        """测试角色管理"""
        story = Story(title="测试故事")
        
        # 创建角色
        character = Character(
            name="主角",
            description="故事的主人公",
            age=25,
            gender="男",
            occupation="程序员"
        )
        
        story.add_character(character)
        assert len(story.characters) == 1
        assert story.characters[0].name == "主角"
        
        # 查找角色
        found = story.find_character_by_name("主角")
        assert found is not None
        assert found.age == 25
        
        # 删除角色
        story.remove_character(character.id)
        assert len(story.characters) == 0
    
    def test_scene_creation(self):
        """测试场景创建"""
        scene = Scene(
            title="开场场景",
            content="故事开始的场景描述",
            location="咖啡厅",
            time_of_day="上午"
        )
        
        assert scene.title == "开场场景"
        assert scene.location == "咖啡厅"
        assert scene.time_of_day == "上午"
        assert scene.get_word_count() > 0
    
    def test_story_element_relationships(self):
        """测试故事元素关系"""
        character1 = Character(name="角色A")
        character2 = Character(name="角色B")
        
        # 创建关系
        relationship = character1.add_relationship(
            character2,
            RelationType.FRIEND,
            "童年好友"
        )
        
        assert relationship.source_id == character1.id
        assert relationship.target_id == character2.id
        assert relationship.relation_type == RelationType.FRIEND
        assert relationship.description == "童年好友"
    
    def test_story_structure(self):
        """测试故事结构"""
        structure = StoryStructure(
            act_count=3,
            chapter_count=12,
            target_word_count=80000
        )
        
        assert structure.act_count == 3
        assert structure.chapter_count == 12
        assert structure.target_word_count == 80000
        
        # 计算每章目标字数
        words_per_chapter = structure.get_words_per_chapter()
        assert words_per_chapter == 80000 // 12


class TestAIService:
    """AI服务模型测试"""
    
    def test_ai_model_creation(self):
        """测试AI模型创建"""
        model = AIModel(
            name="GPT-4",
            provider=AIProvider.OPENAI,
            max_tokens=4000,
            cost_per_token=0.00003,
            capabilities=[ModelCapability.TEXT_GENERATION, ModelCapability.CHAT]
        )
        
        assert model.name == "GPT-4"
        assert model.provider == AIProvider.OPENAI
        assert model.max_tokens == 4000
        assert ModelCapability.TEXT_GENERATION in model.capabilities
    
    def test_ai_request_creation(self):
        """测试AI请求创建"""
        request = AIRequest(
            function=AIFunction.CONTINUE_WRITING,
            prompt="请继续写这个故事",
            context="故事的前文内容",
            parameters={
                "temperature": 0.7,
                "max_tokens": 1000
            }
        )
        
        assert request.function == AIFunction.CONTINUE_WRITING
        assert request.prompt == "请继续写这个故事"
        assert request.parameters["temperature"] == 0.7
        assert request.status == ProcessStatus.PENDING
    
    def test_ai_response_processing(self):
        """测试AI响应处理"""
        request = AIRequest(
            function=AIFunction.GENERATE_OUTLINE,
            prompt="生成故事大纲"
        )
        
        response = AIResponse(
            request_id=request.id,
            content="这是生成的故事大纲内容",
            tokens_used=150,
            processing_time=2.5
        )
        
        assert response.request_id == request.id
        assert response.content == "这是生成的故事大纲内容"
        assert response.tokens_used == 150
        assert response.processing_time == 2.5
        assert isinstance(response.created_at, datetime)
    
    def test_ai_model_validation(self):
        """测试AI模型验证"""
        # 测试无效的最大token数
        with pytest.raises(ValueError, match="最大token数必须大于0"):
            AIModel(
                name="测试模型",
                provider=AIProvider.OPENAI,
                max_tokens=0
            )
        
        # 测试无效的成本
        with pytest.raises(ValueError, match="每token成本不能为负数"):
            AIModel(
                name="测试模型",
                provider=AIProvider.OPENAI,
                max_tokens=1000,
                cost_per_token=-0.1
            )


class TestUser:
    """用户模型测试"""
    
    def test_user_creation(self):
        """测试用户创建"""
        user = User(
            username="testuser",
            email="<EMAIL>",
            display_name="测试用户"
        )
        
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.display_name == "测试用户"
        assert isinstance(user.created_at, datetime)
        assert user.is_active is True
    
    def test_user_preferences(self):
        """测试用户偏好设置"""
        preferences = UserPreferences(
            theme=Theme.DARK,
            language=Language.ZH_CN,
            auto_save_interval=300,
            font_size=14
        )
        
        assert preferences.theme == Theme.DARK
        assert preferences.language == Language.ZH_CN
        assert preferences.auto_save_interval == 300
        assert preferences.font_size == 14
    
    def test_writing_goal(self):
        """测试写作目标"""
        goal = WritingGoal(
            target_words=1000,
            deadline=datetime.now() + timedelta(days=7),
            description="每日写作目标"
        )
        
        assert goal.target_words == 1000
        assert goal.current_words == 0
        assert goal.description == "每日写作目标"
        assert not goal.is_completed()
        
        # 更新进度
        goal.update_progress(500)
        assert goal.current_words == 500
        assert goal.get_progress() == 0.5
        
        # 完成目标
        goal.update_progress(1000)
        assert goal.is_completed()
    
    def test_user_stats(self):
        """测试用户统计"""
        stats = UserStats(
            total_words_written=50000,
            total_writing_time=120,  # 分钟
            projects_completed=3,
            days_active=30
        )
        
        assert stats.total_words_written == 50000
        assert stats.total_writing_time == 120
        assert stats.projects_completed == 3
        assert stats.get_average_words_per_day() == 50000 / 30
        assert stats.get_words_per_minute() == 50000 / 120
    
    def test_achievement_system(self):
        """测试成就系统"""
        achievement = Achievement(
            name="初出茅庐",
            description="完成第一个项目",
            condition="projects_completed >= 1",
            points=100
        )
        
        assert achievement.name == "初出茅庐"
        assert achievement.points == 100
        assert not achievement.is_unlocked
        
        # 解锁成就
        achievement.unlock()
        assert achievement.is_unlocked
        assert isinstance(achievement.unlocked_at, datetime)


class TestModelIntegration:
    """模型集成测试"""
    
    def test_project_story_integration(self):
        """测试项目和故事的集成"""
        project = Project(
            name="集成测试项目",
            author="测试作者",
            project_type=ProjectType.NOVEL
        )
        
        story = Story(
            title="集成测试故事",
            project_id=project.id
        )
        
        # 添加章节
        chapter = Chapter(
            title="测试章节",
            content="这是测试章节的内容" * 100,  # 增加字数
            story_id=story.id
        )
        story.add_chapter(chapter)
        
        # 更新项目字数
        project.update_word_count(story.get_total_word_count())
        
        assert project.word_count > 0
        assert project.word_count == story.get_total_word_count()
    
    def test_user_project_relationship(self):
        """测试用户和项目的关系"""
        user = User(
            username="author",
            email="<EMAIL>"
        )
        
        project = Project(
            name="用户项目",
            author=user.username,
            user_id=user.id
        )
        
        assert project.user_id == user.id
        assert project.author == user.username
    
    def test_ai_request_response_flow(self):
        """测试AI请求响应流程"""
        # 创建请求
        request = AIRequest(
            function=AIFunction.IMPROVE_TEXT,
            prompt="改进这段文字",
            context="原始文字内容"
        )
        
        # 模拟处理过程
        request.start_processing()
        assert request.status == ProcessStatus.PROCESSING
        
        # 创建响应
        response = AIResponse(
            request_id=request.id,
            content="改进后的文字内容",
            tokens_used=200
        )
        
        # 完成处理
        request.complete_processing(response)
        assert request.status == ProcessStatus.COMPLETED
        assert request.response_id == response.id


# 测试数据工厂
class ModelFactory:
    """模型测试数据工厂"""
    
    @staticmethod
    def create_project(**kwargs):
        """创建测试项目"""
        defaults = {
            "name": "测试项目",
            "author": "测试作者",
            "project_type": ProjectType.NOVEL,
            "description": "这是一个测试项目"
        }
        defaults.update(kwargs)
        return Project(**defaults)
    
    @staticmethod
    def create_story(**kwargs):
        """创建测试故事"""
        defaults = {
            "title": "测试故事",
            "synopsis": "测试故事简介",
            "genre": "测试类型"
        }
        defaults.update(kwargs)
        return Story(**defaults)
    
    @staticmethod
    def create_character(**kwargs):
        """创建测试角色"""
        defaults = {
            "name": "测试角色",
            "description": "测试角色描述",
            "age": 25
        }
        defaults.update(kwargs)
        return Character(**defaults)
    
    @staticmethod
    def create_user(**kwargs):
        """创建测试用户"""
        defaults = {
            "username": "testuser",
            "email": "<EMAIL>",
            "display_name": "测试用户"
        }
        defaults.update(kwargs)
        return User(**defaults)
    
    @staticmethod
    def create_ai_request(**kwargs):
        """创建测试AI请求"""
        defaults = {
            "function": AIFunction.CONTINUE_WRITING,
            "prompt": "测试提示",
            "context": "测试上下文"
        }
        defaults.update(kwargs)
        return AIRequest(**defaults)


# 测试配置
@pytest.fixture
def temp_dir():
    """临时目录fixture"""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def sample_project():
    """示例项目fixture"""
    return ModelFactory.create_project()


@pytest.fixture
def sample_story():
    """示例故事fixture"""
    return ModelFactory.create_story()


@pytest.fixture
def sample_user():
    """示例用户fixture"""
    return ModelFactory.create_user()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])