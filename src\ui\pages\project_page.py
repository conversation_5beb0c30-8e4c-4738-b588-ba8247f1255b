"""项目管理界面

提供项目管理的用户界面，包括项目列表、创建、设置等功能。
"""

import flet as ft
from typing import List, Optional, Dict, Any, Callable
from datetime import datetime
from enum import Enum
from dataclasses import dataclass

from ..components.base import BaseComponent, Button, Input, TextArea, Card, Modal, theme_manager
from ..layouts.grid import GridLayout, GridConfig, GridAlignment
from ..layouts.flex import FlexLayout, FlexDirection, JustifyContent, AlignItems
from ..layouts.container import Container, ContainerConfig, PaddingConfig, MarginConfig
from ...services.project_service import ProjectService, Project, ProjectTemplate, ProjectStatus
from ...models.project import ProjectType, Genre

class ProjectViewMode(str, Enum):
    """项目视图模式"""
    GRID = "grid"  # 网格视图
    LIST = "list"  # 列表视图
    TABLE = "table"  # 表格视图

class ProjectSortBy(str, Enum):
    """项目排序方式"""
    NAME = "name"  # 按名称
    CREATED_DATE = "created_date"  # 按创建日期
    MODIFIED_DATE = "modified_date"  # 按修改日期
    WORD_COUNT = "word_count"  # 按字数
    STATUS = "status"  # 按状态

@dataclass
class ProjectFilter:
    """项目过滤器"""
    status: Optional[ProjectStatus] = None
    project_type: Optional[ProjectType] = None
    genre: Optional[Genre] = None
    search_text: Optional[str] = None
    date_range: Optional[tuple] = None

class ProjectCard(BaseComponent):
    """项目卡片组件"""
    
    def __init__(
        self,
        project: Project,
        on_open: Optional[Callable[[Project], None]] = None,
        on_edit: Optional[Callable[[Project], None]] = None,
        on_delete: Optional[Callable[[Project], None]] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.project = project
        self.on_open = on_open
        self.on_edit = on_edit
        self.on_delete = on_delete
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            # 卡片样式由Card组件处理
            pass
    
    def _get_status_color(self) -> str:
        """获取状态颜色"""
        status_colors = {
            ProjectStatus.DRAFT: "orange",
            ProjectStatus.IN_PROGRESS: "blue",
            ProjectStatus.COMPLETED: "green",
            ProjectStatus.PUBLISHED: "purple",
            ProjectStatus.ARCHIVED: "grey"
        }
        return status_colors.get(self.project.status, "grey")
    
    def _format_date(self, date: datetime) -> str:
        """格式化日期"""
        if not date:
            return "未知"
        
        now = datetime.now()
        diff = now - date
        
        if diff.days == 0:
            return "今天"
        elif diff.days == 1:
            return "昨天"
        elif diff.days < 7:
            return f"{diff.days}天前"
        elif diff.days < 30:
            weeks = diff.days // 7
            return f"{weeks}周前"
        else:
            return date.strftime("%Y-%m-%d")
    
    def build(self) -> ft.Control:
        """构建项目卡片"""
        # 项目封面或图标
        cover = ft.Container(
            content=ft.Icon(
                ft.icons.BOOK,
                size=48,
                color=self.theme.colors.primary
            ),
            width=80,
            height=80,
            bgcolor=self.theme.colors.primary_container,
            border_radius=8,
            alignment=ft.alignment.center
        )
        
        # 项目信息
        title = ft.Text(
            self.project.title,
            size=16,
            weight=ft.FontWeight.BOLD,
            max_lines=2,
            overflow=ft.TextOverflow.ELLIPSIS
        )
        
        description = ft.Text(
            self.project.description or "暂无描述",
            size=12,
            color=self.theme.colors.on_surface_variant,
            max_lines=3,
            overflow=ft.TextOverflow.ELLIPSIS
        )
        
        # 状态标签
        status_chip = ft.Container(
            content=ft.Text(
                self.project.status.value,
                size=10,
                color="white",
                weight=ft.FontWeight.BOLD
            ),
            bgcolor=self._get_status_color(),
            padding=ft.padding.symmetric(horizontal=8, vertical=4),
            border_radius=12
        )
        
        # 统计信息
        stats = ft.Row([
            ft.Row([
                ft.Icon(ft.icons.TEXT_FIELDS, size=14, color=self.theme.colors.on_surface_variant),
                ft.Text(f"{self.project.word_count:,}", size=12, color=self.theme.colors.on_surface_variant)
            ], spacing=4),
            ft.Row([
                ft.Icon(ft.icons.ACCESS_TIME, size=14, color=self.theme.colors.on_surface_variant),
                ft.Text(self._format_date(self.project.updated_at), size=12, color=self.theme.colors.on_surface_variant)
            ], spacing=4)
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
        
        # 操作按钮
        action_buttons = ft.Row([
            ft.IconButton(
                icon=ft.icons.OPEN_IN_NEW,
                tooltip="打开项目",
                on_click=lambda e: self.on_open(self.project) if self.on_open else None
            ),
            ft.IconButton(
                icon=ft.icons.EDIT,
                tooltip="编辑项目",
                on_click=lambda e: self.on_edit(self.project) if self.on_edit else None
            ),
            ft.PopupMenuButton(
                icon=ft.icons.MORE_VERT,
                items=[
                    ft.PopupMenuItem(
                        text="删除",
                        icon=ft.icons.DELETE,
                        on_click=lambda e: self.on_delete(self.project) if self.on_delete else None
                    ),
                    ft.PopupMenuItem(
                        text="复制",
                        icon=ft.icons.COPY
                    ),
                    ft.PopupMenuItem(
                        text="导出",
                        icon=ft.icons.DOWNLOAD
                    )
                ]
            )
        ], alignment=ft.MainAxisAlignment.END)
        
        # 卡片内容
        card_content = ft.Column([
            ft.Row([
                cover,
                ft.Container(
                    content=ft.Column([
                        ft.Row([title, status_chip], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        description,
                        stats
                    ], spacing=8),
                    expand=True,
                    padding=ft.padding.only(left=12)
                )
            ], spacing=0),
            ft.Divider(height=1),
            action_buttons
        ], spacing=12)
        
        # 创建卡片
        card = ft.Card(
            content=ft.Container(
                content=card_content,
                padding=ft.padding.all(16),
                width=320,
                height=200
            ),
            elevation=2
        )
        
        return card

class ProjectStatsWidget(BaseComponent):
    """项目统计组件"""
    
    def __init__(
        self,
        projects: List[Project],
        **kwargs
    ):
        super().__init__(**kwargs)
        self.projects = projects
    
    def _calculate_stats(self) -> Dict[str, Any]:
        """计算统计数据"""
        if not self.projects:
            return {
                'total_projects': 0,
                'total_words': 0,
                'status_counts': {},
                'type_counts': {},
                'recent_activity': 0
            }
        
        total_words = sum(p.word_count for p in self.projects)
        
        # 按状态统计
        status_counts = {}
        for project in self.projects:
            status = project.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # 按类型统计
        type_counts = {}
        for project in self.projects:
            project_type = project.project_type.value
            type_counts[project_type] = type_counts.get(project_type, 0) + 1
        
        # 最近活动（7天内更新的项目）
        now = datetime.now()
        recent_activity = sum(
            1 for p in self.projects
            if p.updated_at and (now - p.updated_at).days <= 7
        )
        
        return {
            'total_projects': len(self.projects),
            'total_words': total_words,
            'status_counts': status_counts,
            'type_counts': type_counts,
            'recent_activity': recent_activity
        }
    
    def build(self) -> ft.Control:
        """构建统计组件"""
        stats = self._calculate_stats()
        
        # 统计卡片
        stat_cards = [
            self._create_stat_card(
                "项目总数",
                str(stats['total_projects']),
                ft.icons.FOLDER,
                "primary"
            ),
            self._create_stat_card(
                "总字数",
                f"{stats['total_words']:,}",
                ft.icons.TEXT_FIELDS,
                "secondary"
            ),
            self._create_stat_card(
                "最近活动",
                str(stats['recent_activity']),
                ft.icons.TRENDING_UP,
                "tertiary"
            )
        ]
        
        # 状态分布图表（简化版）
        status_chart = self._create_status_chart(stats['status_counts'])
        
        return ft.Column([
            ft.Text("项目统计", size=18, weight=ft.FontWeight.BOLD),
            ft.Row(stat_cards, spacing=16),
            ft.Container(height=16),
            status_chart
        ], spacing=8)
    
    def _create_stat_card(self, title: str, value: str, icon: str, color: str) -> ft.Control:
        """创建统计卡片"""
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(icon, size=24, color=getattr(self.theme.colors, color, self.theme.colors.primary)),
                        ft.Text(value, size=24, weight=ft.FontWeight.BOLD)
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                    ft.Text(title, size=12, color=self.theme.colors.on_surface_variant)
                ], spacing=8),
                padding=ft.padding.all(16),
                width=150
            ),
            elevation=1
        )
    
    def _create_status_chart(self, status_counts: Dict[str, int]) -> ft.Control:
        """创建状态分布图表"""
        if not status_counts:
            return ft.Container(
                content=ft.Text("暂无数据", color=self.theme.colors.on_surface_variant),
                alignment=ft.alignment.center,
                height=100
            )
        
        # 简化的条形图
        total = sum(status_counts.values())
        bars = []
        
        colors = {
            "草稿": "orange",
            "进行中": "blue",
            "已完成": "green",
            "已发布": "purple",
            "已归档": "grey"
        }
        
        for status, count in status_counts.items():
            percentage = (count / total) * 100 if total > 0 else 0
            
            bar = ft.Container(
                content=ft.Row([
                    ft.Container(
                        bgcolor=colors.get(status, "grey"),
                        height=20,
                        width=percentage * 2,  # 简单的比例缩放
                        border_radius=2
                    ),
                    ft.Text(f"{status}: {count}", size=12)
                ], spacing=8),
                margin=ft.margin.symmetric(vertical=2)
            )
            bars.append(bar)
        
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Text("状态分布", size=14, weight=ft.FontWeight.BOLD),
                    ft.Column(bars, spacing=4)
                ], spacing=8),
                padding=ft.padding.all(16)
            ),
            elevation=1
        )

class ProjectTemplateSelector(BaseComponent):
    """项目模板选择器"""
    
    def __init__(
        self,
        templates: List[ProjectTemplate],
        on_select: Optional[Callable[[ProjectTemplate], None]] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.templates = templates
        self.on_select = on_select
        self.selected_template: Optional[ProjectTemplate] = None
    
    def build(self) -> ft.Control:
        """构建模板选择器"""
        if not self.templates:
            return ft.Container(
                content=ft.Text("暂无可用模板", color=self.theme.colors.on_surface_variant),
                alignment=ft.alignment.center,
                height=200
            )
        
        template_cards = []
        for template in self.templates:
            card = self._create_template_card(template)
            template_cards.append(card)
        
        return ft.Column([
            ft.Text("选择项目模板", size=18, weight=ft.FontWeight.BOLD),
            ft.Container(
                content=ft.GridView(
                    template_cards,
                    runs_count=3,
                    max_extent=200,
                    child_aspect_ratio=0.8,
                    spacing=16,
                    run_spacing=16
                ),
                height=400
            )
        ], spacing=16)
    
    def _create_template_card(self, template: ProjectTemplate) -> ft.Control:
        """创建模板卡片"""
        is_selected = self.selected_template == template
        
        card_content = ft.Column([
            ft.Icon(
                ft.icons.DESCRIPTION,
                size=48,
                color=self.theme.colors.primary if is_selected else self.theme.colors.on_surface_variant
            ),
            ft.Text(
                template.name,
                size=14,
                weight=ft.FontWeight.BOLD,
                text_align=ft.TextAlign.CENTER,
                max_lines=2
            ),
            ft.Text(
                template.description,
                size=12,
                color=self.theme.colors.on_surface_variant,
                text_align=ft.TextAlign.CENTER,
                max_lines=3
            )
        ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.CENTER)
        
        card = ft.Card(
            content=ft.Container(
                content=card_content,
                padding=ft.padding.all(16),
                width=180,
                height=200
            ),
            elevation=3 if is_selected else 1,
            color=self.theme.colors.primary_container if is_selected else None
        )
        
        # 添加点击事件
        card.on_click = lambda e, t=template: self._on_template_click(t)
        
        return card
    
    def _on_template_click(self, template: ProjectTemplate):
        """模板点击事件"""
        self.selected_template = template
        if self.on_select:
            self.on_select(template)
        
        # 更新UI
        if self._control:
            self._control = self.build()
            self.update()

class ProjectCreatePage(BaseComponent):
    """项目创建页面"""
    
    def __init__(
        self,
        project_service: ProjectService,
        on_created: Optional[Callable[[Project], None]] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.project_service = project_service
        self.on_created = on_created
        
        # 表单字段
        self.title_field = Input(label="项目标题", required=True)
        self.description_field = TextArea(label="项目描述", height=100)
        self.type_dropdown = ft.Dropdown(
            label="项目类型",
            options=[
                ft.dropdown.Option(ptype.value, ptype.value) for ptype in ProjectType
            ]
        )
        self.genre_dropdown = ft.Dropdown(
            label="题材类型",
            options=[
                ft.dropdown.Option(genre.value, genre.value) for genre in Genre
            ]
        )
        
        # 模板选择器
        templates = self.project_service.get_templates()
        self.template_selector = ProjectTemplateSelector(
            templates=templates,
            on_select=self._on_template_select
        )
        
        self.selected_template: Optional[ProjectTemplate] = None
    
    def _on_template_select(self, template: ProjectTemplate):
        """模板选择回调"""
        self.selected_template = template
        
        # 根据模板填充表单
        if template.default_title:
            self.title_field.set_value(template.default_title)
        if template.default_description:
            self.description_field.set_value(template.default_description)
        if template.project_type:
            self.type_dropdown.value = template.project_type.value
        if template.genre:
            self.genre_dropdown.value = template.genre.value
    
    def _validate_form(self) -> bool:
        """验证表单"""
        if not self.title_field.get_value().strip():
            self._show_error("请输入项目标题")
            return False
        
        if not self.type_dropdown.value:
            self._show_error("请选择项目类型")
            return False
        
        return True
    
    def _show_error(self, message: str):
        """显示错误信息"""
        # 这里应该显示错误对话框或通知
        print(f"错误: {message}")
    
    def _create_project(self, e):
        """创建项目"""
        if not self._validate_form():
            return
        
        try:
            # 创建项目数据
            project_data = {
                'title': self.title_field.get_value().strip(),
                'description': self.description_field.get_value().strip(),
                'project_type': ProjectType(self.type_dropdown.value),
                'genre': Genre(self.genre_dropdown.value) if self.genre_dropdown.value else None,
                'template_id': self.selected_template.id if self.selected_template else None
            }
            
            # 调用服务创建项目
            project = self.project_service.create_project(**project_data)
            
            if self.on_created:
                self.on_created(project)
            
            # 清空表单
            self._clear_form()
            
        except Exception as e:
            self._show_error(f"创建项目失败: {str(e)}")
    
    def _clear_form(self):
        """清空表单"""
        self.title_field.set_value("")
        self.description_field.set_value("")
        self.type_dropdown.value = None
        self.genre_dropdown.value = None
        self.selected_template = None
    
    def build(self) -> ft.Control:
        """构建创建页面"""
        # 表单区域
        form_section = ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Text("基本信息", size=16, weight=ft.FontWeight.BOLD),
                    self.title_field.get_control(),
                    self.description_field.get_control(),
                    ft.Row([
                        ft.Container(content=self.type_dropdown, expand=True),
                        ft.Container(content=self.genre_dropdown, expand=True)
                    ], spacing=16),
                ], spacing=16),
                padding=ft.padding.all(20)
            ),
            elevation=1
        )
        
        # 模板选择区域
        template_section = ft.Card(
            content=ft.Container(
                content=self.template_selector.get_control(),
                padding=ft.padding.all(20)
            ),
            elevation=1
        )
        
        # 操作按钮
        action_buttons = ft.Row([
            ft.ElevatedButton(
                "取消",
                icon=ft.icons.CANCEL,
                on_click=lambda e: self._clear_form()
            ),
            ft.ElevatedButton(
                "创建项目",
                icon=ft.icons.ADD,
                on_click=self._create_project
            )
        ], alignment=ft.MainAxisAlignment.END, spacing=16)
        
        return ft.Column([
            ft.Text("创建新项目", size=24, weight=ft.FontWeight.BOLD),
            form_section,
            template_section,
            action_buttons
        ], spacing=20, scroll=ft.ScrollMode.AUTO)

class ProjectListPage(BaseComponent):
    """项目列表页面"""
    
    def __init__(
        self,
        project_service: ProjectService,
        on_open_project: Optional[Callable[[Project], None]] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.project_service = project_service
        self.on_open_project = on_open_project
        
        # 状态
        self.projects: List[Project] = []
        self.filtered_projects: List[Project] = []
        self.view_mode = ProjectViewMode.GRID
        self.sort_by = ProjectSortBy.MODIFIED_DATE
        self.current_filter = ProjectFilter()
        
        # 搜索框
        self.search_field = Input(
            label="搜索项目",
            prefix_icon=ft.icons.SEARCH,
            on_change=self._on_search_change
        )
        
        # 加载项目
        self._load_projects()
    
    def _load_projects(self):
        """加载项目列表"""
        try:
            self.projects = self.project_service.get_all_projects()
            self._apply_filter()
        except Exception as e:
            print(f"加载项目失败: {e}")
            self.projects = []
            self.filtered_projects = []
    
    def _apply_filter(self):
        """应用过滤器"""
        filtered = self.projects.copy()
        
        # 搜索文本过滤
        if self.current_filter.search_text:
            search_text = self.current_filter.search_text.lower()
            filtered = [
                p for p in filtered
                if search_text in p.title.lower() or
                   (p.description and search_text in p.description.lower())
            ]
        
        # 状态过滤
        if self.current_filter.status:
            filtered = [p for p in filtered if p.status == self.current_filter.status]
        
        # 类型过滤
        if self.current_filter.project_type:
            filtered = [p for p in filtered if p.project_type == self.current_filter.project_type]
        
        # 题材过滤
        if self.current_filter.genre:
            filtered = [p for p in filtered if p.genre == self.current_filter.genre]
        
        # 排序
        if self.sort_by == ProjectSortBy.NAME:
            filtered.sort(key=lambda p: p.title)
        elif self.sort_by == ProjectSortBy.CREATED_DATE:
            filtered.sort(key=lambda p: p.created_at, reverse=True)
        elif self.sort_by == ProjectSortBy.MODIFIED_DATE:
            filtered.sort(key=lambda p: p.updated_at, reverse=True)
        elif self.sort_by == ProjectSortBy.WORD_COUNT:
            filtered.sort(key=lambda p: p.word_count, reverse=True)
        elif self.sort_by == ProjectSortBy.STATUS:
            filtered.sort(key=lambda p: p.status.value)
        
        self.filtered_projects = filtered
    
    def _on_search_change(self, e):
        """搜索变化事件"""
        self.current_filter.search_text = e.control.value
        self._apply_filter()
        if self._control:
            self.update()
    
    def _on_view_mode_change(self, mode: ProjectViewMode):
        """视图模式变化"""
        self.view_mode = mode
        if self._control:
            self.update()
    
    def _on_sort_change(self, sort_by: ProjectSortBy):
        """排序变化"""
        self.sort_by = sort_by
        self._apply_filter()
        if self._control:
            self.update()
    
    def _on_open_project(self, project: Project):
        """打开项目"""
        if self.on_open_project:
            self.on_open_project(project)
    
    def _on_edit_project(self, project: Project):
        """编辑项目"""
        # 这里应该打开项目设置页面
        print(f"编辑项目: {project.title}")
    
    def _on_delete_project(self, project: Project):
        """删除项目"""
        try:
            self.project_service.delete_project(project.id)
            self._load_projects()
            if self._control:
                self.update()
        except Exception as e:
            print(f"删除项目失败: {e}")
    
    def _create_toolbar(self) -> ft.Control:
        """创建工具栏"""
        # 视图模式按钮
        view_buttons = ft.Row([
            ft.IconButton(
                icon=ft.icons.GRID_VIEW,
                selected=self.view_mode == ProjectViewMode.GRID,
                on_click=lambda e: self._on_view_mode_change(ProjectViewMode.GRID)
            ),
            ft.IconButton(
                icon=ft.icons.LIST,
                selected=self.view_mode == ProjectViewMode.LIST,
                on_click=lambda e: self._on_view_mode_change(ProjectViewMode.LIST)
            )
        ])
        
        # 排序下拉框
        sort_dropdown = ft.Dropdown(
            label="排序",
            value=self.sort_by.value,
            options=[
                ft.dropdown.Option(sort.value, sort.value) for sort in ProjectSortBy
            ],
            on_change=lambda e: self._on_sort_change(ProjectSortBy(e.control.value)),
            width=150
        )
        
        # 过滤器按钮
        filter_button = ft.IconButton(
            icon=ft.icons.FILTER_LIST,
            tooltip="过滤器"
        )
        
        return ft.Row([
            self.search_field.get_control(),
            view_buttons,
            sort_dropdown,
            filter_button
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
    
    def _create_project_grid(self) -> ft.Control:
        """创建项目网格"""
        if not self.filtered_projects:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(ft.icons.FOLDER_OPEN, size=64, color=self.theme.colors.on_surface_variant),
                    ft.Text("暂无项目", size=16, color=self.theme.colors.on_surface_variant),
                    ft.Text("点击创建按钮开始您的第一个项目", size=12, color=self.theme.colors.on_surface_variant)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                alignment=ft.alignment.center,
                height=300
            )
        
        project_cards = []
        for project in self.filtered_projects:
            card = ProjectCard(
                project=project,
                on_open=self._on_open_project,
                on_edit=self._on_edit_project,
                on_delete=self._on_delete_project
            )
            project_cards.append(card.get_control())
        
        if self.view_mode == ProjectViewMode.GRID:
            return ft.GridView(
                project_cards,
                runs_count=3,
                max_extent=340,
                child_aspect_ratio=1.6,
                spacing=16,
                run_spacing=16
            )
        else:
            return ft.Column(project_cards, spacing=8)
    
    def build(self) -> ft.Control:
        """构建项目列表页面"""
        # 统计组件
        stats_widget = ProjectStatsWidget(self.projects)
        
        # 工具栏
        toolbar = self._create_toolbar()
        
        # 项目网格
        project_grid = self._create_project_grid()
        
        return ft.Column([
            ft.Text("我的项目", size=24, weight=ft.FontWeight.BOLD),
            stats_widget.get_control(),
            ft.Divider(),
            toolbar,
            ft.Container(
                content=project_grid,
                expand=True
            )
        ], spacing=16, scroll=ft.ScrollMode.AUTO)

class ProjectSettingsPage(BaseComponent):
    """项目设置页面"""
    
    def __init__(
        self,
        project: Project,
        project_service: ProjectService,
        on_updated: Optional[Callable[[Project], None]] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.project = project
        self.project_service = project_service
        self.on_updated = on_updated
        
        # 表单字段
        self.title_field = Input(label="项目标题", value=project.title)
        self.description_field = TextArea(label="项目描述", value=project.description or "", height=100)
        self.type_dropdown = ft.Dropdown(
            label="项目类型",
            value=project.project_type.value,
            options=[
                ft.dropdown.Option(ptype.value, ptype.value) for ptype in ProjectType
            ]
        )
        self.genre_dropdown = ft.Dropdown(
            label="题材类型",
            value=project.genre.value if project.genre else None,
            options=[
                ft.dropdown.Option(genre.value, genre.value) for genre in Genre
            ]
        )
        self.status_dropdown = ft.Dropdown(
            label="项目状态",
            value=project.status.value,
            options=[
                ft.dropdown.Option(status.value, status.value) for status in ProjectStatus
            ]
        )
    
    def _save_settings(self, e):
        """保存设置"""
        try:
            # 更新项目数据
            updates = {
                'title': self.title_field.get_value().strip(),
                'description': self.description_field.get_value().strip(),
                'project_type': ProjectType(self.type_dropdown.value),
                'genre': Genre(self.genre_dropdown.value) if self.genre_dropdown.value else None,
                'status': ProjectStatus(self.status_dropdown.value)
            }
            
            # 调用服务更新项目
            updated_project = self.project_service.update_project(self.project.id, **updates)
            
            if self.on_updated:
                self.on_updated(updated_project)
            
            # 显示成功消息
            print("项目设置已保存")
            
        except Exception as e:
            print(f"保存设置失败: {str(e)}")
    
    def build(self) -> ft.Control:
        """构建设置页面"""
        # 基本设置
        basic_settings = ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Text("基本设置", size=16, weight=ft.FontWeight.BOLD),
                    self.title_field.get_control(),
                    self.description_field.get_control(),
                    ft.Row([
                        ft.Container(content=self.type_dropdown, expand=True),
                        ft.Container(content=self.genre_dropdown, expand=True)
                    ], spacing=16),
                    self.status_dropdown
                ], spacing=16),
                padding=ft.padding.all(20)
            ),
            elevation=1
        )
        
        # 项目统计
        stats_section = ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Text("项目统计", size=16, weight=ft.FontWeight.BOLD),
                    ft.Row([
                        ft.Text(f"字数: {self.project.word_count:,}"),
                        ft.Text(f"章节: {self.project.chapter_count}")
                    ], alignment=ft.MainAxisAlignment.SPACE_AROUND),
                    ft.Row([
                        ft.Text(f"创建时间: {self.project.created_at.strftime('%Y-%m-%d %H:%M')}"),
                        ft.Text(f"更新时间: {self.project.updated_at.strftime('%Y-%m-%d %H:%M')}")
                    ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
                ], spacing=12),
                padding=ft.padding.all(20)
            ),
            elevation=1
        )
        
        # 操作按钮
        action_buttons = ft.Row([
            ft.OutlinedButton(
                "重置",
                icon=ft.icons.REFRESH,
                on_click=lambda e: self._reset_form()
            ),
            ft.ElevatedButton(
                "保存设置",
                icon=ft.icons.SAVE,
                on_click=self._save_settings
            )
        ], alignment=ft.MainAxisAlignment.END, spacing=16)
        
        return ft.Column([
            ft.Text(f"项目设置 - {self.project.title}", size=24, weight=ft.FontWeight.BOLD),
            basic_settings,
            stats_section,
            action_buttons
        ], spacing=20, scroll=ft.ScrollMode.AUTO)
    
    def _reset_form(self):
        """重置表单"""
        self.title_field.set_value(self.project.title)
        self.description_field.set_value(self.project.description or "")
        self.type_dropdown.value = self.project.project_type.value
        self.genre_dropdown.value = self.project.genre.value if self.project.genre else None
        self.status_dropdown.value = self.project.status.value

# 导出
__all__ = [
    'ProjectViewMode',
    'ProjectSortBy',
    'ProjectFilter',
    'ProjectCard',
    'ProjectStatsWidget',
    'ProjectTemplateSelector',
    'ProjectCreatePage',
    'ProjectListPage',
    'ProjectSettingsPage',
]