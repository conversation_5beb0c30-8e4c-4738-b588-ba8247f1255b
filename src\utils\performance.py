"""性能优化和内存管理模块

提供应用性能监控、内存管理、缓存策略和异步处理功能。
"""

import asyncio
import time
import threading
import weakref
import gc
import psutil
import functools
from typing import Dict, List, Any, Optional, Callable, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from queue import Queue, PriorityQueue
from collections import defaultdict, deque
import logging
import json
import pickle
import hashlib
from pathlib import Path

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: datetime = field(default_factory=datetime.now)
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_used_mb: float = 0.0
    disk_io_read: int = 0
    disk_io_write: int = 0
    network_sent: int = 0
    network_recv: int = 0
    active_threads: int = 0
    open_files: int = 0
    response_time: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'cpu_percent': self.cpu_percent,
            'memory_percent': self.memory_percent,
            'memory_used_mb': self.memory_used_mb,
            'disk_io_read': self.disk_io_read,
            'disk_io_write': self.disk_io_write,
            'network_sent': self.network_sent,
            'network_recv': self.network_recv,
            'active_threads': self.active_threads,
            'open_files': self.open_files,
            'response_time': self.response_time
        }


@dataclass
class CacheEntry:
    """缓存条目"""
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    ttl: Optional[timedelta] = None
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return datetime.now() - self.created_at > self.ttl
    
    def touch(self):
        """更新访问时间"""
        self.last_accessed = datetime.now()
        self.access_count += 1


class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, warning_threshold: float = 80.0, critical_threshold: float = 90.0):
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.callbacks = {
            'warning': [],
            'critical': [],
            'normal': []
        }
        self._monitoring = False
        self._monitor_thread = None
        self.metrics_history = deque(maxlen=1000)
        
    def add_callback(self, level: str, callback: Callable[[float], None]):
        """添加内存使用回调"""
        if level in self.callbacks:
            self.callbacks[level].append(callback)
    
    def get_memory_info(self) -> Dict[str, Any]:
        """获取内存信息"""
        process = psutil.Process()
        memory_info = process.memory_info()
        system_memory = psutil.virtual_memory()
        
        return {
            'process_memory_mb': memory_info.rss / 1024 / 1024,
            'process_memory_percent': process.memory_percent(),
            'system_memory_percent': system_memory.percent,
            'system_available_mb': system_memory.available / 1024 / 1024,
            'system_total_mb': system_memory.total / 1024 / 1024
        }
    
    def start_monitoring(self, interval: float = 5.0):
        """开始监控"""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self._monitor_thread.start()
        logger.info("内存监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=1.0)
        logger.info("内存监控已停止")
    
    def _monitor_loop(self, interval: float):
        """监控循环"""
        while self._monitoring:
            try:
                memory_info = self.get_memory_info()
                memory_percent = memory_info['system_memory_percent']
                
                # 记录历史数据
                self.metrics_history.append({
                    'timestamp': datetime.now(),
                    'memory_percent': memory_percent,
                    'process_memory_mb': memory_info['process_memory_mb']
                })
                
                # 触发回调
                if memory_percent >= self.critical_threshold:
                    for callback in self.callbacks['critical']:
                        try:
                            callback(memory_percent)
                        except Exception as e:
                            logger.error(f"Critical memory callback error: {e}")
                elif memory_percent >= self.warning_threshold:
                    for callback in self.callbacks['warning']:
                        try:
                            callback(memory_percent)
                        except Exception as e:
                            logger.error(f"Warning memory callback error: {e}")
                else:
                    for callback in self.callbacks['normal']:
                        try:
                            callback(memory_percent)
                        except Exception as e:
                            logger.error(f"Normal memory callback error: {e}")
                
                time.sleep(interval)
            except Exception as e:
                logger.error(f"Memory monitoring error: {e}")
                time.sleep(interval)
    
    def force_garbage_collection(self):
        """强制垃圾回收"""
        collected = gc.collect()
        logger.info(f"垃圾回收完成，回收了 {collected} 个对象")
        return collected
    
    def get_memory_usage_trend(self, minutes: int = 30) -> List[Dict[str, Any]]:
        """获取内存使用趋势"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [
            entry for entry in self.metrics_history
            if entry['timestamp'] >= cutoff_time
        ]


class SmartCache:
    """智能缓存系统"""
    
    def __init__(self, max_size: int = 1000, default_ttl: Optional[timedelta] = None):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheEntry] = {}
        self._access_order = deque()
        self._lock = threading.RLock()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'size': 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                self.stats['misses'] += 1
                return None
            
            entry = self._cache[key]
            
            # 检查是否过期
            if entry.is_expired():
                del self._cache[key]
                self._remove_from_access_order(key)
                self.stats['misses'] += 1
                return None
            
            # 更新访问信息
            entry.touch()
            self._update_access_order(key)
            self.stats['hits'] += 1
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[timedelta] = None):
        """设置缓存值"""
        with self._lock:
            # 如果缓存已满，执行LRU淘汰
            if len(self._cache) >= self.max_size and key not in self._cache:
                self._evict_lru()
            
            # 创建缓存条目
            entry = CacheEntry(
                value=value,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                ttl=ttl or self.default_ttl
            )
            
            self._cache[key] = entry
            self._update_access_order(key)
            self.stats['size'] = len(self._cache)
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                self._remove_from_access_order(key)
                self.stats['size'] = len(self._cache)
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._access_order.clear()
            self.stats['size'] = 0
    
    def _evict_lru(self):
        """淘汰最近最少使用的项"""
        if not self._access_order:
            return
        
        lru_key = self._access_order.popleft()
        if lru_key in self._cache:
            del self._cache[lru_key]
            self.stats['evictions'] += 1
    
    def _update_access_order(self, key: str):
        """更新访问顺序"""
        self._remove_from_access_order(key)
        self._access_order.append(key)
    
    def _remove_from_access_order(self, key: str):
        """从访问顺序中移除"""
        try:
            self._access_order.remove(key)
        except ValueError:
            pass
    
    def cleanup_expired(self) -> int:
        """清理过期项"""
        with self._lock:
            expired_keys = [
                key for key, entry in self._cache.items()
                if entry.is_expired()
            ]
            
            for key in expired_keys:
                del self._cache[key]
                self._remove_from_access_order(key)
            
            self.stats['size'] = len(self._cache)
            return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0
            
            return {
                **self.stats,
                'hit_rate': hit_rate,
                'total_requests': total_requests
            }


class AsyncTaskManager:
    """异步任务管理器"""
    
    def __init__(self, max_concurrent_tasks: int = 10):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.task_results: Dict[str, Any] = {}
        self.task_stats = {
            'completed': 0,
            'failed': 0,
            'cancelled': 0
        }
    
    async def submit_task(self, task_id: str, coro: Callable, *args, **kwargs) -> str:
        """提交异步任务"""
        if task_id in self.active_tasks:
            raise ValueError(f"Task {task_id} is already running")
        
        async def _run_task():
            async with self.semaphore:
                try:
                    result = await coro(*args, **kwargs)
                    self.task_results[task_id] = {'status': 'completed', 'result': result}
                    self.task_stats['completed'] += 1
                    return result
                except asyncio.CancelledError:
                    self.task_results[task_id] = {'status': 'cancelled'}
                    self.task_stats['cancelled'] += 1
                    raise
                except Exception as e:
                    self.task_results[task_id] = {'status': 'failed', 'error': str(e)}
                    self.task_stats['failed'] += 1
                    raise
                finally:
                    if task_id in self.active_tasks:
                        del self.active_tasks[task_id]
        
        task = asyncio.create_task(_run_task())
        self.active_tasks[task_id] = task
        return task_id
    
    async def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> Any:
        """等待任务完成"""
        if task_id not in self.active_tasks:
            if task_id in self.task_results:
                result = self.task_results[task_id]
                if result['status'] == 'completed':
                    return result['result']
                elif result['status'] == 'failed':
                    raise Exception(result['error'])
                else:
                    raise asyncio.CancelledError()
            else:
                raise ValueError(f"Task {task_id} not found")
        
        task = self.active_tasks[task_id]
        return await asyncio.wait_for(task, timeout=timeout)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            task.cancel()
            return True
        return False
    
    def get_task_status(self, task_id: str) -> Optional[str]:
        """获取任务状态"""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            if task.done():
                if task.cancelled():
                    return 'cancelled'
                elif task.exception():
                    return 'failed'
                else:
                    return 'completed'
            else:
                return 'running'
        elif task_id in self.task_results:
            return self.task_results[task_id]['status']
        else:
            return None
    
    def get_active_tasks(self) -> List[str]:
        """获取活跃任务列表"""
        return list(self.active_tasks.keys())
    
    def get_stats(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        return {
            **self.task_stats,
            'active': len(self.active_tasks),
            'max_concurrent': self.max_concurrent_tasks
        }


class FileProcessor:
    """大文件处理器"""
    
    def __init__(self, chunk_size: int = 8192, max_workers: int = 4):
        self.chunk_size = chunk_size
        self.max_workers = max_workers
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)
    
    async def read_large_file_async(self, file_path: Path, 
                                   progress_callback: Optional[Callable[[int, int], None]] = None) -> str:
        """异步读取大文件"""
        def _read_file():
            content_parts = []
            file_size = file_path.stat().st_size
            bytes_read = 0
            
            with open(file_path, 'r', encoding='utf-8') as f:
                while True:
                    chunk = f.read(self.chunk_size)
                    if not chunk:
                        break
                    
                    content_parts.append(chunk)
                    bytes_read += len(chunk.encode('utf-8'))
                    
                    if progress_callback:
                        progress_callback(bytes_read, file_size)
            
            return ''.join(content_parts)
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.thread_pool, _read_file)
    
    async def write_large_file_async(self, file_path: Path, content: str,
                                    progress_callback: Optional[Callable[[int, int], None]] = None):
        """异步写入大文件"""
        def _write_file():
            content_bytes = content.encode('utf-8')
            total_size = len(content_bytes)
            bytes_written = 0
            
            with open(file_path, 'wb') as f:
                for i in range(0, total_size, self.chunk_size):
                    chunk = content_bytes[i:i + self.chunk_size]
                    f.write(chunk)
                    bytes_written += len(chunk)
                    
                    if progress_callback:
                        progress_callback(bytes_written, total_size)
        
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(self.thread_pool, _write_file)
    
    def process_file_in_chunks(self, file_path: Path, 
                              processor: Callable[[str], str]) -> str:
        """分块处理文件"""
        processed_parts = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            while True:
                chunk = f.read(self.chunk_size)
                if not chunk:
                    break
                
                processed_chunk = processor(chunk)
                processed_parts.append(processed_chunk)
        
        return ''.join(processed_parts)
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=False)


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.profiles = {}
        self.active_profiles = {}
    
    def start_profile(self, name: str):
        """开始性能分析"""
        self.active_profiles[name] = {
            'start_time': time.time(),
            'start_memory': psutil.Process().memory_info().rss
        }
    
    def end_profile(self, name: str) -> Dict[str, Any]:
        """结束性能分析"""
        if name not in self.active_profiles:
            raise ValueError(f"Profile {name} not started")
        
        start_info = self.active_profiles.pop(name)
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss
        
        profile_result = {
            'name': name,
            'duration': end_time - start_info['start_time'],
            'memory_delta': end_memory - start_info['start_memory'],
            'timestamp': datetime.now()
        }
        
        if name not in self.profiles:
            self.profiles[name] = []
        self.profiles[name].append(profile_result)
        
        return profile_result
    
    def get_profile_stats(self, name: str) -> Dict[str, Any]:
        """获取性能统计"""
        if name not in self.profiles:
            return {}
        
        profiles = self.profiles[name]
        durations = [p['duration'] for p in profiles]
        memory_deltas = [p['memory_delta'] for p in profiles]
        
        return {
            'count': len(profiles),
            'avg_duration': sum(durations) / len(durations),
            'min_duration': min(durations),
            'max_duration': max(durations),
            'avg_memory_delta': sum(memory_deltas) / len(memory_deltas),
            'total_memory_delta': sum(memory_deltas)
        }


def performance_monitor(func: Callable) -> Callable:
    """性能监控装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss
            
            duration = end_time - start_time
            memory_delta = end_memory - start_memory
            
            logger.info(
                f"Function {func.__name__} executed in {duration:.4f}s, "
                f"memory delta: {memory_delta / 1024 / 1024:.2f}MB"
            )
    
    return wrapper


def async_performance_monitor(func: Callable) -> Callable:
    """异步性能监控装饰器"""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss
        
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss
            
            duration = end_time - start_time
            memory_delta = end_memory - start_memory
            
            logger.info(
                f"Async function {func.__name__} executed in {duration:.4f}s, "
                f"memory delta: {memory_delta / 1024 / 1024:.2f}MB"
            )
    
    return wrapper


class ResourcePool:
    """资源池管理器"""
    
    def __init__(self, factory: Callable, max_size: int = 10, 
                 cleanup: Optional[Callable] = None):
        self.factory = factory
        self.cleanup = cleanup
        self.max_size = max_size
        self._pool = Queue(maxsize=max_size)
        self._created_count = 0
        self._lock = threading.Lock()
    
    def acquire(self):
        """获取资源"""
        try:
            # 尝试从池中获取
            resource = self._pool.get_nowait()
            return resource
        except:
            # 池为空，创建新资源
            with self._lock:
                if self._created_count < self.max_size:
                    resource = self.factory()
                    self._created_count += 1
                    return resource
                else:
                    # 等待资源释放
                    return self._pool.get()
    
    def release(self, resource):
        """释放资源"""
        try:
            self._pool.put_nowait(resource)
        except:
            # 池已满，清理资源
            if self.cleanup:
                self.cleanup(resource)
            with self._lock:
                self._created_count -= 1
    
    def close(self):
        """关闭资源池"""
        if self.cleanup:
            while not self._pool.empty():
                try:
                    resource = self._pool.get_nowait()
                    self.cleanup(resource)
                except:
                    break
        
        with self._lock:
            self._created_count = 0


class PerformanceManager:
    """性能管理器 - 统一管理所有性能相关功能"""
    
    def __init__(self):
        self.memory_monitor = MemoryMonitor()
        self.cache = SmartCache(max_size=1000, default_ttl=timedelta(hours=1))
        self.task_manager = AsyncTaskManager()
        self.file_processor = FileProcessor()
        self.profiler = PerformanceProfiler()
        self.metrics_history = deque(maxlen=10000)
        
        # 设置内存监控回调
        self.memory_monitor.add_callback('warning', self._on_memory_warning)
        self.memory_monitor.add_callback('critical', self._on_memory_critical)
    
    def start(self):
        """启动性能管理"""
        self.memory_monitor.start_monitoring()
        logger.info("性能管理器已启动")
    
    def stop(self):
        """停止性能管理"""
        self.memory_monitor.stop_monitoring()
        logger.info("性能管理器已停止")
    
    def _on_memory_warning(self, memory_percent: float):
        """内存警告处理"""
        logger.warning(f"内存使用率达到警告级别: {memory_percent:.1f}%")
        # 清理过期缓存
        expired_count = self.cache.cleanup_expired()
        if expired_count > 0:
            logger.info(f"清理了 {expired_count} 个过期缓存项")
    
    def _on_memory_critical(self, memory_percent: float):
        """内存临界处理"""
        logger.critical(f"内存使用率达到临界级别: {memory_percent:.1f}%")
        # 强制垃圾回收
        collected = self.memory_monitor.force_garbage_collection()
        # 清空部分缓存
        cache_stats = self.cache.get_stats()
        if cache_stats['size'] > 100:
            # 清空一半缓存
            keys_to_remove = list(self.cache._cache.keys())[:cache_stats['size'] // 2]
            for key in keys_to_remove:
                self.cache.delete(key)
            logger.info(f"清空了 {len(keys_to_remove)} 个缓存项")
    
    def collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        process = psutil.Process()
        
        # CPU和内存信息
        cpu_percent = process.cpu_percent()
        memory_info = process.memory_info()
        memory_percent = process.memory_percent()
        
        # IO信息
        try:
            io_counters = process.io_counters()
            disk_io_read = io_counters.read_bytes
            disk_io_write = io_counters.write_bytes
        except:
            disk_io_read = disk_io_write = 0
        
        # 网络信息
        try:
            net_io = psutil.net_io_counters()
            network_sent = net_io.bytes_sent
            network_recv = net_io.bytes_recv
        except:
            network_sent = network_recv = 0
        
        # 线程和文件信息
        active_threads = process.num_threads()
        try:
            open_files = len(process.open_files())
        except:
            open_files = 0
        
        metrics = PerformanceMetrics(
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_mb=memory_info.rss / 1024 / 1024,
            disk_io_read=disk_io_read,
            disk_io_write=disk_io_write,
            network_sent=network_sent,
            network_recv=network_recv,
            active_threads=active_threads,
            open_files=open_files
        )
        
        self.metrics_history.append(metrics)
        return metrics
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        current_metrics = self.collect_metrics()
        cache_stats = self.cache.get_stats()
        task_stats = self.task_manager.get_stats()
        memory_info = self.memory_monitor.get_memory_info()
        
        return {
            'timestamp': datetime.now().isoformat(),
            'current_metrics': current_metrics.to_dict(),
            'cache_stats': cache_stats,
            'task_stats': task_stats,
            'memory_info': memory_info,
            'metrics_history_count': len(self.metrics_history)
        }
    
    def optimize_performance(self):
        """执行性能优化"""
        logger.info("开始性能优化...")
        
        # 清理过期缓存
        expired_count = self.cache.cleanup_expired()
        
        # 强制垃圾回收
        collected = self.memory_monitor.force_garbage_collection()
        
        # 清理旧的性能指标
        if len(self.metrics_history) > 5000:
            # 保留最近的5000条记录
            self.metrics_history = deque(
                list(self.metrics_history)[-5000:],
                maxlen=10000
            )
        
        logger.info(
            f"性能优化完成: 清理 {expired_count} 个过期缓存, "
            f"回收 {collected} 个对象"
        )


# 全局性能管理器实例
performance_manager = PerformanceManager()


# 便捷函数
def start_performance_monitoring():
    """启动性能监控"""
    performance_manager.start()


def stop_performance_monitoring():
    """停止性能监控"""
    performance_manager.stop()


def get_cache() -> SmartCache:
    """获取缓存实例"""
    return performance_manager.cache


def get_task_manager() -> AsyncTaskManager:
    """获取任务管理器实例"""
    return performance_manager.task_manager


def get_performance_report() -> Dict[str, Any]:
    """获取性能报告"""
    return performance_manager.get_performance_report()


def optimize_performance():
    """执行性能优化"""
    performance_manager.optimize_performance()