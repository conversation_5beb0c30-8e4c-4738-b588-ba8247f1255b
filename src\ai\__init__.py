"""AI服务模块

提供AI服务的抽象接口和适配器管理功能。
支持多种AI模型的统一访问和管理。
"""

from .base_adapter import (
    AIModelType,
    AITaskType,
    AIResponseStatus,
    AIMessage,
    AIRequest,
    AIResponse,
    AIUsageStats,
    AIServiceError,
    AIRateLimitError,
    AIQuotaExceededError,
    AIModelError,
    AINetworkError,
    BaseAIAdapter,
    AIAdapterManager,
    ai_manager
)
from .openai_adapter import OpenAIAdapter, create_openai_adapter
from .claude_adapter import Claude<PERSON>dapter, create_claude_adapter
from .domestic_adapters import (
    ZhipuAIAdapter, WenxinAdapter, TongyiAdapter,
    create_zhipu_adapter, create_wenxin_adapter, create_tongyi_adapter
)

__version__ = "1.0.0"

__all__ = [
    "AIModelType",
    "AITaskType",
    "AIResponseStatus",
    "AIMessage",
    "AIRequest",
    "AIResponse",
    "AIUsageStats",
    "AIServiceError",
    "AIRateLimitError",
    "AIQuotaExceededError",
    "AIModelError",
    "AINetworkError",
    "BaseAIAdapter",
    "AIAdapterManager",
    "ai_manager",
    "OpenAIAdapter",
    "create_openai_adapter",
    "ClaudeAdapter",
    "create_claude_adapter",
    "ZhipuAIAdapter",
    "WenxinAdapter",
    "TongyiAdapter",
    "create_zhipu_adapter",
    "create_wenxin_adapter",
    "create_tongyi_adapter"
]