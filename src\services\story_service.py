"""场景和事件管理服务

提供完整的故事环境和情节管理功能，包括：
- 场景的CRUD操作和描述生成
- 事件的CRUD操作和时间线管理
- 情节逻辑检查和一致性验证
- 冲突设计和发展轨迹
- AI辅助场景描述和事件生成
- 故事结构分析和优化建议
"""

import json
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union, Tuple
from uuid import UUID
from collections import defaultdict, deque
from enum import Enum

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from fastapi import HTTPException, status
from pydantic import BaseModel, Field

from ..models.story_elements import (
    Scene, Event, SceneType, EventType,
    SceneCreateSchema, SceneUpdateSchema, SceneResponseSchema,
    EventCreateSchema, EventUpdateSchema, EventResponseSchema
)
from ..models.project import Project
from ..models.chapter import Chapter
from ..services.content_service import ContentService, ContentType, GenerationRequest
from ..core.database import get_db
from ..utils.logger import get_logger

logger = get_logger(__name__)

class ConflictType(str, Enum):
    """冲突类型枚举"""
    INTERNAL = "internal"  # 内在冲突
    INTERPERSONAL = "interpersonal"  # 人际冲突
    SOCIETAL = "societal"  # 社会冲突
    ENVIRONMENTAL = "environmental"  # 环境冲突
    SUPERNATURAL = "supernatural"  # 超自然冲突

class PlotStructure(str, Enum):
    """情节结构类型"""
    THREE_ACT = "three_act"  # 三幕结构
    HEROS_JOURNEY = "heros_journey"  # 英雄之旅
    FREYTAG_PYRAMID = "freytag_pyramid"  # 弗赖塔格金字塔
    SEVEN_POINT = "seven_point"  # 七点故事结构
    CUSTOM = "custom"  # 自定义结构

class Timeline(BaseModel):
    """时间线模型"""
    project_id: UUID = Field(..., description="项目ID")
    events: List[Dict[str, Any]] = Field(default_factory=list, description="事件列表")
    total_duration: int = Field(0, description="总持续时间（分钟）")
    structure_type: PlotStructure = Field(PlotStructure.THREE_ACT, description="结构类型")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

class ConflictDesign(BaseModel):
    """冲突设计模型"""
    project_id: UUID = Field(..., description="项目ID")
    conflict_type: ConflictType = Field(..., description="冲突类型")
    title: str = Field(..., description="冲突标题")
    description: str = Field(..., description="冲突描述")
    participants: List[UUID] = Field(default_factory=list, description="参与角色ID列表")
    trigger_event_id: Optional[UUID] = Field(None, description="触发事件ID")
    resolution_event_id: Optional[UUID] = Field(None, description="解决事件ID")
    intensity_curve: List[Dict[str, Any]] = Field(default_factory=list, description="强度变化曲线")
    stakes: str = Field(..., description="冲突赌注")
    obstacles: List[str] = Field(default_factory=list, description="障碍列表")
    resolution_method: Optional[str] = Field(None, description="解决方式")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

class SceneGenerationRequest(BaseModel):
    """场景生成请求"""
    project_id: UUID = Field(..., description="项目ID")
    scene_type: SceneType = Field(..., description="场景类型")
    name: Optional[str] = Field(None, description="场景名称")
    location: Optional[str] = Field(None, description="地理位置")
    time_period: Optional[str] = Field(None, description="时间设定")
    weather: Optional[str] = Field(None, description="天气条件")
    atmosphere: Optional[str] = Field(None, description="期望氛围")
    purpose: str = Field(..., description="场景用途")
    characters_present: List[UUID] = Field(default_factory=list, description="在场角色ID列表")
    mood: Optional[str] = Field(None, description="情绪基调")
    special_requirements: Optional[str] = Field(None, description="特殊要求")

class EventGenerationRequest(BaseModel):
    """事件生成请求"""
    project_id: UUID = Field(..., description="项目ID")
    event_type: EventType = Field(..., description="事件类型")
    title: Optional[str] = Field(None, description="事件标题")
    participants: List[UUID] = Field(default_factory=list, description="参与角色ID列表")
    scene_id: Optional[UUID] = Field(None, description="发生场景ID")
    conflict_level: int = Field(1, ge=1, le=10, description="冲突级别")
    emotional_intensity: int = Field(5, ge=1, le=10, description="情感强度")
    purpose: str = Field(..., description="事件目的")
    constraints: List[str] = Field(default_factory=list, description="约束条件")
    desired_outcome: Optional[str] = Field(None, description="期望结果")

class PlotAnalysis(BaseModel):
    """情节分析结果"""
    project_id: UUID = Field(..., description="项目ID")
    structure_score: float = Field(..., ge=0, le=100, description="结构评分")
    pacing_score: float = Field(..., ge=0, le=100, description="节奏评分")
    conflict_score: float = Field(..., ge=0, le=100, description="冲突评分")
    character_development_score: float = Field(..., ge=0, le=100, description="角色发展评分")
    overall_score: float = Field(..., ge=0, le=100, description="总体评分")
    strengths: List[str] = Field(default_factory=list, description="优势")
    weaknesses: List[str] = Field(default_factory=list, description="不足")
    suggestions: List[str] = Field(default_factory=list, description="改进建议")
    plot_holes: List[Dict[str, Any]] = Field(default_factory=list, description="情节漏洞")
    analyzed_at: datetime = Field(default_factory=datetime.utcnow, description="分析时间")

class StoryService:
    """场景和事件管理服务类"""
    
    def __init__(self, db: Session, content_service: ContentService = None):
        self.db = db
        self.content_service = content_service
    
    # 场景管理
    
    def create_scene(self, scene_data: SceneCreateSchema) -> SceneResponseSchema:
        """创建新场景"""
        try:
            # 验证项目存在
            project = self.db.query(Project).filter(
                and_(
                    Project.id == scene_data.project_id,
                    Project.deleted_at.is_(None)
                )
            ).first()
            
            if not project:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="项目不存在"
                )
            
            # 检查场景名称是否重复
            existing_scene = self.db.query(Scene).filter(
                and_(
                    Scene.project_id == scene_data.project_id,
                    Scene.name == scene_data.name,
                    Scene.deleted_at.is_(None)
                )
            ).first()
            
            if existing_scene:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="场景名称已存在"
                )
            
            # 创建场景
            scene = Scene(**scene_data.dict())
            self.db.add(scene)
            self.db.commit()
            self.db.refresh(scene)
            
            logger.info(f"Created scene: {scene.name} for project {scene_data.project_id}")
            return SceneResponseSchema.from_orm(scene)
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create scene: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建场景失败: {str(e)}"
            )
    
    def get_scene(self, scene_id: UUID) -> Optional[SceneResponseSchema]:
        """获取场景信息"""
        scene = self.db.query(Scene).filter(
            and_(
                Scene.id == scene_id,
                Scene.deleted_at.is_(None)
            )
        ).first()
        
        if scene:
            return SceneResponseSchema.from_orm(scene)
        return None
    
    def get_scenes_by_project(
        self,
        project_id: UUID,
        scene_type: Optional[SceneType] = None,
        importance_level: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[SceneResponseSchema]:
        """获取项目的场景列表"""
        query = self.db.query(Scene).filter(
            and_(
                Scene.project_id == project_id,
                Scene.deleted_at.is_(None)
            )
        )
        
        if scene_type:
            query = query.filter(Scene.scene_type == scene_type.value)
        
        if importance_level:
            query = query.filter(Scene.importance_level >= importance_level)
        
        scenes = query.order_by(
            desc(Scene.importance_level),
            asc(Scene.name)
        ).offset(skip).limit(limit).all()
        
        return [SceneResponseSchema.from_orm(scene) for scene in scenes]
    
    def update_scene(
        self,
        scene_id: UUID,
        scene_data: SceneUpdateSchema
    ) -> Optional[SceneResponseSchema]:
        """更新场景信息"""
        try:
            scene = self.db.query(Scene).filter(
                and_(
                    Scene.id == scene_id,
                    Scene.deleted_at.is_(None)
                )
            ).first()
            
            if not scene:
                return None
            
            # 检查名称重复（如果更新了名称）
            if scene_data.name and scene_data.name != scene.name:
                existing_scene = self.db.query(Scene).filter(
                    and_(
                        Scene.project_id == scene.project_id,
                        Scene.name == scene_data.name,
                        Scene.id != scene_id,
                        Scene.deleted_at.is_(None)
                    )
                ).first()
                
                if existing_scene:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="场景名称已存在"
                    )
            
            # 更新场景数据
            update_data = scene_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(scene, field, value)
            
            scene.updated_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(scene)
            
            logger.info(f"Updated scene: {scene.name}")
            return SceneResponseSchema.from_orm(scene)
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to update scene: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"更新场景失败: {str(e)}"
            )
    
    def delete_scene(self, scene_id: UUID) -> bool:
        """删除场景（软删除）"""
        try:
            scene = self.db.query(Scene).filter(
                and_(
                    Scene.id == scene_id,
                    Scene.deleted_at.is_(None)
                )
            ).first()
            
            if not scene:
                return False
            
            scene.deleted_at = datetime.utcnow()
            self.db.commit()
            
            logger.info(f"Deleted scene: {scene.name}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to delete scene: {str(e)}")
            return False
    
    def generate_scene_with_ai(self, request: SceneGenerationRequest) -> SceneResponseSchema:
        """使用AI生成场景"""
        if not self.content_service:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="AI服务不可用"
            )
        
        try:
            # 构建生成提示
            prompt = self._build_scene_generation_prompt(request)
            
            # 调用AI生成内容
            generation_request = GenerationRequest(
                content_type=ContentType.SCENE_DESCRIPTION,
                prompt=prompt,
                context={"request": request.dict()}
            )
            
            ai_response = self.content_service.generate_content(generation_request)
            
            # 解析AI响应为场景数据
            scene_data = self._parse_ai_scene_response(ai_response, request)
            
            # 创建场景
            scene = self.create_scene(scene_data)
            
            logger.info(f"Generated scene with AI: {scene.name}")
            return scene
            
        except Exception as e:
            logger.error(f"Failed to generate scene with AI: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"AI生成场景失败: {str(e)}"
            )
    
    def enhance_scene_description(
        self,
        scene_id: UUID,
        enhancement_type: str = "atmosphere"
    ) -> Optional[SceneResponseSchema]:
        """使用AI增强场景描述"""
        if not self.content_service:
            return None
        
        scene = self.get_scene(scene_id)
        if not scene:
            return None
        
        try:
            # 构建增强提示
            prompt = self._build_scene_enhancement_prompt(scene, enhancement_type)
            
            # 调用AI生成内容
            generation_request = GenerationRequest(
                content_type=ContentType.SCENE_DESCRIPTION,
                prompt=prompt,
                context={"scene": scene.dict()}
            )
            
            ai_response = self.content_service.generate_content(generation_request)
            
            # 更新场景信息
            update_data = self._parse_ai_scene_enhancement_response(ai_response, enhancement_type)
            
            if update_data:
                updated_scene = self.update_scene(
                    scene_id,
                    SceneUpdateSchema(**update_data)
                )
                
                logger.info(f"Enhanced scene with AI: {scene.name}")
                return updated_scene
            
            return scene
            
        except Exception as e:
            logger.error(f"Failed to enhance scene with AI: {str(e)}")
            return None
    
    # 事件管理
    
    def create_event(self, event_data: EventCreateSchema) -> EventResponseSchema:
        """创建新事件"""
        try:
            # 验证项目存在
            project = self.db.query(Project).filter(
                and_(
                    Project.id == event_data.project_id,
                    Project.deleted_at.is_(None)
                )
            ).first()
            
            if not project:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="项目不存在"
                )
            
            # 验证章节存在（如果指定）
            if event_data.chapter_id:
                chapter = self.db.query(Chapter).filter(
                    and_(
                        Chapter.id == event_data.chapter_id,
                        Chapter.deleted_at.is_(None)
                    )
                ).first()
                
                if not chapter:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="章节不存在"
                    )
            
            # 创建事件
            event = Event(**event_data.dict())
            self.db.add(event)
            self.db.commit()
            self.db.refresh(event)
            
            logger.info(f"Created event: {event.title} for project {event_data.project_id}")
            return EventResponseSchema.from_orm(event)
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create event: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建事件失败: {str(e)}"
            )
    
    def get_event(self, event_id: UUID) -> Optional[EventResponseSchema]:
        """获取事件信息"""
        event = self.db.query(Event).filter(
            and_(
                Event.id == event_id,
                Event.deleted_at.is_(None)
            )
        ).first()
        
        if event:
            return EventResponseSchema.from_orm(event)
        return None
    
    def get_events_by_project(
        self,
        project_id: UUID,
        event_type: Optional[EventType] = None,
        chapter_id: Optional[UUID] = None,
        importance_level: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[EventResponseSchema]:
        """获取项目的事件列表"""
        query = self.db.query(Event).filter(
            and_(
                Event.project_id == project_id,
                Event.deleted_at.is_(None)
            )
        )
        
        if event_type:
            query = query.filter(Event.event_type == event_type.value)
        
        if chapter_id:
            query = query.filter(Event.chapter_id == chapter_id)
        
        if importance_level:
            query = query.filter(Event.importance_level >= importance_level)
        
        events = query.order_by(
            asc(Event.sequence_order),
            desc(Event.importance_level)
        ).offset(skip).limit(limit).all()
        
        return [EventResponseSchema.from_orm(event) for event in events]
    
    def update_event(
        self,
        event_id: UUID,
        event_data: EventUpdateSchema
    ) -> Optional[EventResponseSchema]:
        """更新事件信息"""
        try:
            event = self.db.query(Event).filter(
                and_(
                    Event.id == event_id,
                    Event.deleted_at.is_(None)
                )
            ).first()
            
            if not event:
                return None
            
            # 验证章节存在（如果更新了章节）
            if event_data.chapter_id and event_data.chapter_id != event.chapter_id:
                chapter = self.db.query(Chapter).filter(
                    and_(
                        Chapter.id == event_data.chapter_id,
                        Chapter.deleted_at.is_(None)
                    )
                ).first()
                
                if not chapter:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="章节不存在"
                    )
            
            # 更新事件数据
            update_data = event_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(event, field, value)
            
            event.updated_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(event)
            
            logger.info(f"Updated event: {event.title}")
            return EventResponseSchema.from_orm(event)
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to update event: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"更新事件失败: {str(e)}"
            )
    
    def delete_event(self, event_id: UUID) -> bool:
        """删除事件（软删除）"""
        try:
            event = self.db.query(Event).filter(
                and_(
                    Event.id == event_id,
                    Event.deleted_at.is_(None)
                )
            ).first()
            
            if not event:
                return False
            
            event.deleted_at = datetime.utcnow()
            self.db.commit()
            
            logger.info(f"Deleted event: {event.title}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to delete event: {str(e)}")
            return False
    
    def generate_event_with_ai(self, request: EventGenerationRequest) -> EventResponseSchema:
        """使用AI生成事件"""
        if not self.content_service:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="AI服务不可用"
            )
        
        try:
            # 构建生成提示
            prompt = self._build_event_generation_prompt(request)
            
            # 调用AI生成内容
            generation_request = GenerationRequest(
                content_type=ContentType.PLOT_OUTLINE,
                prompt=prompt,
                context={"request": request.dict()}
            )
            
            ai_response = self.content_service.generate_content(generation_request)
            
            # 解析AI响应为事件数据
            event_data = self._parse_ai_event_response(ai_response, request)
            
            # 创建事件
            event = self.create_event(event_data)
            
            logger.info(f"Generated event with AI: {event.title}")
            return event
            
        except Exception as e:
            logger.error(f"Failed to generate event with AI: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"AI生成事件失败: {str(e)}"
            )
    
    # 时间线管理
    
    def create_timeline(self, project_id: UUID, structure_type: PlotStructure = PlotStructure.THREE_ACT) -> Timeline:
        """创建项目时间线"""
        try:
            # 获取项目的所有事件
            events = self.get_events_by_project(project_id, limit=1000)
            
            # 按时间顺序排序
            sorted_events = sorted(events, key=lambda x: x.sequence_order)
            
            # 构建时间线数据
            timeline_events = []
            total_duration = 0
            
            for event in sorted_events:
                event_data = {
                    "id": str(event.id),
                    "title": event.title,
                    "type": event.event_type.value,
                    "sequence_order": event.sequence_order,
                    "duration_minutes": event.duration_minutes or 0,
                    "importance_level": event.importance_level,
                    "emotional_intensity": event.emotional_intensity,
                    "conflict_level": event.conflict_level
                }
                timeline_events.append(event_data)
                total_duration += event.duration_minutes or 0
            
            timeline = Timeline(
                project_id=project_id,
                events=timeline_events,
                total_duration=total_duration,
                structure_type=structure_type
            )
            
            # 保存时间线
            self._save_timeline(timeline)
            
            logger.info(f"Created timeline for project {project_id}")
            return timeline
            
        except Exception as e:
            logger.error(f"Failed to create timeline: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建时间线失败: {str(e)}"
            )
    
    def get_timeline(self, project_id: UUID) -> Optional[Timeline]:
        """获取项目时间线"""
        return self._load_timeline(project_id)
    
    def update_timeline(self, project_id: UUID) -> Optional[Timeline]:
        """更新项目时间线"""
        timeline = self.get_timeline(project_id)
        if timeline:
            # 重新创建时间线
            return self.create_timeline(project_id, timeline.structure_type)
        return None
    
    def reorder_events(self, project_id: UUID, event_orders: List[Tuple[UUID, int]]) -> bool:
        """重新排序事件"""
        try:
            for event_id, new_order in event_orders:
                event = self.db.query(Event).filter(
                    and_(
                        Event.id == event_id,
                        Event.project_id == project_id,
                        Event.deleted_at.is_(None)
                    )
                ).first()
                
                if event:
                    event.sequence_order = new_order
                    event.updated_at = datetime.utcnow()
            
            self.db.commit()
            
            # 更新时间线
            self.update_timeline(project_id)
            
            logger.info(f"Reordered events for project {project_id}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to reorder events: {str(e)}")
            return False
    
    # 冲突设计
    
    def create_conflict(
        self,
        project_id: UUID,
        conflict_data: Dict[str, Any]
    ) -> ConflictDesign:
        """创建冲突设计"""
        try:
            conflict = ConflictDesign(
                project_id=project_id,
                **conflict_data
            )
            
            # 保存冲突设计
            self._save_conflict_design(conflict)
            
            logger.info(f"Created conflict: {conflict.title} for project {project_id}")
            return conflict
            
        except Exception as e:
            logger.error(f"Failed to create conflict: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建冲突设计失败: {str(e)}"
            )
    
    def get_conflicts_by_project(self, project_id: UUID) -> List[ConflictDesign]:
        """获取项目的冲突设计列表"""
        return self._load_conflict_designs(project_id)
    
    def update_conflict(
        self,
        project_id: UUID,
        conflict_title: str,
        update_data: Dict[str, Any]
    ) -> Optional[ConflictDesign]:
        """更新冲突设计"""
        conflicts = self.get_conflicts_by_project(project_id)
        
        for conflict in conflicts:
            if conflict.title == conflict_title:
                # 更新冲突数据
                for key, value in update_data.items():
                    if hasattr(conflict, key):
                        setattr(conflict, key, value)
                
                conflict.updated_at = datetime.utcnow()
                
                # 保存更新
                self._save_conflict_design(conflict)
                
                logger.info(f"Updated conflict: {conflict.title}")
                return conflict
        
        return None
    
    def analyze_conflict_development(self, project_id: UUID) -> Dict[str, Any]:
        """分析冲突发展"""
        conflicts = self.get_conflicts_by_project(project_id)
        events = self.get_events_by_project(project_id, limit=1000)
        
        analysis = {
            "total_conflicts": len(conflicts),
            "conflict_types": defaultdict(int),
            "resolution_rate": 0,
            "intensity_distribution": defaultdict(int),
            "development_stages": []
        }
        
        resolved_conflicts = 0
        
        for conflict in conflicts:
            analysis["conflict_types"][conflict.conflict_type.value] += 1
            
            if conflict.resolution_event_id:
                resolved_conflicts += 1
            
            # 分析强度分布
            for point in conflict.intensity_curve:
                intensity = point.get("intensity", 0)
                analysis["intensity_distribution"][f"{intensity//2*2}-{intensity//2*2+1}"] += 1
        
        if conflicts:
            analysis["resolution_rate"] = (resolved_conflicts / len(conflicts)) * 100
        
        return dict(analysis)
    
    # 情节逻辑检查
    
    def check_plot_logic(self, project_id: UUID) -> PlotAnalysis:
        """检查情节逻辑"""
        try:
            events = self.get_events_by_project(project_id, limit=1000)
            timeline = self.get_timeline(project_id)
            conflicts = self.get_conflicts_by_project(project_id)
            
            analysis = PlotAnalysis(project_id=project_id)
            
            # 结构评分
            analysis.structure_score = self._analyze_plot_structure(events, timeline)
            
            # 节奏评分
            analysis.pacing_score = self._analyze_pacing(events)
            
            # 冲突评分
            analysis.conflict_score = self._analyze_conflicts(conflicts, events)
            
            # 角色发展评分
            analysis.character_development_score = self._analyze_character_development(events)
            
            # 总体评分
            analysis.overall_score = (
                analysis.structure_score * 0.3 +
                analysis.pacing_score * 0.25 +
                analysis.conflict_score * 0.25 +
                analysis.character_development_score * 0.2
            )
            
            # 识别优势和不足
            analysis.strengths, analysis.weaknesses = self._identify_strengths_weaknesses(analysis)
            
            # 生成改进建议
            analysis.suggestions = self._generate_improvement_suggestions(analysis, events)
            
            # 检查情节漏洞
            analysis.plot_holes = self._detect_plot_holes(events)
            
            logger.info(f"Analyzed plot logic for project {project_id}, score: {analysis.overall_score}")
            return analysis
            
        except Exception as e:
            logger.error(f"Failed to check plot logic: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"检查情节逻辑失败: {str(e)}"
            )
    
    def validate_event_sequence(self, project_id: UUID) -> List[Dict[str, Any]]:
        """验证事件序列的逻辑性"""
        events = self.get_events_by_project(project_id, limit=1000)
        issues = []
        
        # 按时间顺序排序
        sorted_events = sorted(events, key=lambda x: x.sequence_order)
        
        for i, event in enumerate(sorted_events):
            # 检查前置事件
            if event.prerequisite_events:
                for prereq_id in event.prerequisite_events:
                    prereq_event = next((e for e in sorted_events if str(e.id) == str(prereq_id)), None)
                    if prereq_event and prereq_event.sequence_order >= event.sequence_order:
                        issues.append({
                            "type": "sequence_error",
                            "event_id": str(event.id),
                            "event_title": event.title,
                            "message": f"前置事件 '{prereq_event.title}' 的时间顺序晚于当前事件",
                            "severity": "high"
                        })
            
            # 检查后续事件
            if event.consequence_events:
                for conseq_id in event.consequence_events:
                    conseq_event = next((e for e in sorted_events if str(e.id) == str(conseq_id)), None)
                    if conseq_event and conseq_event.sequence_order <= event.sequence_order:
                        issues.append({
                            "type": "sequence_error",
                            "event_id": str(event.id),
                            "event_title": event.title,
                            "message": f"后续事件 '{conseq_event.title}' 的时间顺序早于当前事件",
                            "severity": "high"
                        })
        
        return issues
    
    # 统计和分析
    
    def get_story_statistics(self, project_id: UUID) -> Dict[str, Any]:
        """获取故事统计信息"""
        scenes = self.get_scenes_by_project(project_id, limit=1000)
        events = self.get_events_by_project(project_id, limit=1000)
        timeline = self.get_timeline(project_id)
        
        # 场景统计
        scene_stats = {
            "total_scenes": len(scenes),
            "by_type": defaultdict(int),
            "by_importance": defaultdict(int),
            "average_importance": 0
        }
        
        if scenes:
            for scene in scenes:
                scene_stats["by_type"][scene.scene_type.value] += 1
                scene_stats["by_importance"][scene.importance_level] += 1
            
            scene_stats["average_importance"] = sum(s.importance_level for s in scenes) / len(scenes)
        
        # 事件统计
        event_stats = {
            "total_events": len(events),
            "by_type": defaultdict(int),
            "by_importance": defaultdict(int),
            "average_importance": 0,
            "average_conflict_level": 0,
            "average_emotional_intensity": 0
        }
        
        if events:
            for event in events:
                event_stats["by_type"][event.event_type.value] += 1
                event_stats["by_importance"][event.importance_level] += 1
            
            event_stats["average_importance"] = sum(e.importance_level for e in events) / len(events)
            event_stats["average_conflict_level"] = sum(e.conflict_level for e in events) / len(events)
            event_stats["average_emotional_intensity"] = sum(e.emotional_intensity for e in events) / len(events)
        
        return {
            "scenes": dict(scene_stats),
            "events": dict(event_stats),
            "timeline": {
                "total_duration": timeline.total_duration if timeline else 0,
                "structure_type": timeline.structure_type.value if timeline else None,
                "event_count": len(timeline.events) if timeline else 0
            }
        }
    
    # 私有辅助方法
    
    def _build_scene_generation_prompt(self, request: SceneGenerationRequest) -> str:
        """构建场景生成提示"""
        prompt = f"""请为小说创建一个{request.scene_type.value}场景：

场景用途：{request.purpose}
"""
        
        if request.name:
            prompt += f"场景名称：{request.name}\n"
        
        if request.location:
            prompt += f"地理位置：{request.location}\n"
        
        if request.time_period:
            prompt += f"时间设定：{request.time_period}\n"
        
        if request.weather:
            prompt += f"天气条件：{request.weather}\n"
        
        if request.atmosphere:
            prompt += f"期望氛围：{request.atmosphere}\n"
        
        if request.mood:
            prompt += f"情绪基调：{request.mood}\n"
        
        if request.special_requirements:
            prompt += f"特殊要求：{request.special_requirements}\n"
        
        prompt += "\n请提供详细的场景描述，包括环境细节、氛围营造和视觉效果。"
        
        return prompt
    
    def _parse_ai_scene_response(self, ai_response: str, request: SceneGenerationRequest) -> SceneCreateSchema:
        """解析AI场景生成响应"""
        # 这里需要实现AI响应的解析逻辑
        # 暂时返回一个基本的场景数据
        return SceneCreateSchema(
            project_id=request.project_id,
            name=request.name or "AI生成场景",
            scene_type=request.scene_type,
            location=request.location,
            time_period=request.time_period,
            weather=request.weather,
            description="AI生成的场景描述",
            atmosphere=request.atmosphere or "AI生成的氛围描述"
        )
    
    def _build_scene_enhancement_prompt(self, scene: SceneResponseSchema, enhancement_type: str) -> str:
        """构建场景增强提示"""
        if enhancement_type == "atmosphere":
            return f"""请为以下场景增强氛围描述：
场景名称：{scene.name}
场景类型：{scene.scene_type.value}
当前描述：{scene.description or '无'}
当前氛围：{scene.atmosphere or '无'}
请提供更丰富的氛围描述。"""
        elif enhancement_type == "details":
            return f"""请为以下场景添加环境细节：
场景名称：{scene.name}
地理位置：{scene.location or '无'}
当前描述：{scene.description or '无'}
请提供更详细的环境描述。"""
        else:
            return f"""请优化以下场景信息：
场景名称：{scene.name}
描述：{scene.description or '无'}
氛围：{scene.atmosphere or '无'}
请提供改进建议。"""
    
    def _parse_ai_scene_enhancement_response(self, ai_response: str, enhancement_type: str) -> Dict[str, Any]:
        """解析AI场景增强响应"""
        # 这里需要实现AI响应的解析逻辑
        if enhancement_type == "atmosphere":
            return {"atmosphere": ai_response}
        elif enhancement_type == "details":
            return {"environment_details": ai_response}
        else:
            return {}
    
    def _build_event_generation_prompt(self, request: EventGenerationRequest) -> str:
        """构建事件生成提示"""
        prompt = f"""请为小说创建一个{request.event_type.value}事件：

事件目的：{request.purpose}
冲突级别：{request.conflict_level}/10
情感强度：{request.emotional_intensity}/10
"""
        
        if request.title:
            prompt += f"事件标题：{request.title}\n"
        
        if request.constraints:
            prompt += f"约束条件：{', '.join(request.constraints)}\n"
        
        if request.desired_outcome:
            prompt += f"期望结果：{request.desired_outcome}\n"
        
        prompt += "\n请提供详细的事件描述，包括发生过程、参与者行为和事件结果。"
        
        return prompt
    
    def _parse_ai_event_response(self, ai_response: str, request: EventGenerationRequest) -> EventCreateSchema:
        """解析AI事件生成响应"""
        # 这里需要实现AI响应的解析逻辑
        # 暂时返回一个基本的事件数据
        return EventCreateSchema(
            project_id=request.project_id,
            title=request.title or "AI生成事件",
            event_type=request.event_type,
            description="AI生成的事件描述",
            conflict_level=request.conflict_level,
            emotional_intensity=request.emotional_intensity
        )
    
    def _save_timeline(self, timeline: Timeline):
        """保存时间线到数据库"""
        # 这里可以将时间线保存到项目的扩展信息中
        project = self.db.query(Project).filter(Project.id == timeline.project_id).first()
        if project:
            if not hasattr(project, 'timeline_data'):
                project.timeline_data = {}
            
            project.timeline_data = timeline.dict()
            project.updated_at = datetime.utcnow()
            self.db.commit()
    
    def _load_timeline(self, project_id: UUID) -> Optional[Timeline]:
        """从数据库加载时间线"""
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project or not hasattr(project, 'timeline_data') or not project.timeline_data:
            return None
        
        try:
            timeline_data = project.timeline_data
            # 转换时间字符串为datetime对象
            if isinstance(timeline_data.get("created_at"), str):
                timeline_data["created_at"] = datetime.fromisoformat(timeline_data["created_at"])
            if isinstance(timeline_data.get("updated_at"), str):
                timeline_data["updated_at"] = datetime.fromisoformat(timeline_data["updated_at"])
            
            return Timeline(**timeline_data)
        except Exception as e:
            logger.warning(f"Failed to parse timeline data: {str(e)}")
            return None
    
    def _save_conflict_design(self, conflict: ConflictDesign):
        """保存冲突设计"""
        project = self.db.query(Project).filter(Project.id == conflict.project_id).first()
        if not project:
            return
        
        # 获取现有的冲突数据
        conflicts_data = getattr(project, 'conflicts_data', None) or {}
        conflicts_list = conflicts_data.get('conflicts', [])
        
        # 更新或添加冲突
        found = False
        for i, conflict_data in enumerate(conflicts_list):
            if conflict_data.get('title') == conflict.title:
                conflicts_list[i] = conflict.dict()
                found = True
                break
        
        if not found:
            conflicts_list.append(conflict.dict())
        
        conflicts_data['conflicts'] = conflicts_list
        project.conflicts_data = conflicts_data
        project.updated_at = datetime.utcnow()
        
        self.db.commit()
    
    def _load_conflict_designs(self, project_id: UUID) -> List[ConflictDesign]:
        """加载冲突设计"""
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project or not hasattr(project, 'conflicts_data') or not project.conflicts_data:
            return []
        
        conflicts_data = project.conflicts_data.get('conflicts', [])
        conflicts = []
        
        for conflict_data in conflicts_data:
            try:
                # 转换时间字符串为datetime对象
                if isinstance(conflict_data.get("created_at"), str):
                    conflict_data["created_at"] = datetime.fromisoformat(conflict_data["created_at"])
                if isinstance(conflict_data.get("updated_at"), str):
                    conflict_data["updated_at"] = datetime.fromisoformat(conflict_data["updated_at"])
                
                conflicts.append(ConflictDesign(**conflict_data))
            except Exception as e:
                logger.warning(f"Failed to parse conflict data: {str(e)}")
                continue
        
        return conflicts
    
    def _analyze_plot_structure(self, events: List[EventResponseSchema], timeline: Optional[Timeline]) -> float:
        """分析情节结构"""
        if not events:
            return 0.0
        
        score = 50.0  # 基础分
        
        # 检查是否有开头、发展、高潮、结局
        event_types = [event.event_type.value for event in events]
        
        if "opening" in event_types:
            score += 10
        if "climax" in event_types:
            score += 15
        if "resolution" in event_types:
            score += 10
        
        # 检查事件分布
        if len(events) >= 5:
            score += 10
        
        # 检查时间线结构
        if timeline and timeline.structure_type != PlotStructure.CUSTOM:
            score += 5
        
        return min(100.0, score)
    
    def _analyze_pacing(self, events: List[EventResponseSchema]) -> float:
        """分析节奏"""
        if not events:
            return 0.0
        
        score = 50.0  # 基础分
        
        # 检查冲突级别变化
        conflict_levels = [event.conflict_level for event in events]
        if len(set(conflict_levels)) > 1:  # 有变化
            score += 15
        
        # 检查情感强度变化
        emotional_levels = [event.emotional_intensity for event in events]
        if len(set(emotional_levels)) > 1:  # 有变化
            score += 15
        
        # 检查事件重要性分布
        importance_levels = [event.importance_level for event in events]
        if max(importance_levels) - min(importance_levels) >= 3:  # 有明显差异
            score += 10
        
        # 检查持续时间分布
        durations = [event.duration_minutes or 0 for event in events]
        if len(set(durations)) > 1:  # 有变化
            score += 10
        
        return min(100.0, score)
    
    def _analyze_conflicts(self, conflicts: List[ConflictDesign], events: List[EventResponseSchema]) -> float:
        """分析冲突"""
        if not conflicts:
            return 30.0  # 没有冲突设计，给基础分
        
        score = 40.0  # 基础分
        
        # 检查冲突类型多样性
        conflict_types = set(conflict.conflict_type.value for conflict in conflicts)
        score += len(conflict_types) * 10
        
        # 检查冲突解决率
        resolved_conflicts = sum(1 for conflict in conflicts if conflict.resolution_event_id)
        if conflicts:
            resolution_rate = resolved_conflicts / len(conflicts)
            score += resolution_rate * 20
        
        # 检查冲突与事件的关联
        event_ids = set(str(event.id) for event in events)
        linked_conflicts = sum(
            1 for conflict in conflicts
            if (conflict.trigger_event_id and str(conflict.trigger_event_id) in event_ids) or
               (conflict.resolution_event_id and str(conflict.resolution_event_id) in event_ids)
        )
        
        if conflicts:
            link_rate = linked_conflicts / len(conflicts)
            score += link_rate * 20
        
        return min(100.0, score)
    
    def _analyze_character_development(self, events: List[EventResponseSchema]) -> float:
        """分析角色发展"""
        if not events:
            return 0.0
        
        score = 50.0  # 基础分
        
        # 检查角色相关事件
        character_events = [event for event in events if event.event_type.value in ["character_development", "dialogue"]]
        if character_events:
            score += min(30, len(character_events) * 5)
        
        # 检查情感强度变化（反映角色成长）
        emotional_progression = [event.emotional_intensity for event in events]
        if len(emotional_progression) > 1:
            # 简单检查是否有情感起伏
            has_progression = any(
                abs(emotional_progression[i] - emotional_progression[i-1]) >= 2
                for i in range(1, len(emotional_progression))
            )
            if has_progression:
                score += 20
        
        return min(100.0, score)
    
    def _identify_strengths_weaknesses(self, analysis: PlotAnalysis) -> Tuple[List[str], List[str]]:
        """识别优势和不足"""
        strengths = []
        weaknesses = []
        
        if analysis.structure_score >= 80:
            strengths.append("故事结构完整，层次分明")
        elif analysis.structure_score < 60:
            weaknesses.append("故事结构需要完善，缺少关键情节点")
        
        if analysis.pacing_score >= 80:
            strengths.append("节奏控制良好，张弛有度")
        elif analysis.pacing_score < 60:
            weaknesses.append("节奏单调，需要增加变化")
        
        if analysis.conflict_score >= 80:
            strengths.append("冲突设计精彩，推动力强")
        elif analysis.conflict_score < 60:
            weaknesses.append("冲突设计不足，缺乏戏剧张力")
        
        if analysis.character_development_score >= 80:
            strengths.append("角色发展充分，成长轨迹清晰")
        elif analysis.character_development_score < 60:
            weaknesses.append("角色发展不足，需要更多成长情节")
        
        return strengths, weaknesses
    
    def _generate_improvement_suggestions(self, analysis: PlotAnalysis, events: List[EventResponseSchema]) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        if analysis.structure_score < 70:
            suggestions.append("建议添加明确的开头、发展、高潮、结局事件")
        
        if analysis.pacing_score < 70:
            suggestions.append("建议调整事件的冲突级别和情感强度，创造更好的节奏变化")
        
        if analysis.conflict_score < 70:
            suggestions.append("建议设计更多样化的冲突类型，并确保冲突有明确的解决方案")
        
        if analysis.character_development_score < 70:
            suggestions.append("建议增加更多角色发展相关的事件和对话")
        
        if len(events) < 5:
            suggestions.append("建议增加更多事件来丰富故事内容")
        
        return suggestions
    
    def _detect_plot_holes(self, events: List[EventResponseSchema]) -> List[Dict[str, Any]]:
        """检测情节漏洞"""
        plot_holes = []
        
        # 检查前置事件缺失
        event_ids = set(str(event.id) for event in events)
        
        for event in events:
            if event.prerequisite_events:
                for prereq_id in event.prerequisite_events:
                    if str(prereq_id) not in event_ids:
                        plot_holes.append({
                            "type": "missing_prerequisite",
                            "event_id": str(event.id),
                            "event_title": event.title,
                            "message": f"前置事件 {prereq_id} 不存在",
                            "severity": "medium"
                        })
        
        # 检查逻辑矛盾
        # 这里可以添加更多复杂的逻辑检查
        
        return plot_holes

# 服务实例获取函数
def get_story_service(db: Session = None, content_service: ContentService = None) -> StoryService:
    """获取故事服务实例"""
    if db is None:
        db = next(get_db())
    return StoryService(db, content_service)

# 导出的类和函数
__all__ = [
    "ConflictType",
    "PlotStructure",
    "Timeline",
    "ConflictDesign",
    "SceneGenerationRequest",
    "EventGenerationRequest",
    "PlotAnalysis",
    "StoryService",
    "get_story_service"
]