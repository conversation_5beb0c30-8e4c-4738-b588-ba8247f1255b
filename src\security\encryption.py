#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
加密和数据保护模块

提供API密钥加密存储、数据备份加密、数据隐私保护等安全功能。
"""

import os
import json
import base64
import hashlib
import secrets
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum

try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography.hazmat.backends import default_backend
except ImportError:
    print("Warning: cryptography library not installed. Install with: pip install cryptography")
    Fernet = None

try:
    import keyring
except ImportError:
    print("Warning: keyring library not installed. Install with: pip install keyring")
    keyring = None

from ..utils.logger import LoggerManager
from ..utils.error_handler import ErrorHandler


class EncryptionLevel(Enum):
    """加密级别"""
    NONE = "none"          # 无加密
    BASIC = "basic"        # 基础加密
    STANDARD = "standard"  # 标准加密
    HIGH = "high"          # 高级加密
    MILITARY = "military"  # 军用级加密


class DataType(Enum):
    """数据类型"""
    API_KEY = "api_key"
    USER_DATA = "user_data"
    PROJECT_DATA = "project_data"
    BACKUP_DATA = "backup_data"
    CACHE_DATA = "cache_data"
    LOG_DATA = "log_data"


@dataclass
class EncryptionConfig:
    """加密配置"""
    level: EncryptionLevel = EncryptionLevel.STANDARD
    key_derivation_iterations: int = 100000
    salt_length: int = 32
    use_hardware_security: bool = True
    auto_key_rotation: bool = True
    key_rotation_days: int = 90
    backup_encryption: bool = True
    compress_before_encrypt: bool = True


@dataclass
class EncryptedData:
    """加密数据结构"""
    data: str  # Base64编码的加密数据
    salt: str  # Base64编码的盐值
    iv: str    # Base64编码的初始化向量
    algorithm: str  # 加密算法
    created_at: datetime
    expires_at: Optional[datetime] = None
    metadata: Dict[str, Any] = None


class KeyManager:
    """密钥管理器"""
    
    def __init__(self, config: EncryptionConfig):
        self.config = config
        self.logger = LoggerManager().get_logger("security.key_manager")
        self.error_handler = ErrorHandler()
        self._master_key: Optional[bytes] = None
        self._key_cache: Dict[str, bytes] = {}
        self._key_rotation_schedule: Dict[str, datetime] = {}
    
    def generate_master_key(self, password: str, salt: Optional[bytes] = None) -> bytes:
        """生成主密钥"""
        try:
            if salt is None:
                salt = os.urandom(self.config.salt_length)
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=self.config.key_derivation_iterations,
                backend=default_backend()
            )
            
            key = kdf.derive(password.encode())
            self._master_key = key
            
            self.logger.info("主密钥生成成功")
            return key
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "generate_master_key",
                "salt_length": len(salt) if salt else 0
            })
            raise
    
    def derive_key(self, purpose: str, master_key: Optional[bytes] = None) -> bytes:
        """派生专用密钥"""
        try:
            if master_key is None:
                master_key = self._master_key
            
            if master_key is None:
                raise ValueError("主密钥未设置")
            
            # 使用HKDF派生专用密钥
            info = purpose.encode()
            salt = hashlib.sha256(info).digest()
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=10000,  # 较少的迭代次数用于派生
                backend=default_backend()
            )
            
            derived_key = kdf.derive(master_key)
            
            # 缓存密钥
            self._key_cache[purpose] = derived_key
            
            # 设置密钥轮换计划
            if self.config.auto_key_rotation:
                self._key_rotation_schedule[purpose] = (
                    datetime.now() + timedelta(days=self.config.key_rotation_days)
                )
            
            return derived_key
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "derive_key",
                "purpose": purpose
            })
            raise
    
    def get_key(self, purpose: str) -> Optional[bytes]:
        """获取密钥"""
        # 检查是否需要轮换
        if self.config.auto_key_rotation and purpose in self._key_rotation_schedule:
            if datetime.now() > self._key_rotation_schedule[purpose]:
                self.logger.info(f"密钥需要轮换: {purpose}")
                return self.derive_key(purpose)
        
        return self._key_cache.get(purpose)
    
    def rotate_key(self, purpose: str) -> bytes:
        """轮换密钥"""
        try:
            # 生成新的主密钥组件
            new_component = os.urandom(16)
            combined_master = self._master_key + new_component
            
            # 派生新密钥
            new_key = self.derive_key(purpose, combined_master)
            
            self.logger.info(f"密钥轮换完成: {purpose}")
            return new_key
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "rotate_key",
                "purpose": purpose
            })
            raise
    
    def clear_cache(self):
        """清除密钥缓存"""
        self._key_cache.clear()
        self._key_rotation_schedule.clear()
        self.logger.info("密钥缓存已清除")


class DataEncryption:
    """数据加密器"""
    
    def __init__(self, key_manager: KeyManager, config: EncryptionConfig):
        self.key_manager = key_manager
        self.config = config
        self.logger = LoggerManager().get_logger("security.encryption")
        self.error_handler = ErrorHandler()
    
    def encrypt_data(self, data: Union[str, bytes, Dict], 
                    data_type: DataType,
                    expires_in_days: Optional[int] = None) -> EncryptedData:
        """加密数据"""
        try:
            # 准备数据
            if isinstance(data, dict):
                data_bytes = json.dumps(data, ensure_ascii=False).encode('utf-8')
            elif isinstance(data, str):
                data_bytes = data.encode('utf-8')
            else:
                data_bytes = data
            
            # 压缩数据（如果启用）
            if self.config.compress_before_encrypt:
                import gzip
                data_bytes = gzip.compress(data_bytes)
            
            # 获取加密密钥
            key = self.key_manager.get_key(data_type.value)
            if key is None:
                key = self.key_manager.derive_key(data_type.value)
            
            # 创建Fernet实例
            fernet_key = base64.urlsafe_b64encode(key)
            fernet = Fernet(fernet_key)
            
            # 加密数据
            encrypted_bytes = fernet.encrypt(data_bytes)
            
            # 创建加密数据结构
            encrypted_data = EncryptedData(
                data=base64.b64encode(encrypted_bytes).decode('ascii'),
                salt=base64.b64encode(os.urandom(16)).decode('ascii'),
                iv=base64.b64encode(os.urandom(16)).decode('ascii'),
                algorithm="Fernet-AES256",
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(days=expires_in_days) if expires_in_days else None,
                metadata={
                    "data_type": data_type.value,
                    "encryption_level": self.config.level.value,
                    "compressed": self.config.compress_before_encrypt
                }
            )
            
            self.logger.info(f"数据加密成功: {data_type.value}")
            return encrypted_data
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "encrypt_data",
                "data_type": data_type.value,
                "data_size": len(str(data))
            })
            raise
    
    def decrypt_data(self, encrypted_data: EncryptedData, 
                    expected_type: Optional[type] = None) -> Union[str, bytes, Dict]:
        """解密数据"""
        try:
            # 检查数据是否过期
            if encrypted_data.expires_at and datetime.now() > encrypted_data.expires_at:
                raise ValueError("加密数据已过期")
            
            # 获取数据类型
            data_type = DataType(encrypted_data.metadata.get("data_type", "user_data"))
            
            # 获取解密密钥
            key = self.key_manager.get_key(data_type.value)
            if key is None:
                raise ValueError(f"无法获取解密密钥: {data_type.value}")
            
            # 创建Fernet实例
            fernet_key = base64.urlsafe_b64encode(key)
            fernet = Fernet(fernet_key)
            
            # 解密数据
            encrypted_bytes = base64.b64decode(encrypted_data.data.encode('ascii'))
            decrypted_bytes = fernet.decrypt(encrypted_bytes)
            
            # 解压缩数据（如果需要）
            if encrypted_data.metadata.get("compressed", False):
                import gzip
                decrypted_bytes = gzip.decompress(decrypted_bytes)
            
            # 根据期望类型返回数据
            if expected_type == dict:
                return json.loads(decrypted_bytes.decode('utf-8'))
            elif expected_type == str:
                return decrypted_bytes.decode('utf-8')
            elif expected_type == bytes:
                return decrypted_bytes
            else:
                # 尝试自动检测类型
                try:
                    text = decrypted_bytes.decode('utf-8')
                    # 尝试解析为JSON
                    try:
                        return json.loads(text)
                    except json.JSONDecodeError:
                        return text
                except UnicodeDecodeError:
                    return decrypted_bytes
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "decrypt_data",
                "algorithm": encrypted_data.algorithm,
                "created_at": encrypted_data.created_at.isoformat()
            })
            raise


class SecureStorage:
    """安全存储管理器"""
    
    def __init__(self, storage_path: Path, config: EncryptionConfig):
        self.storage_path = storage_path
        self.config = config
        self.logger = LoggerManager().get_logger("security.storage")
        self.error_handler = ErrorHandler()
        
        # 确保存储目录存在
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化密钥管理器和加密器
        self.key_manager = KeyManager(config)
        self.encryptor = DataEncryption(self.key_manager, config)
        
        # 初始化主密钥
        self._initialize_master_key()
    
    def _initialize_master_key(self):
        """初始化主密钥"""
        try:
            # 尝试从系统密钥环获取密钥
            if keyring and self.config.use_hardware_security:
                stored_key = keyring.get_password("bamboofall_ai", "master_key")
                if stored_key:
                    self.key_manager._master_key = base64.b64decode(stored_key)
                    self.logger.info("从系统密钥环加载主密钥")
                    return
            
            # 生成新的主密钥
            password = self._generate_device_password()
            salt = self._get_or_create_salt()
            self.key_manager.generate_master_key(password, salt)
            
            # 保存到系统密钥环
            if keyring and self.config.use_hardware_security:
                key_b64 = base64.b64encode(self.key_manager._master_key).decode()
                keyring.set_password("bamboofall_ai", "master_key", key_b64)
                self.logger.info("主密钥已保存到系统密钥环")
            
        except Exception as e:
            self.error_handler.handle_error(e, {"action": "initialize_master_key"})
            # 使用默认密码作为后备方案
            self.key_manager.generate_master_key("default_password_change_me")
    
    def _generate_device_password(self) -> str:
        """生成设备相关的密码"""
        import platform
        import uuid
        
        # 收集设备信息
        device_info = {
            "platform": platform.platform(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "node": platform.node(),
            "mac": str(uuid.getnode())
        }
        
        # 生成基于设备的密码
        device_string = json.dumps(device_info, sort_keys=True)
        password_hash = hashlib.sha256(device_string.encode()).hexdigest()
        
        return password_hash
    
    def _get_or_create_salt(self) -> bytes:
        """获取或创建盐值"""
        salt_file = self.storage_path / ".salt"
        
        if salt_file.exists():
            return salt_file.read_bytes()
        else:
            salt = os.urandom(self.config.salt_length)
            salt_file.write_bytes(salt)
            # 设置文件权限（仅所有者可读写）
            salt_file.chmod(0o600)
            return salt
    
    def store_api_key(self, service: str, api_key: str, 
                     expires_in_days: Optional[int] = None) -> bool:
        """存储API密钥"""
        try:
            # 加密API密钥
            encrypted_data = self.encryptor.encrypt_data(
                api_key, DataType.API_KEY, expires_in_days
            )
            
            # 保存到文件
            key_file = self.storage_path / f"{service}_api_key.enc"
            with open(key_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(encrypted_data), f, default=str, indent=2)
            
            # 设置文件权限
            key_file.chmod(0o600)
            
            self.logger.info(f"API密钥已安全存储: {service}")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "store_api_key",
                "service": service
            })
            return False
    
    def retrieve_api_key(self, service: str) -> Optional[str]:
        """检索API密钥"""
        try:
            key_file = self.storage_path / f"{service}_api_key.enc"
            
            if not key_file.exists():
                return None
            
            # 读取加密数据
            with open(key_file, 'r', encoding='utf-8') as f:
                data_dict = json.load(f)
            
            # 重建EncryptedData对象
            data_dict['created_at'] = datetime.fromisoformat(data_dict['created_at'])
            if data_dict.get('expires_at'):
                data_dict['expires_at'] = datetime.fromisoformat(data_dict['expires_at'])
            
            encrypted_data = EncryptedData(**data_dict)
            
            # 解密API密钥
            api_key = self.encryptor.decrypt_data(encrypted_data, str)
            
            self.logger.info(f"API密钥检索成功: {service}")
            return api_key
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "retrieve_api_key",
                "service": service
            })
            return None
    
    def store_user_data(self, user_id: str, data: Dict[str, Any]) -> bool:
        """存储用户数据"""
        try:
            # 加密用户数据
            encrypted_data = self.encryptor.encrypt_data(data, DataType.USER_DATA)
            
            # 保存到文件
            user_file = self.storage_path / f"user_{user_id}.enc"
            with open(user_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(encrypted_data), f, default=str, indent=2)
            
            user_file.chmod(0o600)
            
            self.logger.info(f"用户数据已安全存储: {user_id}")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "store_user_data",
                "user_id": user_id
            })
            return False
    
    def retrieve_user_data(self, user_id: str) -> Optional[Dict[str, Any]]:
        """检索用户数据"""
        try:
            user_file = self.storage_path / f"user_{user_id}.enc"
            
            if not user_file.exists():
                return None
            
            # 读取和解密数据
            with open(user_file, 'r', encoding='utf-8') as f:
                data_dict = json.load(f)
            
            data_dict['created_at'] = datetime.fromisoformat(data_dict['created_at'])
            if data_dict.get('expires_at'):
                data_dict['expires_at'] = datetime.fromisoformat(data_dict['expires_at'])
            
            encrypted_data = EncryptedData(**data_dict)
            user_data = self.encryptor.decrypt_data(encrypted_data, dict)
            
            self.logger.info(f"用户数据检索成功: {user_id}")
            return user_data
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "retrieve_user_data",
                "user_id": user_id
            })
            return None
    
    def create_encrypted_backup(self, source_path: Path, backup_path: Path) -> bool:
        """创建加密备份"""
        try:
            import shutil
            import tempfile
            import zipfile
            
            # 创建临时压缩文件
            with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_zip:
                with zipfile.ZipFile(temp_zip.name, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    if source_path.is_file():
                        zipf.write(source_path, source_path.name)
                    else:
                        for file_path in source_path.rglob('*'):
                            if file_path.is_file():
                                arcname = file_path.relative_to(source_path)
                                zipf.write(file_path, arcname)
                
                # 读取压缩文件内容
                with open(temp_zip.name, 'rb') as f:
                    backup_data = f.read()
                
                # 删除临时文件
                os.unlink(temp_zip.name)
            
            # 加密备份数据
            encrypted_data = self.encryptor.encrypt_data(backup_data, DataType.BACKUP_DATA)
            
            # 保存加密备份
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(asdict(encrypted_data), f, default=str, indent=2)
            
            backup_path.chmod(0o600)
            
            self.logger.info(f"加密备份创建成功: {backup_path}")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "create_encrypted_backup",
                "source": str(source_path),
                "backup": str(backup_path)
            })
            return False
    
    def restore_encrypted_backup(self, backup_path: Path, restore_path: Path) -> bool:
        """恢复加密备份"""
        try:
            import tempfile
            import zipfile
            
            # 读取加密备份
            with open(backup_path, 'r', encoding='utf-8') as f:
                data_dict = json.load(f)
            
            data_dict['created_at'] = datetime.fromisoformat(data_dict['created_at'])
            if data_dict.get('expires_at'):
                data_dict['expires_at'] = datetime.fromisoformat(data_dict['expires_at'])
            
            encrypted_data = EncryptedData(**data_dict)
            
            # 解密备份数据
            backup_data = self.encryptor.decrypt_data(encrypted_data, bytes)
            
            # 创建临时文件并解压
            with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_zip:
                temp_zip.write(backup_data)
                temp_zip.flush()
                
                # 解压到目标位置
                restore_path.mkdir(parents=True, exist_ok=True)
                with zipfile.ZipFile(temp_zip.name, 'r') as zipf:
                    zipf.extractall(restore_path)
                
                # 删除临时文件
                os.unlink(temp_zip.name)
            
            self.logger.info(f"加密备份恢复成功: {restore_path}")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "restore_encrypted_backup",
                "backup": str(backup_path),
                "restore": str(restore_path)
            })
            return False
    
    def cleanup_expired_data(self) -> int:
        """清理过期数据"""
        cleaned_count = 0
        
        try:
            for enc_file in self.storage_path.glob("*.enc"):
                try:
                    with open(enc_file, 'r', encoding='utf-8') as f:
                        data_dict = json.load(f)
                    
                    if data_dict.get('expires_at'):
                        expires_at = datetime.fromisoformat(data_dict['expires_at'])
                        if datetime.now() > expires_at:
                            enc_file.unlink()
                            cleaned_count += 1
                            self.logger.info(f"清理过期数据: {enc_file.name}")
                
                except Exception as e:
                    self.logger.warning(f"检查文件时出错 {enc_file}: {e}")
            
            self.logger.info(f"清理完成，删除了 {cleaned_count} 个过期文件")
            return cleaned_count
            
        except Exception as e:
            self.error_handler.handle_error(e, {"action": "cleanup_expired_data"})
            return 0
    
    def get_storage_info(self) -> Dict[str, Any]:
        """获取存储信息"""
        try:
            info = {
                "storage_path": str(self.storage_path),
                "total_files": 0,
                "encrypted_files": 0,
                "total_size": 0,
                "encryption_level": self.config.level.value,
                "auto_key_rotation": self.config.auto_key_rotation,
                "files": []
            }
            
            for file_path in self.storage_path.iterdir():
                if file_path.is_file():
                    info["total_files"] += 1
                    file_size = file_path.stat().st_size
                    info["total_size"] += file_size
                    
                    file_info = {
                        "name": file_path.name,
                        "size": file_size,
                        "modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                        "encrypted": file_path.suffix == ".enc"
                    }
                    
                    if file_info["encrypted"]:
                        info["encrypted_files"] += 1
                    
                    info["files"].append(file_info)
            
            return info
            
        except Exception as e:
            self.error_handler.handle_error(e, {"action": "get_storage_info"})
            return {}


class PrivacyManager:
    """隐私管理器 - GDPR合规"""
    
    def __init__(self, storage: SecureStorage):
        self.storage = storage
        self.logger = LoggerManager().get_logger("security.privacy")
        self.error_handler = ErrorHandler()
        self.consent_records: Dict[str, Dict] = {}
        self.data_processing_log: List[Dict] = []
    
    def record_consent(self, user_id: str, consent_type: str, 
                      granted: bool, purpose: str) -> bool:
        """记录用户同意"""
        try:
            consent_record = {
                "user_id": user_id,
                "consent_type": consent_type,
                "granted": granted,
                "purpose": purpose,
                "timestamp": datetime.now().isoformat(),
                "ip_address": "127.0.0.1",  # 在实际应用中获取真实IP
                "user_agent": "BambooFall AI Client"
            }
            
            if user_id not in self.consent_records:
                self.consent_records[user_id] = {}
            
            self.consent_records[user_id][consent_type] = consent_record
            
            # 保存同意记录
            self._save_consent_records()
            
            self.logger.info(f"用户同意记录已保存: {user_id} - {consent_type}")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "record_consent",
                "user_id": user_id,
                "consent_type": consent_type
            })
            return False
    
    def check_consent(self, user_id: str, consent_type: str) -> bool:
        """检查用户同意状态"""
        try:
            if user_id not in self.consent_records:
                return False
            
            consent = self.consent_records[user_id].get(consent_type)
            return consent and consent.get("granted", False)
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "check_consent",
                "user_id": user_id,
                "consent_type": consent_type
            })
            return False
    
    def log_data_processing(self, user_id: str, action: str, 
                           data_type: str, purpose: str) -> bool:
        """记录数据处理活动"""
        try:
            log_entry = {
                "user_id": user_id,
                "action": action,
                "data_type": data_type,
                "purpose": purpose,
                "timestamp": datetime.now().isoformat(),
                "legal_basis": "consent"  # 或其他法律依据
            }
            
            self.data_processing_log.append(log_entry)
            
            # 保存处理日志
            self._save_processing_log()
            
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "log_data_processing",
                "user_id": user_id,
                "data_action": action
            })
            return False
    
    def export_user_data(self, user_id: str) -> Optional[Dict[str, Any]]:
        """导出用户数据（GDPR数据可携带权）"""
        try:
            # 收集用户的所有数据
            user_data = {
                "user_id": user_id,
                "export_timestamp": datetime.now().isoformat(),
                "personal_data": {},
                "consent_records": {},
                "processing_log": []
            }
            
            # 获取用户个人数据
            personal_data = self.storage.retrieve_user_data(user_id)
            if personal_data:
                user_data["personal_data"] = personal_data
            
            # 获取同意记录
            if user_id in self.consent_records:
                user_data["consent_records"] = self.consent_records[user_id]
            
            # 获取处理日志
            user_processing_log = [
                entry for entry in self.data_processing_log
                if entry.get("user_id") == user_id
            ]
            user_data["processing_log"] = user_processing_log
            
            self.logger.info(f"用户数据导出完成: {user_id}")
            return user_data
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "export_user_data",
                "user_id": user_id
            })
            return None
    
    def delete_user_data(self, user_id: str, reason: str = "user_request") -> bool:
        """删除用户数据（GDPR被遗忘权）"""
        try:
            # 记录删除操作
            self.log_data_processing(user_id, "delete_all", "all_data", reason)
            
            # 删除用户个人数据文件
            user_file = self.storage.storage_path / f"user_{user_id}.enc"
            if user_file.exists():
                user_file.unlink()
                self.logger.info(f"删除用户数据文件: {user_file}")
            
            # 删除同意记录
            if user_id in self.consent_records:
                del self.consent_records[user_id]
                self._save_consent_records()
            
            # 匿名化处理日志（保留用于合规审计）
            for entry in self.data_processing_log:
                if entry.get("user_id") == user_id:
                    entry["user_id"] = "[DELETED]"
                    entry["anonymized"] = True
                    entry["deletion_timestamp"] = datetime.now().isoformat()
            
            self._save_processing_log()
            
            self.logger.info(f"用户数据删除完成: {user_id}")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "action": "delete_user_data",
                "user_id": user_id,
                "reason": reason
            })
            return False
    
    def _save_consent_records(self):
        """保存同意记录"""
        consent_file = self.storage.storage_path / "consent_records.enc"
        self.storage.encryptor.encrypt_data(self.consent_records, DataType.USER_DATA)
        
        # 简化保存逻辑
        with open(consent_file, 'w', encoding='utf-8') as f:
            json.dump(self.consent_records, f, indent=2, default=str)
        consent_file.chmod(0o600)
    
    def _save_processing_log(self):
        """保存处理日志"""
        log_file = self.storage.storage_path / "processing_log.enc"
        
        # 简化保存逻辑
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(self.data_processing_log, f, indent=2, default=str)
        log_file.chmod(0o600)


# 便捷函数
def create_secure_storage(storage_path: Union[str, Path], 
                         config: Optional[EncryptionConfig] = None) -> SecureStorage:
    """创建安全存储实例"""
    if config is None:
        config = EncryptionConfig()
    
    return SecureStorage(Path(storage_path), config)


def create_privacy_manager(storage: SecureStorage) -> PrivacyManager:
    """创建隐私管理器实例"""
    return PrivacyManager(storage)


if __name__ == "__main__":
    # 示例用法
    import tempfile
    
    # 创建临时存储目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建安全存储
        config = EncryptionConfig(
            level=EncryptionLevel.HIGH,
            auto_key_rotation=True,
            backup_encryption=True
        )
        
        storage = create_secure_storage(temp_dir, config)
        
        # 存储API密钥
        storage.store_api_key("openai", "sk-test123456789", expires_in_days=30)
        
        # 检索API密钥
        api_key = storage.retrieve_api_key("openai")
        print(f"检索到的API密钥: {api_key}")
        
        # 存储用户数据
        user_data = {
            "name": "张三",
            "email": "<EMAIL>",
            "preferences": {
                "theme": "dark",
                "language": "zh-CN"
            }
        }
        
        storage.store_user_data("user123", user_data)
        
        # 检索用户数据
        retrieved_data = storage.retrieve_user_data("user123")
        print(f"检索到的用户数据: {retrieved_data}")
        
        # 创建隐私管理器
        privacy_manager = create_privacy_manager(storage)
        
        # 记录用户同意
        privacy_manager.record_consent(
            "user123", "data_processing", True, "AI写作服务"
        )
        
        # 检查同意状态
        has_consent = privacy_manager.check_consent("user123", "data_processing")
        print(f"用户同意状态: {has_consent}")
        
        # 导出用户数据
        exported_data = privacy_manager.export_user_data("user123")
        print(f"导出的用户数据: {json.dumps(exported_data, indent=2, ensure_ascii=False)}")
        
        # 获取存储信息
        storage_info = storage.get_storage_info()
        print(f"存储信息: {json.dumps(storage_info, indent=2, ensure_ascii=False)}")