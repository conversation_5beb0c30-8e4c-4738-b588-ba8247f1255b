"""设置和配置界面

提供应用程序的各种配置管理界面，包括通用设置、AI配置、导出设置等。
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Callable, Any
import flet as ft
import json
import os
from pathlib import Path


class SettingsCategory(Enum):
    """设置分类"""
    GENERAL = "general"
    AI = "ai"
    EDITOR = "editor"
    EXPORT = "export"
    APPEARANCE = "appearance"
    ADVANCED = "advanced"


class ThemeMode(Enum):
    """主题模式"""
    LIGHT = "light"
    DARK = "dark"
    SYSTEM = "system"


class Language(Enum):
    """语言设置"""
    ZH_CN = "zh_CN"
    EN_US = "en_US"
    JA_JP = "ja_JP"
    KO_KR = "ko_KR"


class AutoSaveInterval(Enum):
    """自动保存间隔"""
    DISABLED = 0
    EVERY_30S = 30
    EVERY_1MIN = 60
    EVERY_5MIN = 300
    EVERY_10MIN = 600


@dataclass
class GeneralSettings:
    """通用设置"""
    language: Language = Language.ZH_CN
    theme_mode: ThemeMode = ThemeMode.SYSTEM
    auto_save_interval: AutoSaveInterval = AutoSaveInterval.EVERY_1MIN
    backup_enabled: bool = True
    backup_count: int = 5
    startup_check_updates: bool = True
    show_word_count: bool = True
    show_character_count: bool = True
    enable_spell_check: bool = True
    default_project_path: str = ""


@dataclass
class AISettings:
    """AI设置"""
    default_model: str = "gpt-3.5-turbo"
    api_key: str = ""
    api_base_url: str = ""
    max_tokens: int = 2000
    temperature: float = 0.7
    timeout: int = 30
    retry_count: int = 3
    enable_streaming: bool = True
    custom_prompts: Dict[str, str] = field(default_factory=dict)
    model_configs: Dict[str, Dict[str, Any]] = field(default_factory=dict)


@dataclass
class EditorSettings:
    """编辑器设置"""
    font_family: str = "Microsoft YaHei"
    font_size: int = 14
    line_height: float = 1.5
    tab_size: int = 4
    word_wrap: bool = True
    show_line_numbers: bool = False
    highlight_current_line: bool = True
    auto_indent: bool = True
    smart_quotes: bool = True
    typewriter_mode: bool = False
    focus_mode: bool = False
    zen_mode_padding: int = 20


@dataclass
class ExportSettings:
    """导出设置"""
    default_format: str = "docx"
    include_metadata: bool = True
    include_toc: bool = True
    page_size: str = "A4"
    margin_top: float = 2.5
    margin_bottom: float = 2.5
    margin_left: float = 2.0
    margin_right: float = 2.0
    font_family: str = "宋体"
    font_size: int = 12
    line_spacing: float = 1.5
    chapter_page_break: bool = True
    watermark_text: str = ""
    custom_css: str = ""


@dataclass
class AppearanceSettings:
    """外观设置"""
    primary_color: str = "#2196F3"
    accent_color: str = "#FF4081"
    window_opacity: float = 1.0
    sidebar_width: int = 250
    toolbar_size: str = "medium"
    icon_theme: str = "default"
    animation_enabled: bool = True
    compact_mode: bool = False


@dataclass
class AdvancedSettings:
    """高级设置"""
    debug_mode: bool = False
    log_level: str = "INFO"
    cache_size: int = 100
    memory_limit: int = 512
    thread_count: int = 4
    enable_analytics: bool = True
    crash_reporting: bool = True
    experimental_features: bool = False


class SettingsSection(ft.UserControl):
    """设置区域组件"""
    
    def __init__(
        self,
        title: str,
        description: str = "",
        controls: List[ft.Control] = None
    ):
        super().__init__()
        self.title = title
        self.description = description
        self.controls_list = controls or []
    
    def build(self):
        content = [
            ft.Text(self.title, size=16, weight=ft.FontWeight.BOLD),
        ]
        
        if self.description:
            content.append(
                ft.Text(
                    self.description,
                    size=12,
                    color=ft.colors.ON_SURFACE_VARIANT
                )
            )
        
        content.extend(self.controls_list)
        
        return ft.Container(
            content=ft.Column(content, spacing=12),
            padding=16,
            border=ft.border.all(1, ft.colors.OUTLINE_VARIANT),
            border_radius=8,
            margin=ft.margin.only(bottom=16)
        )


class GeneralSettingsPanel(ft.UserControl):
    """通用设置面板"""
    
    def __init__(self, settings: GeneralSettings, on_change: Optional[Callable] = None):
        super().__init__()
        self.settings = settings
        self.on_change = on_change
        self._create_controls()
    
    def _create_controls(self):
        """创建控件"""
        self.language_dropdown = ft.Dropdown(
            label="界面语言",
            options=[
                ft.dropdown.Option(Language.ZH_CN.value, "简体中文"),
                ft.dropdown.Option(Language.EN_US.value, "English"),
                ft.dropdown.Option(Language.JA_JP.value, "日本語"),
                ft.dropdown.Option(Language.KO_KR.value, "한국어")
            ],
            value=self.settings.language.value,
            on_change=self._handle_change
        )
        
        self.theme_dropdown = ft.Dropdown(
            label="主题模式",
            options=[
                ft.dropdown.Option(ThemeMode.LIGHT.value, "浅色"),
                ft.dropdown.Option(ThemeMode.DARK.value, "深色"),
                ft.dropdown.Option(ThemeMode.SYSTEM.value, "跟随系统")
            ],
            value=self.settings.theme_mode.value,
            on_change=self._handle_change
        )
        
        self.auto_save_dropdown = ft.Dropdown(
            label="自动保存间隔",
            options=[
                ft.dropdown.Option(str(AutoSaveInterval.DISABLED.value), "禁用"),
                ft.dropdown.Option(str(AutoSaveInterval.EVERY_30S.value), "30秒"),
                ft.dropdown.Option(str(AutoSaveInterval.EVERY_1MIN.value), "1分钟"),
                ft.dropdown.Option(str(AutoSaveInterval.EVERY_5MIN.value), "5分钟"),
                ft.dropdown.Option(str(AutoSaveInterval.EVERY_10MIN.value), "10分钟")
            ],
            value=str(self.settings.auto_save_interval.value),
            on_change=self._handle_change
        )
        
        self.backup_switch = ft.Switch(
            label="启用自动备份",
            value=self.settings.backup_enabled,
            on_change=self._handle_change
        )
        
        self.backup_count_field = ft.TextField(
            label="备份文件数量",
            value=str(self.settings.backup_count),
            keyboard_type=ft.KeyboardType.NUMBER,
            on_change=self._handle_change
        )
        
        self.update_check_switch = ft.Switch(
            label="启动时检查更新",
            value=self.settings.startup_check_updates,
            on_change=self._handle_change
        )
        
        self.word_count_switch = ft.Switch(
            label="显示字数统计",
            value=self.settings.show_word_count,
            on_change=self._handle_change
        )
        
        self.spell_check_switch = ft.Switch(
            label="启用拼写检查",
            value=self.settings.enable_spell_check,
            on_change=self._handle_change
        )
        
        self.project_path_field = ft.TextField(
            label="默认项目路径",
            value=self.settings.default_project_path,
            on_change=self._handle_change,
            suffix=ft.IconButton(
                ft.icons.FOLDER_OPEN,
                tooltip="选择文件夹",
                on_click=self._select_folder
            )
        )
    
    def build(self):
        return ft.Column([
            SettingsSection(
                "界面设置",
                "配置应用程序的界面语言和主题",
                [
                    self.language_dropdown,
                    self.theme_dropdown
                ]
            ),
            SettingsSection(
                "自动保存",
                "配置文档的自动保存和备份功能",
                [
                    self.auto_save_dropdown,
                    self.backup_switch,
                    self.backup_count_field
                ]
            ),
            SettingsSection(
                "其他设置",
                "其他通用功能设置",
                [
                    self.update_check_switch,
                    self.word_count_switch,
                    self.spell_check_switch,
                    self.project_path_field
                ]
            )
        ], scroll=ft.ScrollMode.AUTO)
    
    def _handle_change(self, e):
        """处理设置变化"""
        # 更新设置对象
        if e.control == self.language_dropdown:
            self.settings.language = Language(e.control.value)
        elif e.control == self.theme_dropdown:
            self.settings.theme_mode = ThemeMode(e.control.value)
        elif e.control == self.auto_save_dropdown:
            self.settings.auto_save_interval = AutoSaveInterval(int(e.control.value))
        elif e.control == self.backup_switch:
            self.settings.backup_enabled = e.control.value
        elif e.control == self.backup_count_field:
            try:
                self.settings.backup_count = int(e.control.value)
            except ValueError:
                pass
        elif e.control == self.update_check_switch:
            self.settings.startup_check_updates = e.control.value
        elif e.control == self.word_count_switch:
            self.settings.show_word_count = e.control.value
        elif e.control == self.spell_check_switch:
            self.settings.enable_spell_check = e.control.value
        elif e.control == self.project_path_field:
            self.settings.default_project_path = e.control.value
        
        if self.on_change:
            self.on_change(self.settings)
    
    def _select_folder(self, e):
        """选择文件夹"""
        # TODO: 实现文件夹选择对话框
        pass


class AISettingsPanel(ft.UserControl):
    """AI设置面板"""
    
    def __init__(self, settings: AISettings, on_change: Optional[Callable] = None):
        super().__init__()
        self.settings = settings
        self.on_change = on_change
        self._create_controls()
    
    def _create_controls(self):
        """创建控件"""
        self.model_dropdown = ft.Dropdown(
            label="默认AI模型",
            options=[
                ft.dropdown.Option("gpt-4", "GPT-4"),
                ft.dropdown.Option("gpt-3.5-turbo", "GPT-3.5 Turbo"),
                ft.dropdown.Option("claude-3", "Claude 3"),
                ft.dropdown.Option("gemini-pro", "Gemini Pro")
            ],
            value=self.settings.default_model,
            on_change=self._handle_change
        )
        
        self.api_key_field = ft.TextField(
            label="API密钥",
            value=self.settings.api_key,
            password=True,
            can_reveal_password=True,
            on_change=self._handle_change
        )
        
        self.api_url_field = ft.TextField(
            label="API基础URL",
            value=self.settings.api_base_url,
            hint_text="留空使用默认URL",
            on_change=self._handle_change
        )
        
        self.max_tokens_field = ft.TextField(
            label="最大Token数",
            value=str(self.settings.max_tokens),
            keyboard_type=ft.KeyboardType.NUMBER,
            on_change=self._handle_change
        )
        
        self.temperature_slider = ft.Slider(
            min=0.0,
            max=2.0,
            value=self.settings.temperature,
            label="创造性 ({value})",
            divisions=20,
            on_change=self._handle_change
        )
        
        self.timeout_field = ft.TextField(
            label="请求超时（秒）",
            value=str(self.settings.timeout),
            keyboard_type=ft.KeyboardType.NUMBER,
            on_change=self._handle_change
        )
        
        self.retry_field = ft.TextField(
            label="重试次数",
            value=str(self.settings.retry_count),
            keyboard_type=ft.KeyboardType.NUMBER,
            on_change=self._handle_change
        )
        
        self.streaming_switch = ft.Switch(
            label="启用流式响应",
            value=self.settings.enable_streaming,
            on_change=self._handle_change
        )
    
    def build(self):
        return ft.Column([
            SettingsSection(
                "模型配置",
                "配置AI模型和API设置",
                [
                    self.model_dropdown,
                    self.api_key_field,
                    self.api_url_field
                ]
            ),
            SettingsSection(
                "生成参数",
                "配置AI生成的参数",
                [
                    self.max_tokens_field,
                    ft.Text("创造性"),
                    self.temperature_slider
                ]
            ),
            SettingsSection(
                "连接设置",
                "配置网络连接参数",
                [
                    self.timeout_field,
                    self.retry_field,
                    self.streaming_switch
                ]
            ),
            SettingsSection(
                "测试连接",
                "测试AI服务连接",
                [
                    ft.ElevatedButton(
                        "测试连接",
                        icon=ft.icons.WIFI_PROTECTED_SETUP,
                        on_click=self._test_connection
                    )
                ]
            )
        ], scroll=ft.ScrollMode.AUTO)
    
    def _handle_change(self, e):
        """处理设置变化"""
        if e.control == self.model_dropdown:
            self.settings.default_model = e.control.value
        elif e.control == self.api_key_field:
            self.settings.api_key = e.control.value
        elif e.control == self.api_url_field:
            self.settings.api_base_url = e.control.value
        elif e.control == self.max_tokens_field:
            try:
                self.settings.max_tokens = int(e.control.value)
            except ValueError:
                pass
        elif e.control == self.temperature_slider:
            self.settings.temperature = e.control.value
        elif e.control == self.timeout_field:
            try:
                self.settings.timeout = int(e.control.value)
            except ValueError:
                pass
        elif e.control == self.retry_field:
            try:
                self.settings.retry_count = int(e.control.value)
            except ValueError:
                pass
        elif e.control == self.streaming_switch:
            self.settings.enable_streaming = e.control.value
        
        if self.on_change:
            self.on_change(self.settings)
    
    def _test_connection(self, e):
        """测试AI连接"""
        # TODO: 实现AI连接测试
        self.page.show_snack_bar(
            ft.SnackBar(content=ft.Text("连接测试功能开发中..."))
        )


class EditorSettingsPanel(ft.UserControl):
    """编辑器设置面板"""
    
    def __init__(self, settings: EditorSettings, on_change: Optional[Callable] = None):
        super().__init__()
        self.settings = settings
        self.on_change = on_change
        self._create_controls()
    
    def _create_controls(self):
        """创建控件"""
        self.font_family_dropdown = ft.Dropdown(
            label="字体",
            options=[
                ft.dropdown.Option("Microsoft YaHei", "微软雅黑"),
                ft.dropdown.Option("SimSun", "宋体"),
                ft.dropdown.Option("SimHei", "黑体"),
                ft.dropdown.Option("KaiTi", "楷体"),
                ft.dropdown.Option("Arial", "Arial"),
                ft.dropdown.Option("Times New Roman", "Times New Roman"),
                ft.dropdown.Option("Courier New", "Courier New")
            ],
            value=self.settings.font_family,
            on_change=self._handle_change
        )
        
        self.font_size_slider = ft.Slider(
            min=10,
            max=24,
            value=self.settings.font_size,
            label="字体大小 ({value})",
            divisions=14,
            on_change=self._handle_change
        )
        
        self.line_height_slider = ft.Slider(
            min=1.0,
            max=3.0,
            value=self.settings.line_height,
            label="行高 ({value})",
            divisions=20,
            on_change=self._handle_change
        )
        
        self.word_wrap_switch = ft.Switch(
            label="自动换行",
            value=self.settings.word_wrap,
            on_change=self._handle_change
        )
        
        self.line_numbers_switch = ft.Switch(
            label="显示行号",
            value=self.settings.show_line_numbers,
            on_change=self._handle_change
        )
        
        self.highlight_line_switch = ft.Switch(
            label="高亮当前行",
            value=self.settings.highlight_current_line,
            on_change=self._handle_change
        )
        
        self.smart_quotes_switch = ft.Switch(
            label="智能引号",
            value=self.settings.smart_quotes,
            on_change=self._handle_change
        )
        
        self.typewriter_switch = ft.Switch(
            label="打字机模式",
            value=self.settings.typewriter_mode,
            on_change=self._handle_change
        )
        
        self.focus_mode_switch = ft.Switch(
            label="专注模式",
            value=self.settings.focus_mode,
            on_change=self._handle_change
        )
    
    def build(self):
        return ft.Column([
            SettingsSection(
                "字体设置",
                "配置编辑器的字体和排版",
                [
                    self.font_family_dropdown,
                    ft.Text("字体大小"),
                    self.font_size_slider,
                    ft.Text("行高"),
                    self.line_height_slider
                ]
            ),
            SettingsSection(
                "显示设置",
                "配置编辑器的显示选项",
                [
                    self.word_wrap_switch,
                    self.line_numbers_switch,
                    self.highlight_line_switch
                ]
            ),
            SettingsSection(
                "编辑功能",
                "配置编辑器的辅助功能",
                [
                    self.smart_quotes_switch,
                    self.typewriter_switch,
                    self.focus_mode_switch
                ]
            )
        ], scroll=ft.ScrollMode.AUTO)
    
    def _handle_change(self, e):
        """处理设置变化"""
        if e.control == self.font_family_dropdown:
            self.settings.font_family = e.control.value
        elif e.control == self.font_size_slider:
            self.settings.font_size = int(e.control.value)
        elif e.control == self.line_height_slider:
            self.settings.line_height = e.control.value
        elif e.control == self.word_wrap_switch:
            self.settings.word_wrap = e.control.value
        elif e.control == self.line_numbers_switch:
            self.settings.show_line_numbers = e.control.value
        elif e.control == self.highlight_line_switch:
            self.settings.highlight_current_line = e.control.value
        elif e.control == self.smart_quotes_switch:
            self.settings.smart_quotes = e.control.value
        elif e.control == self.typewriter_switch:
            self.settings.typewriter_mode = e.control.value
        elif e.control == self.focus_mode_switch:
            self.settings.focus_mode = e.control.value
        
        if self.on_change:
            self.on_change(self.settings)


class SettingsPage(ft.UserControl):
    """设置页面主组件"""
    
    def __init__(self):
        super().__init__()
        
        # 设置数据
        self.general_settings = GeneralSettings()
        self.ai_settings = AISettings()
        self.editor_settings = EditorSettings()
        self.export_settings = ExportSettings()
        self.appearance_settings = AppearanceSettings()
        self.advanced_settings = AdvancedSettings()
        
        # 当前选中的分类
        self.current_category = SettingsCategory.GENERAL
        
        # 创建侧边栏
        self.sidebar = self._create_sidebar()
        
        # 创建内容区域
        self.content_area = ft.Container(
            content=self._create_content_panel(),
            expand=True,
            padding=20
        )
        
        # 加载设置
        self._load_settings()
    
    def build(self):
        return ft.Row([
            # 侧边栏
            ft.Container(
                content=self.sidebar,
                width=250,
                bgcolor=ft.colors.SURFACE_VARIANT,
                padding=16
            ),
            # 内容区域
            self.content_area,
            # 操作按钮区域
            ft.Container(
                content=ft.Column([
                    ft.ElevatedButton(
                        "保存设置",
                        icon=ft.icons.SAVE,
                        on_click=self._save_settings
                    ),
                    ft.OutlinedButton(
                        "重置默认",
                        icon=ft.icons.RESTORE,
                        on_click=self._reset_settings
                    ),
                    ft.OutlinedButton(
                        "导入设置",
                        icon=ft.icons.UPLOAD,
                        on_click=self._import_settings
                    ),
                    ft.OutlinedButton(
                        "导出设置",
                        icon=ft.icons.DOWNLOAD,
                        on_click=self._export_settings
                    )
                ], spacing=8),
                width=150,
                padding=16
            )
        ], expand=True)
    
    def _create_sidebar(self) -> ft.Control:
        """创建侧边栏"""
        categories = [
            (SettingsCategory.GENERAL, "通用设置", ft.icons.SETTINGS),
            (SettingsCategory.AI, "AI配置", ft.icons.SMART_TOY),
            (SettingsCategory.EDITOR, "编辑器", ft.icons.EDIT),
            (SettingsCategory.EXPORT, "导出设置", ft.icons.DOWNLOAD),
            (SettingsCategory.APPEARANCE, "外观", ft.icons.PALETTE),
            (SettingsCategory.ADVANCED, "高级设置", ft.icons.TUNE)
        ]
        
        nav_items = []
        for category, title, icon in categories:
            nav_items.append(
                ft.ListTile(
                    leading=ft.Icon(icon),
                    title=ft.Text(title),
                    selected=category == self.current_category,
                    on_click=lambda e, cat=category: self._switch_category(cat)
                )
            )
        
        return ft.Column([
            ft.Text("设置", size=20, weight=ft.FontWeight.BOLD),
            ft.Divider(),
            *nav_items
        ], spacing=4)
    
    def _create_content_panel(self) -> ft.Control:
        """创建内容面板"""
        if self.current_category == SettingsCategory.GENERAL:
            return GeneralSettingsPanel(
                self.general_settings,
                on_change=self._handle_settings_change
            )
        elif self.current_category == SettingsCategory.AI:
            return AISettingsPanel(
                self.ai_settings,
                on_change=self._handle_settings_change
            )
        elif self.current_category == SettingsCategory.EDITOR:
            return EditorSettingsPanel(
                self.editor_settings,
                on_change=self._handle_settings_change
            )
        else:
            return ft.Container(
                content=ft.Text(
                    f"{self.current_category.value.title()}设置面板开发中...",
                    text_align=ft.TextAlign.CENTER
                ),
                alignment=ft.alignment.center
            )
    
    def _switch_category(self, category: SettingsCategory):
        """切换设置分类"""
        self.current_category = category
        
        # 更新侧边栏选中状态
        for item in self.sidebar.controls[2:]:  # 跳过标题和分隔线
            item.selected = False
        
        # 设置当前选中项
        category_index = list(SettingsCategory).index(category)
        self.sidebar.controls[2 + category_index].selected = True
        
        # 更新内容面板
        self.content_area.content = self._create_content_panel()
        
        self.update()
    
    def _handle_settings_change(self, settings):
        """处理设置变化"""
        # 设置已在各个面板中更新，这里可以添加额外的处理逻辑
        pass
    
    def _save_settings(self, e):
        """保存设置"""
        try:
            settings_data = {
                "general": self._dataclass_to_dict(self.general_settings),
                "ai": self._dataclass_to_dict(self.ai_settings),
                "editor": self._dataclass_to_dict(self.editor_settings),
                "export": self._dataclass_to_dict(self.export_settings),
                "appearance": self._dataclass_to_dict(self.appearance_settings),
                "advanced": self._dataclass_to_dict(self.advanced_settings)
            }
            
            # 保存到文件
            settings_path = Path("settings.json")
            with open(settings_path, "w", encoding="utf-8") as f:
                json.dump(settings_data, f, ensure_ascii=False, indent=2)
            
            self.page.show_snack_bar(
                ft.SnackBar(content=ft.Text("设置已保存"))
            )
        except Exception as ex:
            self.page.show_snack_bar(
                ft.SnackBar(content=ft.Text(f"保存失败: {str(ex)}"))
            )
    
    def _load_settings(self):
        """加载设置"""
        try:
            settings_path = Path("settings.json")
            if settings_path.exists():
                with open(settings_path, "r", encoding="utf-8") as f:
                    settings_data = json.load(f)
                
                # 更新设置对象
                if "general" in settings_data:
                    self._dict_to_dataclass(settings_data["general"], self.general_settings)
                if "ai" in settings_data:
                    self._dict_to_dataclass(settings_data["ai"], self.ai_settings)
                if "editor" in settings_data:
                    self._dict_to_dataclass(settings_data["editor"], self.editor_settings)
                
        except Exception as ex:
            print(f"加载设置失败: {ex}")
    
    def _reset_settings(self, e):
        """重置设置为默认值"""
        def confirm_reset(e):
            self.general_settings = GeneralSettings()
            self.ai_settings = AISettings()
            self.editor_settings = EditorSettings()
            self.export_settings = ExportSettings()
            self.appearance_settings = AppearanceSettings()
            self.advanced_settings = AdvancedSettings()
            
            # 重新创建内容面板
            self.content_area.content = self._create_content_panel()
            self.update()
            
            self.page.dialog.open = False
            self.page.update()
            
            self.page.show_snack_bar(
                ft.SnackBar(content=ft.Text("设置已重置为默认值"))
            )
        
        def cancel_reset(e):
            self.page.dialog.open = False
            self.page.update()
        
        confirm_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("确认重置"),
            content=ft.Text("确定要将所有设置重置为默认值吗？此操作不可撤销。"),
            actions=[
                ft.TextButton("取消", on_click=cancel_reset),
                ft.TextButton("重置", on_click=confirm_reset)
            ]
        )
        
        self.page.dialog = confirm_dialog
        confirm_dialog.open = True
        self.page.update()
    
    def _import_settings(self, e):
        """导入设置"""
        # TODO: 实现设置导入功能
        self.page.show_snack_bar(
            ft.SnackBar(content=ft.Text("导入功能开发中..."))
        )
    
    def _export_settings(self, e):
        """导出设置"""
        # TODO: 实现设置导出功能
        self.page.show_snack_bar(
            ft.SnackBar(content=ft.Text("导出功能开发中..."))
        )
    
    def _dataclass_to_dict(self, obj) -> dict:
        """将数据类转换为字典"""
        result = {}
        for field_name, field_value in obj.__dict__.items():
            if hasattr(field_value, 'value'):  # 枚举类型
                result[field_name] = field_value.value
            else:
                result[field_name] = field_value
        return result
    
    def _dict_to_dataclass(self, data: dict, obj):
        """将字典数据更新到数据类"""
        for key, value in data.items():
            if hasattr(obj, key):
                current_value = getattr(obj, key)
                if hasattr(current_value, '__class__') and hasattr(current_value.__class__, '__bases__'):
                    # 处理枚举类型
                    if any(base.__name__ == 'Enum' for base in current_value.__class__.__bases__):
                        try:
                            setattr(obj, key, current_value.__class__(value))
                        except ValueError:
                            pass  # 忽略无效的枚举值
                    else:
                        setattr(obj, key, value)
                else:
                    setattr(obj, key, value)


# 工具函数
def create_settings_page() -> SettingsPage:
    """创建设置页面"""
    return SettingsPage()


def load_app_settings() -> Dict[str, Any]:
    """加载应用设置"""
    try:
        settings_path = Path("settings.json")
        if settings_path.exists():
            with open(settings_path, "r", encoding="utf-8") as f:
                return json.load(f)
    except Exception:
        pass
    return {}


def save_app_settings(settings: Dict[str, Any]):
    """保存应用设置"""
    try:
        settings_path = Path("settings.json")
        with open(settings_path, "w", encoding="utf-8") as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)
    except Exception as ex:
        print(f"保存设置失败: {ex}")