"""弹性布局组件

提供灵活的弹性盒子布局系统，支持各种对齐和分布方式。
"""

import flet as ft
from typing import List, Optional, Union
from enum import Enum
from dataclasses import dataclass
from ..components.base import BaseComponent, theme_manager

class FlexDirection(str, Enum):
    """弹性布局方向"""
    ROW = "row"
    ROW_REVERSE = "row_reverse"
    COLUMN = "column"
    COLUMN_REVERSE = "column_reverse"

class FlexWrap(str, Enum):
    """弹性布局换行"""
    NO_WRAP = "nowrap"
    WRAP = "wrap"
    WRAP_REVERSE = "wrap_reverse"

class JustifyContent(str, Enum):
    """主轴对齐方式"""
    START = "start"
    END = "end"
    CENTER = "center"
    SPACE_BETWEEN = "space_between"
    SPACE_AROUND = "space_around"
    SPACE_EVENLY = "space_evenly"

class AlignItems(str, Enum):
    """交叉轴对齐方式"""
    START = "start"
    END = "end"
    CENTER = "center"
    STRETCH = "stretch"
    BASELINE = "baseline"

class AlignContent(str, Enum):
    """多行对齐方式"""
    START = "start"
    END = "end"
    CENTER = "center"
    STRETCH = "stretch"
    SPACE_BETWEEN = "space_between"
    SPACE_AROUND = "space_around"
    SPACE_EVENLY = "space_evenly"

@dataclass
class FlexConfig:
    """弹性布局配置"""
    direction: FlexDirection = FlexDirection.ROW
    wrap: FlexWrap = FlexWrap.NO_WRAP
    justify_content: JustifyContent = JustifyContent.START
    align_items: AlignItems = AlignItems.STRETCH
    align_content: AlignContent = AlignContent.STRETCH
    gap: int = 0
    row_gap: Optional[int] = None
    column_gap: Optional[int] = None
    
    def get_row_gap(self) -> int:
        """获取行间距"""
        return self.row_gap if self.row_gap is not None else self.gap
    
    def get_column_gap(self) -> int:
        """获取列间距"""
        return self.column_gap if self.column_gap is not None else self.gap
    
    def is_row_direction(self) -> bool:
        """是否为行方向"""
        return self.direction in [FlexDirection.ROW, FlexDirection.ROW_REVERSE]
    
    def is_reverse(self) -> bool:
        """是否为反向"""
        return self.direction in [FlexDirection.ROW_REVERSE, FlexDirection.COLUMN_REVERSE]

@dataclass
class FlexItemConfig:
    """弹性项配置"""
    flex_grow: float = 0  # 放大比例
    flex_shrink: float = 1  # 缩小比例
    flex_basis: Optional[Union[int, str]] = None  # 基础尺寸
    align_self: Optional[AlignItems] = None  # 自身对齐方式
    order: int = 0  # 显示顺序
    
    def get_flex_value(self) -> float:
        """获取flex值"""
        return self.flex_grow

class FlexItem(BaseComponent):
    """弹性项组件"""
    
    def __init__(
        self,
        content: ft.Control,
        config: Optional[FlexItemConfig] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.content = content
        self.config = config or FlexItemConfig()
    
    def _update_theme_styles(self):
        """更新主题样式"""
        # 弹性项的样式更新由内容组件处理
        pass
    
    def build(self) -> ft.Control:
        """构建弹性项"""
        # 创建容器包装内容
        container = ft.Container(
            content=self.content,
            expand=self.config.flex_grow > 0,
        )
        
        # 如果有flex_grow，设置expand属性
        if self.config.flex_grow > 0:
            container.expand = True
        
        return container
    
    def set_flex_grow(self, grow: float):
        """设置放大比例"""
        self.config.flex_grow = grow
        if self._control:
            self._control.expand = grow > 0
            self.update()
    
    def set_flex_shrink(self, shrink: float):
        """设置缩小比例"""
        self.config.flex_shrink = shrink
    
    def set_order(self, order: int):
        """设置显示顺序"""
        self.config.order = order

class FlexLayout(BaseComponent):
    """弹性布局组件"""
    
    def __init__(
        self,
        children: List[Union[ft.Control, FlexItem]] = None,
        config: Optional[FlexConfig] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.children = children or []
        self.config = config or FlexConfig()
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            # 更新弹性布局样式
            pass
    
    def _get_main_axis_alignment(self) -> ft.MainAxisAlignment:
        """获取主轴对齐方式"""
        alignment_map = {
            JustifyContent.START: ft.MainAxisAlignment.START,
            JustifyContent.END: ft.MainAxisAlignment.END,
            JustifyContent.CENTER: ft.MainAxisAlignment.CENTER,
            JustifyContent.SPACE_BETWEEN: ft.MainAxisAlignment.SPACE_BETWEEN,
            JustifyContent.SPACE_AROUND: ft.MainAxisAlignment.SPACE_AROUND,
            JustifyContent.SPACE_EVENLY: ft.MainAxisAlignment.SPACE_EVENLY,
        }
        return alignment_map.get(self.config.justify_content, ft.MainAxisAlignment.START)
    
    def _get_cross_axis_alignment(self) -> ft.CrossAxisAlignment:
        """获取交叉轴对齐方式"""
        alignment_map = {
            AlignItems.START: ft.CrossAxisAlignment.START,
            AlignItems.END: ft.CrossAxisAlignment.END,
            AlignItems.CENTER: ft.CrossAxisAlignment.CENTER,
            AlignItems.STRETCH: ft.CrossAxisAlignment.STRETCH,
            AlignItems.BASELINE: ft.CrossAxisAlignment.START,  # Flet没有baseline，使用start
        }
        return alignment_map.get(self.config.align_items, ft.CrossAxisAlignment.STRETCH)
    
    def _prepare_children(self) -> List[ft.Control]:
        """准备子组件"""
        prepared_children = []
        
        # 按order排序
        sorted_children = sorted(
            self.children,
            key=lambda x: x.config.order if isinstance(x, FlexItem) else 0
        )
        
        # 如果是反向，反转顺序
        if self.config.is_reverse():
            sorted_children = list(reversed(sorted_children))
        
        for child in sorted_children:
            if isinstance(child, FlexItem):
                prepared_children.append(child.get_control())
            else:
                prepared_children.append(child)
        
        return prepared_children
    
    def build(self) -> ft.Control:
        """构建弹性布局"""
        if not self.children:
            return ft.Container()
        
        prepared_children = self._prepare_children()
        
        # 根据方向创建布局
        if self.config.is_row_direction():
            # 行方向布局
            container = ft.Row(
                prepared_children,
                alignment=self._get_main_axis_alignment(),
                vertical_alignment=self._get_cross_axis_alignment(),
                spacing=self.config.get_column_gap(),
                wrap=self.config.wrap == FlexWrap.WRAP,
                expand=True
            )
        else:
            # 列方向布局
            container = ft.Column(
                prepared_children,
                alignment=self._get_main_axis_alignment(),
                horizontal_alignment=self._get_cross_axis_alignment(),
                spacing=self.config.get_row_gap(),
                expand=True
            )
        
        return container
    
    def add_child(self, child: Union[ft.Control, FlexItem], index: Optional[int] = None):
        """添加子组件"""
        if index is None:
            self.children.append(child)
        else:
            self.children.insert(index, child)
        
        # 重新构建布局
        if self._control:
            self._control = self.build()
            self.update()
    
    def remove_child(self, index: int):
        """移除子组件"""
        if 0 <= index < len(self.children):
            self.children.pop(index)
            
            # 重新构建布局
            if self._control:
                self._control = self.build()
                self.update()
    
    def clear_children(self):
        """清空所有子组件"""
        self.children.clear()
        
        # 重新构建布局
        if self._control:
            self._control = self.build()
            self.update()
    
    def set_direction(self, direction: FlexDirection):
        """设置布局方向"""
        self.config.direction = direction
        
        # 重新构建布局
        if self._control:
            self._control = self.build()
            self.update()
    
    def set_justify_content(self, justify: JustifyContent):
        """设置主轴对齐方式"""
        self.config.justify_content = justify
        
        # 重新构建布局
        if self._control:
            self._control = self.build()
            self.update()
    
    def set_align_items(self, align: AlignItems):
        """设置交叉轴对齐方式"""
        self.config.align_items = align
        
        # 重新构建布局
        if self._control:
            self._control = self.build()
            self.update()
    
    def set_gap(self, gap: int):
        """设置间距"""
        self.config.gap = gap
        
        # 重新构建布局
        if self._control:
            self._control = self.build()
            self.update()

# 弹性布局工具函数

def create_flex_row(
    children: List[ft.Control],
    justify: JustifyContent = JustifyContent.START,
    align: AlignItems = AlignItems.STRETCH,
    gap: int = 0,
    wrap: bool = False
) -> FlexLayout:
    """创建弹性行布局"""
    config = FlexConfig(
        direction=FlexDirection.ROW,
        justify_content=justify,
        align_items=align,
        gap=gap,
        wrap=FlexWrap.WRAP if wrap else FlexWrap.NO_WRAP
    )
    
    return FlexLayout(children=children, config=config)

def create_flex_column(
    children: List[ft.Control],
    justify: JustifyContent = JustifyContent.START,
    align: AlignItems = AlignItems.STRETCH,
    gap: int = 0
) -> FlexLayout:
    """创建弹性列布局"""
    config = FlexConfig(
        direction=FlexDirection.COLUMN,
        justify_content=justify,
        align_items=align,
        gap=gap
    )
    
    return FlexLayout(children=children, config=config)

def create_centered_layout(
    content: ft.Control,
    width: Optional[int] = None,
    height: Optional[int] = None
) -> FlexLayout:
    """创建居中布局"""
    container = ft.Container(
        content=content,
        width=width,
        height=height
    )
    
    config = FlexConfig(
        direction=FlexDirection.COLUMN,
        justify_content=JustifyContent.CENTER,
        align_items=AlignItems.CENTER
    )
    
    return FlexLayout(children=[container], config=config)

def create_space_between_layout(
    left_content: ft.Control,
    right_content: ft.Control,
    gap: int = 0
) -> FlexLayout:
    """创建两端对齐布局"""
    config = FlexConfig(
        direction=FlexDirection.ROW,
        justify_content=JustifyContent.SPACE_BETWEEN,
        align_items=AlignItems.CENTER,
        gap=gap
    )
    
    return FlexLayout(children=[left_content, right_content], config=config)

def create_sidebar_layout(
    sidebar: ft.Control,
    main_content: ft.Control,
    sidebar_width: int = 250,
    gap: int = 0
) -> FlexLayout:
    """创建侧边栏布局"""
    # 侧边栏容器
    sidebar_container = ft.Container(
        content=sidebar,
        width=sidebar_width,
        expand=False
    )
    
    # 主内容容器
    main_container = FlexItem(
        content=main_content,
        config=FlexItemConfig(flex_grow=1)
    )
    
    config = FlexConfig(
        direction=FlexDirection.ROW,
        justify_content=JustifyContent.START,
        align_items=AlignItems.STRETCH,
        gap=gap
    )
    
    return FlexLayout(children=[sidebar_container, main_container], config=config)

def create_header_content_footer_layout(
    header: ft.Control,
    content: ft.Control,
    footer: ft.Control,
    header_height: Optional[int] = None,
    footer_height: Optional[int] = None,
    gap: int = 0
) -> FlexLayout:
    """创建头部-内容-底部布局"""
    # 头部容器
    header_container = ft.Container(
        content=header,
        height=header_height,
        expand=False
    )
    
    # 内容容器（可扩展）
    content_item = FlexItem(
        content=content,
        config=FlexItemConfig(flex_grow=1)
    )
    
    # 底部容器
    footer_container = ft.Container(
        content=footer,
        height=footer_height,
        expand=False
    )
    
    config = FlexConfig(
        direction=FlexDirection.COLUMN,
        justify_content=JustifyContent.START,
        align_items=AlignItems.STRETCH,
        gap=gap
    )
    
    return FlexLayout(
        children=[header_container, content_item, footer_container],
        config=config
    )

# 导出
__all__ = [
    'FlexDirection',
    'FlexWrap',
    'JustifyContent',
    'AlignItems',
    'AlignContent',
    'FlexConfig',
    'FlexItemConfig',
    'FlexItem',
    'FlexLayout',
    'create_flex_row',
    'create_flex_column',
    'create_centered_layout',
    'create_space_between_layout',
    'create_sidebar_layout',
    'create_header_content_footer_layout',
]