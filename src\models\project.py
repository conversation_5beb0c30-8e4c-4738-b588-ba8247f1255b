"""项目数据模型

定义小说项目相关的数据模型，包括：
- 项目基本信息
- 项目统计数据
- 项目设置和配置
- 项目状态管理
"""

import uuid
from datetime import datetime
from typing import Optional, List
from enum import Enum

from sqlalchemy import Column, String, Text, Integer, Float, Boolean, JSON, Enum as SQLEnum
from sqlalchemy.orm import relationship
from pydantic import BaseModel, Field, validator

from .base import BaseTable, BaseSchema, BaseResponseSchema, BaseCreateSchema, BaseUpdateSchema

class ProjectStatus(str, Enum):
    """项目状态枚举"""
    DRAFT = "draft"          # 草稿
    ACTIVE = "active"        # 进行中
    PAUSED = "paused"        # 暂停
    COMPLETED = "completed"  # 已完成
    ARCHIVED = "archived"    # 已归档

class ProjectGenre(str, Enum):
    """小说类型枚举"""
    FANTASY = "fantasy"              # 玄幻
    ROMANCE = "romance"              # 言情
    URBAN = "urban"                  # 都市
    HISTORICAL = "historical"        # 历史
    SCIFI = "scifi"                  # 科幻
    MYSTERY = "mystery"              # 悬疑
    MARTIAL_ARTS = "martial_arts"    # 武侠
    GAME = "game"                    # 游戏
    MILITARY = "military"            # 军事
    SPORTS = "sports"                # 体育
    BUSINESS = "business"            # 商战
    YOUTH = "youth"                  # 青春
    OTHER = "other"                  # 其他

class Project(BaseTable):
    """项目数据模型
    
    存储小说项目的基本信息和元数据。
    """
    __tablename__ = "projects"
    
    # 基本信息
    title = Column(
        String(255),
        nullable=False,
        comment="项目标题"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="项目描述"
    )
    
    # 小说类型
    genre = Column(
        SQLEnum(ProjectGenre),
        nullable=False,
        default=ProjectGenre.OTHER,
        comment="小说类型"
    )
    
    # 项目状态
    status = Column(
        SQLEnum(ProjectStatus),
        nullable=False,
        default=ProjectStatus.DRAFT,
        comment="项目状态"
    )
    
    # 封面图片路径
    cover_image = Column(
        String(500),
        nullable=True,
        comment="封面图片路径"
    )
    
    # 标签（JSON数组）
    tags = Column(
        JSON,
        nullable=True,
        comment="项目标签"
    )
    
    # 统计信息
    total_words = Column(
        Integer,
        default=0,
        nullable=False,
        comment="总字数"
    )
    
    total_chapters = Column(
        Integer,
        default=0,
        nullable=False,
        comment="总章节数"
    )
    
    total_characters = Column(
        Integer,
        default=0,
        nullable=False,
        comment="角色数量"
    )
    
    total_scenes = Column(
        Integer,
        default=0,
        nullable=False,
        comment="场景数量"
    )
    
    # 目标设置
    target_words = Column(
        Integer,
        nullable=True,
        comment="目标字数"
    )
    
    target_chapters = Column(
        Integer,
        nullable=True,
        comment="目标章节数"
    )
    
    # 进度百分比
    progress_percentage = Column(
        Float,
        default=0.0,
        nullable=False,
        comment="完成进度百分比"
    )
    
    # 项目设置（JSON对象）
    settings = Column(
        JSON,
        nullable=True,
        comment="项目设置"
    )
    
    # 最后编辑时间
    last_edited_at = Column(
        "last_edited_at",
        nullable=True,
        comment="最后编辑时间"
    )
    
    # 是否为模板项目
    is_template = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否为模板项目"
    )
    
    # 项目路径（用于文件存储）
    project_path = Column(
        String(500),
        nullable=True,
        comment="项目文件路径"
    )
    
    # 关联关系（将在其他模型文件中定义）
    # chapters = relationship("Chapter", back_populates="project")
    # characters = relationship("Character", back_populates="project")
    # scenes = relationship("Scene", back_populates="project")
    # outlines = relationship("Outline", back_populates="project")
    
    def update_statistics(self):
        """更新项目统计信息"""
        # 这里将在实现章节、角色等模型后添加具体逻辑
        pass
    
    def calculate_progress(self) -> float:
        """计算项目进度"""
        if self.target_words and self.target_words > 0:
            return min(100.0, (self.total_words / self.target_words) * 100)
        elif self.target_chapters and self.target_chapters > 0:
            return min(100.0, (self.total_chapters / self.target_chapters) * 100)
        return 0.0
    
    def update_last_edited(self):
        """更新最后编辑时间"""
        self.last_edited_at = datetime.utcnow()
    
    def get_default_settings(self) -> dict:
        """获取默认项目设置"""
        return {
            "auto_save": True,
            "auto_backup": True,
            "backup_interval": 300,  # 5分钟
            "word_count_goal_daily": 1000,
            "ai_assistance_enabled": True,
            "ai_model_preference": "gpt-3.5-turbo",
            "export_format": "docx",
            "theme": "light",
            "font_family": "微软雅黑",
            "font_size": 14,
            "line_spacing": 1.5
        }
    
    def __repr__(self) -> str:
        return f"<Project(id={self.id}, title='{self.title}', status='{self.status}')>"

# Pydantic模式定义

class ProjectBaseSchema(BaseSchema):
    """项目基础模式"""
    title: str = Field(..., min_length=1, max_length=255, description="项目标题")
    description: Optional[str] = Field(None, description="项目描述")
    genre: ProjectGenre = Field(ProjectGenre.OTHER, description="小说类型")
    status: ProjectStatus = Field(ProjectStatus.DRAFT, description="项目状态")
    cover_image: Optional[str] = Field(None, description="封面图片路径")
    tags: Optional[List[str]] = Field(None, description="项目标签")
    target_words: Optional[int] = Field(None, ge=0, description="目标字数")
    target_chapters: Optional[int] = Field(None, ge=0, description="目标章节数")
    is_template: bool = Field(False, description="是否为模板项目")
    project_path: Optional[str] = Field(None, description="项目文件路径")
    settings: Optional[dict] = Field(None, description="项目设置")
    
    @validator('tags')
    def validate_tags(cls, v):
        if v is not None:
            # 限制标签数量和长度
            if len(v) > 10:
                raise ValueError('标签数量不能超过10个')
            for tag in v:
                if len(tag) > 20:
                    raise ValueError('单个标签长度不能超过20个字符')
        return v

class ProjectCreateSchema(ProjectBaseSchema, BaseCreateSchema):
    """创建项目模式"""
    pass

class ProjectUpdateSchema(BaseUpdateSchema):
    """更新项目模式"""
    title: Optional[str] = Field(None, min_length=1, max_length=255, description="项目标题")
    description: Optional[str] = Field(None, description="项目描述")
    genre: Optional[ProjectGenre] = Field(None, description="小说类型")
    status: Optional[ProjectStatus] = Field(None, description="项目状态")
    cover_image: Optional[str] = Field(None, description="封面图片路径")
    tags: Optional[List[str]] = Field(None, description="项目标签")
    target_words: Optional[int] = Field(None, ge=0, description="目标字数")
    target_chapters: Optional[int] = Field(None, ge=0, description="目标章节数")
    is_template: Optional[bool] = Field(None, description="是否为模板项目")
    project_path: Optional[str] = Field(None, description="项目文件路径")
    settings: Optional[dict] = Field(None, description="项目设置")

class ProjectResponseSchema(ProjectBaseSchema, BaseResponseSchema):
    """项目响应模式"""
    total_words: int = Field(0, description="总字数")
    total_chapters: int = Field(0, description="总章节数")
    total_characters: int = Field(0, description="角色数量")
    total_scenes: int = Field(0, description="场景数量")
    progress_percentage: float = Field(0.0, description="完成进度百分比")
    last_edited_at: Optional[datetime] = Field(None, description="最后编辑时间")

class ProjectListSchema(BaseSchema):
    """项目列表模式（简化版）"""
    id: uuid.UUID = Field(..., description="项目ID")
    title: str = Field(..., description="项目标题")
    description: Optional[str] = Field(None, description="项目描述")
    genre: ProjectGenre = Field(..., description="小说类型")
    status: ProjectStatus = Field(..., description="项目状态")
    cover_image: Optional[str] = Field(None, description="封面图片路径")
    total_words: int = Field(0, description="总字数")
    total_chapters: int = Field(0, description="总章节数")
    progress_percentage: float = Field(0.0, description="完成进度百分比")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    last_edited_at: Optional[datetime] = Field(None, description="最后编辑时间")

class ProjectStatisticsSchema(BaseSchema):
    """项目统计模式"""
    id: uuid.UUID = Field(..., description="项目ID")
    title: str = Field(..., description="项目标题")
    total_words: int = Field(0, description="总字数")
    total_chapters: int = Field(0, description="总章节数")
    total_characters: int = Field(0, description="角色数量")
    total_scenes: int = Field(0, description="场景数量")
    target_words: Optional[int] = Field(None, description="目标字数")
    target_chapters: Optional[int] = Field(None, description="目标章节数")
    progress_percentage: float = Field(0.0, description="完成进度百分比")
    created_at: datetime = Field(..., description="创建时间")
    last_edited_at: Optional[datetime] = Field(None, description="最后编辑时间")

# 导出的类
__all__ = [
    "ProjectStatus",
    "ProjectGenre",
    "Project",
    "ProjectBaseSchema",
    "ProjectCreateSchema",
    "ProjectUpdateSchema",
    "ProjectResponseSchema",
    "ProjectListSchema",
    "ProjectStatisticsSchema"
]