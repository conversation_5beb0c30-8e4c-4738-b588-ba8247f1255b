"""配置管理模块

提供应用程序的配置管理功能，包括：
- 数据库配置
- AI服务配置
- UI界面配置
- 应用程序设置
"""

from .settings import (
    DatabaseConfig,
    AIServiceConfig,
    UIConfig,
    AppConfig,
    settings
)

from .database import (
    get_database_url,
    get_database_config,
    get_alembic_config,
    validate_database_config,
    get_test_database_url,
    create_database_url
)

__all__ = [
    "DatabaseConfig",
    "AIServiceConfig",
    "UIConfig", 
    "AppConfig",
    "settings",
    "get_database_url",
    "get_database_config",
    "get_alembic_config",
    "validate_database_config",
    "get_test_database_url",
    "create_database_url"
]