# Design Document

## Overview

笔落App是一款基于纯Python技术栈的AI辅助小说创作平台。该平台采用现代化的桌面应用架构，集成多种AI大语言模型，为小说创作者提供从项目管理到内容输出的完整创作工具链。系统设计遵循模块化、可扩展和用户体验优先的原则。

核心技术架构：
- **前端界面**：基于Python GUI框架（推荐使用Flet或CustomTkinter）
- **后端服务**：FastAPI + SQLAlchemy + Redis
- **AI集成**：多模型适配器模式，支持OpenAI、Anthropic、国产大模型
- **数据存储**：SQLite（本地）+ PostgreSQL（可选云端同步）
- **桌面应用**：Python原生打包（PyInstaller/cx_Freeze）

## Steering Document Alignment

### Technical Standards (tech.md)
本设计遵循以下技术标准：
- 采用纯Python生态系统，确保技术栈一致性
- 遵循PEP 8代码规范和类型提示标准
- 使用异步编程模式提升性能
- 实现依赖注入和控制反转原则
- 采用领域驱动设计（DDD）架构模式

### Project Structure (structure.md)
项目将按照以下结构组织：
```
bamboofall_app/
├── src/
│   ├── ui/                 # 用户界面层
│   ├── services/           # 业务服务层
│   ├── models/             # 数据模型层
│   ├── repositories/       # 数据访问层
│   ├── ai/                 # AI集成层
│   └── utils/              # 工具函数层
├── tests/                  # 测试代码
├── docs/                   # 文档
└── requirements.txt        # 依赖管理
```

## Code Reuse Analysis

### Existing Components to Leverage
- **Python标准库**：asyncio用于异步处理，pathlib用于文件操作，json用于数据序列化
- **SQLAlchemy ORM**：复用现有的数据库抽象层模式
- **Pydantic模型**：利用数据验证和序列化功能
- **FastAPI框架**：复用RESTful API设计模式

### Integration Points
- **AI服务API**：通过统一的适配器接口集成多种大语言模型
- **本地文件系统**：与操作系统文件管理器深度集成
- **数据库连接**：支持SQLite和PostgreSQL的无缝切换
- **云存储服务**：可选集成OneDrive、Google Drive等云同步服务

## Architecture

系统采用分层架构设计，确保各层职责清晰、耦合度低：

### Modular Design Principles
- **Single File Responsibility**：每个Python模块专注单一功能领域
- **Component Isolation**：UI组件、业务逻辑、数据访问完全分离
- **Service Layer Separation**：明确区分表现层、应用层、领域层、基础设施层
- **Utility Modularity**：工具函数按功能域分组，支持独立测试和复用

```mermaid
graph TD
    A[UI Layer - Flet/CustomTkinter] --> B[Application Service Layer]
    B --> C[Domain Service Layer]
    C --> D[Repository Layer]
    D --> E[Database Layer - SQLite/PostgreSQL]
    
    B --> F[AI Service Layer]
    F --> G[AI Adapter - OpenAI]
    F --> H[AI Adapter - Claude]
    F --> I[AI Adapter - 国产模型]
    
    B --> J[File Service Layer]
    J --> K[Local Storage]
    J --> L[Cloud Sync Service]
```

## Components and Interfaces

### 1. UI Layer Components

#### MainWindow Component
- **Purpose:** 应用程序主窗口，管理整体布局和导航
- **Interfaces:** 
  - `show_project_list()` - 显示项目列表
  - `open_project(project_id)` - 打开指定项目
  - `show_settings()` - 显示设置界面
- **Dependencies:** ProjectService, UIStateManager
- **Reuses:** 基础窗口组件、主题管理器

#### WritingWorkspace Component
- **Purpose:** 核心创作界面，提供三栏式布局
- **Interfaces:**
  - `load_chapter(chapter_id)` - 加载章节内容
  - `save_content()` - 保存当前内容
  - `toggle_sidebar()` - 切换侧边栏显示
- **Dependencies:** EditorService, OutlineService, CharacterService
- **Reuses:** 富文本编辑器组件、分割面板组件

#### AIAssistantPanel Component
- **Purpose:** AI辅助功能面板
- **Interfaces:**
  - `generate_content(prompt, context)` - 生成内容
  - `optimize_text(text, optimization_type)` - 优化文本
  - `show_suggestions()` - 显示AI建议
- **Dependencies:** AIService, ContentAnalyzer
- **Reuses:** 按钮组件、进度指示器

### 2. Service Layer Components

#### ProjectService
- **Purpose:** 项目管理核心服务
- **Interfaces:**
  - `create_project(project_data)` - 创建新项目
  - `load_project(project_id)` - 加载项目
  - `get_project_statistics(project_id)` - 获取项目统计
- **Dependencies:** ProjectRepository, FileService
- **Reuses:** 数据验证器、文件操作工具

#### AIService
- **Purpose:** AI模型集成和调用管理
- **Interfaces:**
  - `generate_text(model_name, prompt, parameters)` - 生成文本
  - `get_available_models()` - 获取可用模型列表
  - `estimate_cost(model_name, token_count)` - 估算调用成本
- **Dependencies:** AIAdapterFactory, ConfigService
- **Reuses:** HTTP客户端、重试机制

#### ContentService
- **Purpose:** 内容创作和管理服务
- **Interfaces:**
  - `save_chapter(chapter_data)` - 保存章节
  - `auto_save()` - 自动保存
  - `export_content(format, options)` - 导出内容
- **Dependencies:** ContentRepository, ExportService
- **Reuses:** 文本处理工具、格式转换器

### 3. AI Integration Layer

#### AIAdapterFactory
- **Purpose:** AI模型适配器工厂
- **Interfaces:**
  - `create_adapter(provider_name)` - 创建适配器实例
  - `register_adapter(provider_name, adapter_class)` - 注册新适配器
- **Dependencies:** 配置管理器
- **Reuses:** 工厂模式基类

#### OpenAIAdapter
- **Purpose:** OpenAI API适配器
- **Interfaces:**
  - `chat_completion(messages, model, parameters)` - 聊天完成
  - `text_completion(prompt, model, parameters)` - 文本完成
- **Dependencies:** openai库, 认证管理器
- **Reuses:** HTTP客户端基类、错误处理器

## Data Models

### Project Model
```python
from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List

class Project(BaseModel):
    id: str
    title: str
    author: str
    description: Optional[str] = None
    genre: str
    created_at: datetime
    updated_at: datetime
    word_count: int = 0
    chapter_count: int = 0
    status: str = "draft"  # draft, writing, completed
    settings: dict = {}
```

### Chapter Model
```python
class Chapter(BaseModel):
    id: str
    project_id: str
    title: str
    content: str
    order_index: int
    word_count: int
    created_at: datetime
    updated_at: datetime
    status: str = "draft"  # draft, writing, completed
    ai_generated_parts: List[dict] = []  # 记录AI生成的部分
```

### Character Model
```python
class Character(BaseModel):
    id: str
    project_id: str
    name: str
    description: str
    appearance: Optional[str] = None
    personality: Optional[str] = None
    background: Optional[str] = None
    relationships: List[dict] = []
    importance: str = "main"  # main, supporting, minor
    created_at: datetime
    updated_at: datetime
```

### Scene Model
```python
class Scene(BaseModel):
    id: str
    project_id: str
    name: str
    description: str
    location: str
    time_period: Optional[str] = None
    atmosphere: Optional[str] = None
    details: dict = {}
    created_at: datetime
    updated_at: datetime
```

### AIModel Configuration
```python
class AIModelConfig(BaseModel):
    provider: str  # openai, anthropic, zhipu, etc.
    model_name: str
    api_key: str
    base_url: Optional[str] = None
    max_tokens: int = 4000
    temperature: float = 0.7
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    enabled: bool = True
```

## Error Handling

### Error Scenarios

1. **AI服务调用失败**
   - **Handling:** 实现重试机制，降级到备用模型，记录错误日志
   - **User Impact:** 显示友好错误提示，提供手动重试选项

2. **网络连接中断**
   - **Handling:** 缓存用户输入，离线模式运行，自动重连
   - **User Impact:** 显示离线状态指示，保证本地功能正常使用

3. **文件保存失败**
   - **Handling:** 多重备份策略，临时文件恢复，权限检查
   - **User Impact:** 立即通知用户，提供备用保存位置

4. **数据库连接错误**
   - **Handling:** 连接池重试，数据库修复，备份恢复
   - **User Impact:** 显示数据同步状态，提供数据导出选项

5. **AI内容生成质量问题**
   - **Handling:** 内容质量评估，多模型对比，用户反馈学习
   - **User Impact:** 提供内容评分，支持重新生成

## Testing Strategy

### Unit Testing
- **测试框架**: pytest + pytest-asyncio
- **覆盖率目标**: 90%以上
- **关键组件测试**:
  - AI适配器的模拟测试
  - 数据模型验证测试
  - 业务逻辑单元测试
  - 工具函数纯函数测试

### Integration Testing
- **测试范围**: 服务层集成、数据库操作、AI服务调用
- **测试环境**: Docker容器化测试环境
- **关键流程测试**:
  - 项目创建到内容生成完整流程
  - AI模型切换和降级流程
  - 数据导入导出流程
  - 多用户并发操作流程

### End-to-End Testing
- **测试工具**: Playwright for Python（UI自动化）
- **测试场景**:
  - 新用户首次使用完整流程
  - 长篇小说创作完整工作流
  - AI辅助功能使用场景
  - 数据备份和恢复场景

### Performance Testing
- **负载测试**: 大文件处理性能（100万字+）
- **并发测试**: AI服务并发调用
- **内存测试**: 长时间运行内存泄漏检测
- **响应时间测试**: UI操作响应时间基准测试

## Security Considerations

### API密钥管理
- 使用系统密钥库（Windows Credential Manager, macOS Keychain）
- 本地加密存储，支持主密码保护
- 运行时内存加密，防止内存转储泄露

### 数据隐私保护
- 本地优先存储策略
- 可选的端到端加密
- 用户数据匿名化选项
- GDPR合规的数据删除功能

### 网络安全
- HTTPS强制加密通信
- API调用签名验证
- 请求频率限制和防滥用
- 恶意内容检测和过滤

## Deployment and Distribution

### 打包策略
- **主要方案**: PyInstaller一键打包
- **备选方案**: cx_Freeze跨平台打包
- **优化选项**: UPX压缩，减少安装包体积

### 平台支持
- **Windows**: 原生支持，MSI安装包
- **macOS**: 应用程序包，代码签名
- **Linux**: AppImage便携版本

### 自动更新
- 内置更新检查机制
- 增量更新支持
- 回滚功能保障
- 用户可控的更新策略