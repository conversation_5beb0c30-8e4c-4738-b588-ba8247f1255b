笔落App需求文档
一. 功能需求
1.目的
  笔落App是一个用于作者和AI大模型合作进行编写小说的应用，主旨是将作者的创意落地成小说成品，减少作者的写作复杂度，只专注于构思更加优秀创意。
2.功能
  2.1 小说大纲管理
    用户可以在应用程序中管理小说的大纲，包括添加、删除、修改大纲等操作。
    通过一个创意来引申出小说的开端、结局以及中间的重要节点，为小说确定一个稳定的骨架。
  2.2 小说章节管理
    用户可以在应用程序中管理小说的章节，包括添加、删除、修改章节等操作。
    与大纲相比较，章节管理更加详细，用户可以在章节中添加更多的内容，如角色介绍、场景描述、事件发生等。
  2.3 小说角色管理
    用户可以在应用程序中管理小说的角色，包括添加、删除、修改角色等操作。
    角色管理为小说的主要和次要人物设定其性格、背景、能力等，为小说的发展提供了基础。
  2.4 小说场景管理
    用户可以在应用程序中管理小说的场景，包括添加、删除、修改场景等操作。
    场景管理为小说的事件发生提供了背景，为小说的发展提供了基础。
  2.5 小说事件管理
    用户可以在应用程序中管理小说的事件，包括添加、删除、修改事件等操作。
    事件管理为小说的发展提供了具体的内容，为小说的发展提供了基础。
  2.6 小说章节内容生成和修改
    用户可以在应用程序中生成和修改小说的章节内容，包括添加、删除、修改段落等操作。
    内容生成和修改为小说的发展提供了具体的内容，实现最终的小说内容输出。
  2.7 小说内容输出
    用户可以在应用程序中输出小说的内容，包括生成文本文件、导出PDF等操作。
    内容输出为小说的发展提供了最终的产品，为小说的阅读和分享提供了基础。
  2.8 AI功能辅助(与上面的每一个功能都存在关联)
    2.8.1 根据用户提供的创意，生成符合小说大纲的章节内容。
    2.8.2 根据用户提供的章节内容，生成符合小说角色和场景的事件。
    2.8.3 根据用户提供的事件，生成符合小说角色和场景的段落。
    2.8.4 小说内容优化
      2.8.4.1 自动优化段落结构，使段落更加流畅。
      2.8.4.2 自动优化角色对话，使对话更加自然。
      2.8.4.3 自动优化场景描述，使场景更加真实。
      2.8.4.4 自动优化事件描述，使事件更加真实。
      2.8.4.5 自动优化角色关系，使角色关系更加自然。
      2.8.4.6 提供一件优化内容，为AI生成的内容去除AI味，使内容更加符合读者的口味。

二. 界面设计
    1. 软件主界面
       界面背景是一个很有意境的图像，符合软件名字的寓意。
       界面上有一个创建项目按钮，点击后在同级目录的project目录下创建一个新的项目。
       界面上有一个打开项目按钮，点击后读取项目文件夹下的项目文件，可以从中选择一个打开项目。
       界面上有一个设置按钮，点击后进入设置界面。

    2. 创作界面
       创作界面分为大纲管理、章节管理、角色管理、场景管理、事件管理、内容生成和修改、内容输出等模块。该界面应该以生成内容为主题，不同的模块按照优雅的方式分布在生成内容的四周。
       大纲管理模块：用户可以在该模块中添加、删除、修改小说的大纲。
       章节管理模块：用户可以在该模块中添加、删除、修改小说的章节。
       角色管理模块：用户可以在该模块中添加、删除、修改小说的角色。
       场景管理模块：用户可以在该模块中添加、删除、修改小说的场景。
       事件管理模块：用户可以在该模块中添加、删除、修改小说的事件。
    
    3. 设置界面
       设置界面分为通用设置、AI模型设置、内容优化设置等模块。
       通用设置包含背景颜色、字体、字体大小、主题等。
       AI模型设置包含AI厂商选择，API输入和厂商名下的模型选择。
       内容优化设置暂时没有考虑好，目前不实现功能。

三. 工作流设计
    1. 打开软件，在主界面选择新建项目、打开项目或者设置。
    2. 新建项目或打开项目后，进入创作界面。
    3. 在创作界面中，用户可以管理小说的大纲、章节、角色、场景、事件等。
    4. 用户可以在创作界面中生成和修改小说的章节内容。
    5. 用户可以在创作界面选择一些AI模型，用于生成和修改小说的内容。
    6. 用户可以在创作界面使用一些特色功能，例如内容格式标准化，一键去AI味等。
    7. 用户可以在创作界面中输出小说的内容。

四. 开发阶段
    第一阶段，创建不同的模块，完成主要界面的设计，主界面的设计，AI大模型接入，创作界面的所有功能实现。
    第二阶段，根据使用体验，完善不同模块的功能，如优化界面、完善功能、修复bug等。
    第三阶段，根据后续使用的需求，完成新功能的开发，如添加新的AI模型、优化内容生成和修改的功能、添加新的内容输出格式等。