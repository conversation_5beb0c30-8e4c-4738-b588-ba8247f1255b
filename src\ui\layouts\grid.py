"""网格布局组件

提供灵活的网格布局系统，支持响应式网格和自定义网格配置。
"""

import flet as ft
from typing import List, Optional, Union, Dict, Any
from enum import Enum
from dataclasses import dataclass
from ..components.base import BaseComponent, theme_manager

class GridAlignment(str, Enum):
    """网格对齐方式"""
    START = "start"
    CENTER = "center"
    END = "end"
    STRETCH = "stretch"
    SPACE_BETWEEN = "space_between"
    SPACE_AROUND = "space_around"
    SPACE_EVENLY = "space_evenly"

class GridAutoFlow(str, Enum):
    """网格自动流向"""
    ROW = "row"
    COLUMN = "column"
    ROW_DENSE = "row_dense"
    COLUMN_DENSE = "column_dense"

@dataclass
class GridConfig:
    """网格配置"""
    columns: int = 12  # 网格列数
    rows: Optional[int] = None  # 网格行数（None表示自动）
    gap: int = 16  # 网格间距
    row_gap: Optional[int] = None  # 行间距（None使用gap）
    column_gap: Optional[int] = None  # 列间距（None使用gap）
    justify_items: GridAlignment = GridAlignment.STRETCH  # 项目水平对齐
    align_items: GridAlignment = GridAlignment.STRETCH  # 项目垂直对齐
    justify_content: GridAlignment = GridAlignment.START  # 内容水平对齐
    align_content: GridAlignment = GridAlignment.START  # 内容垂直对齐
    auto_flow: GridAutoFlow = GridAutoFlow.ROW  # 自动流向
    
    def get_row_gap(self) -> int:
        """获取行间距"""
        return self.row_gap if self.row_gap is not None else self.gap
    
    def get_column_gap(self) -> int:
        """获取列间距"""
        return self.column_gap if self.column_gap is not None else self.gap

@dataclass
class GridItemConfig:
    """网格项配置"""
    column_start: Optional[int] = None  # 起始列
    column_end: Optional[int] = None  # 结束列
    column_span: Optional[int] = None  # 跨越列数
    row_start: Optional[int] = None  # 起始行
    row_end: Optional[int] = None  # 结束行
    row_span: Optional[int] = None  # 跨越行数
    justify_self: Optional[GridAlignment] = None  # 自身水平对齐
    align_self: Optional[GridAlignment] = None  # 自身垂直对齐
    order: Optional[int] = None  # 显示顺序
    
    def get_column_span(self) -> int:
        """获取列跨度"""
        if self.column_span is not None:
            return self.column_span
        elif self.column_start is not None and self.column_end is not None:
            return self.column_end - self.column_start
        else:
            return 1
    
    def get_row_span(self) -> int:
        """获取行跨度"""
        if self.row_span is not None:
            return self.row_span
        elif self.row_start is not None and self.row_end is not None:
            return self.row_end - self.row_start
        else:
            return 1

class GridItem(BaseComponent):
    """网格项组件"""
    
    def __init__(
        self,
        content: ft.Control,
        config: Optional[GridItemConfig] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.content = content
        self.config = config or GridItemConfig()
    
    def _update_theme_styles(self):
        """更新主题样式"""
        # 网格项的样式更新由内容组件处理
        pass
    
    def build(self) -> ft.Control:
        """构建网格项"""
        # 在Flet中，网格项主要通过容器来实现
        # 这里返回包装后的内容
        container = ft.Container(
            content=self.content,
            expand=True,
        )
        
        return container
    
    def set_column_span(self, span: int):
        """设置列跨度"""
        self.config.column_span = span
    
    def set_row_span(self, span: int):
        """设置行跨度"""
        self.config.row_span = span
    
    def set_position(self, column: int, row: int):
        """设置位置"""
        self.config.column_start = column
        self.config.row_start = row

class GridLayout(BaseComponent):
    """网格布局组件"""
    
    def __init__(
        self,
        items: List[Union[ft.Control, GridItem]] = None,
        config: Optional[GridConfig] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.items = items or []
        self.config = config or GridConfig()
    
    def _update_theme_styles(self):
        """更新主题样式"""
        if self._control and self.theme:
            # 更新网格布局样式
            pass
    
    def _create_grid_rows(self) -> List[ft.Row]:
        """创建网格行"""
        if not self.items:
            return []
        
        rows = []
        current_row = []
        current_column = 0
        
        for item in self.items:
            # 确保item是GridItem
            if isinstance(item, GridItem):
                grid_item = item
            else:
                grid_item = GridItem(content=item)
            
            # 获取项目跨度
            column_span = grid_item.config.get_column_span()
            
            # 检查是否需要换行
            if current_column + column_span > self.config.columns:
                if current_row:
                    # 填充当前行的剩余空间
                    while len(current_row) < self.config.columns:
                        current_row.append(ft.Container(expand=True))
                    
                    rows.append(ft.Row(
                        current_row,
                        spacing=self.config.get_column_gap(),
                        alignment=self._get_main_axis_alignment(),
                        vertical_alignment=self._get_cross_axis_alignment()
                    ))
                
                current_row = []
                current_column = 0
            
            # 添加项目到当前行
            item_container = ft.Container(
                content=grid_item.get_control(),
                expand=True,
                col={"xs": column_span} if hasattr(ft.Container, 'col') else None
            )
            
            # 如果跨多列，添加相应数量的占位符
            for _ in range(column_span):
                if current_column < self.config.columns:
                    if _ == 0:
                        current_row.append(item_container)
                    else:
                        # 跨列项目的占位符
                        pass
                    current_column += 1
        
        # 处理最后一行
        if current_row:
            # 填充最后一行的剩余空间
            while len(current_row) < self.config.columns:
                current_row.append(ft.Container(expand=True))
            
            rows.append(ft.Row(
                current_row,
                spacing=self.config.get_column_gap(),
                alignment=self._get_main_axis_alignment(),
                vertical_alignment=self._get_cross_axis_alignment()
            ))
        
        return rows
    
    def _get_main_axis_alignment(self) -> ft.MainAxisAlignment:
        """获取主轴对齐方式"""
        alignment_map = {
            GridAlignment.START: ft.MainAxisAlignment.START,
            GridAlignment.CENTER: ft.MainAxisAlignment.CENTER,
            GridAlignment.END: ft.MainAxisAlignment.END,
            GridAlignment.SPACE_BETWEEN: ft.MainAxisAlignment.SPACE_BETWEEN,
            GridAlignment.SPACE_AROUND: ft.MainAxisAlignment.SPACE_AROUND,
            GridAlignment.SPACE_EVENLY: ft.MainAxisAlignment.SPACE_EVENLY,
        }
        return alignment_map.get(self.config.justify_content, ft.MainAxisAlignment.START)
    
    def _get_cross_axis_alignment(self) -> ft.CrossAxisAlignment:
        """获取交叉轴对齐方式"""
        alignment_map = {
            GridAlignment.START: ft.CrossAxisAlignment.START,
            GridAlignment.CENTER: ft.CrossAxisAlignment.CENTER,
            GridAlignment.END: ft.CrossAxisAlignment.END,
            GridAlignment.STRETCH: ft.CrossAxisAlignment.STRETCH,
        }
        return alignment_map.get(self.config.align_items, ft.CrossAxisAlignment.STRETCH)
    
    def build(self) -> ft.Control:
        """构建网格布局"""
        if not self.items:
            return ft.Container()
        
        # 创建网格行
        grid_rows = self._create_grid_rows()
        
        # 创建网格容器
        grid_container = ft.Column(
            grid_rows,
            spacing=self.config.get_row_gap(),
            alignment=ft.MainAxisAlignment.START,
            horizontal_alignment=ft.CrossAxisAlignment.STRETCH,
            expand=True
        )
        
        return grid_container
    
    def add_item(self, item: Union[ft.Control, GridItem], index: Optional[int] = None):
        """添加网格项"""
        if index is None:
            self.items.append(item)
        else:
            self.items.insert(index, item)
        
        # 重新构建网格
        if self._control:
            self._control = self.build()
            self.update()
    
    def remove_item(self, index: int):
        """移除网格项"""
        if 0 <= index < len(self.items):
            self.items.pop(index)
            
            # 重新构建网格
            if self._control:
                self._control = self.build()
                self.update()
    
    def clear_items(self):
        """清空所有项"""
        self.items.clear()
        
        # 重新构建网格
        if self._control:
            self._control = self.build()
            self.update()
    
    def set_columns(self, columns: int):
        """设置列数"""
        self.config.columns = columns
        
        # 重新构建网格
        if self._control:
            self._control = self.build()
            self.update()
    
    def set_gap(self, gap: int):
        """设置间距"""
        self.config.gap = gap
        
        # 重新构建网格
        if self._control:
            self._control = self.build()
            self.update()

# 网格工具函数

def create_responsive_grid(
    items: List[ft.Control],
    mobile_columns: int = 1,
    tablet_columns: int = 2,
    desktop_columns: int = 3,
    gap: int = 16
) -> Dict[str, GridLayout]:
    """创建响应式网格布局"""
    return {
        "mobile": GridLayout(
            items=items,
            config=GridConfig(columns=mobile_columns, gap=gap)
        ),
        "tablet": GridLayout(
            items=items,
            config=GridConfig(columns=tablet_columns, gap=gap)
        ),
        "desktop": GridLayout(
            items=items,
            config=GridConfig(columns=desktop_columns, gap=gap)
        ),
    }

def create_masonry_grid(
    items: List[ft.Control],
    columns: int = 3,
    gap: int = 16
) -> ft.Control:
    """创建瀑布流网格布局"""
    # 将项目分配到各列
    column_items = [[] for _ in range(columns)]
    
    for i, item in enumerate(items):
        column_index = i % columns
        column_items[column_index].append(item)
    
    # 创建列
    columns_controls = []
    for column_list in column_items:
        column = ft.Column(
            column_list,
            spacing=gap,
            expand=True
        )
        columns_controls.append(column)
    
    # 创建行容器
    masonry_container = ft.Row(
        columns_controls,
        spacing=gap,
        alignment=ft.MainAxisAlignment.START,
        vertical_alignment=ft.CrossAxisAlignment.START,
        expand=True
    )
    
    return masonry_container

def create_card_grid(
    cards: List[ft.Control],
    columns: int = 3,
    gap: int = 16,
    min_card_width: int = 200
) -> GridLayout:
    """创建卡片网格布局"""
    # 为每个卡片创建网格项
    grid_items = []
    for card in cards:
        # 包装卡片以确保最小宽度
        card_container = ft.Container(
            content=card,
            width=min_card_width,
            expand=True
        )
        grid_items.append(GridItem(content=card_container))
    
    return GridLayout(
        items=grid_items,
        config=GridConfig(
            columns=columns,
            gap=gap,
            justify_items=GridAlignment.STRETCH,
            align_items=GridAlignment.START
        )
    )

# 导出
__all__ = [
    'GridAlignment',
    'GridAutoFlow',
    'GridConfig',
    'GridItemConfig',
    'GridItem',
    'GridLayout',
    'create_responsive_grid',
    'create_masonry_grid',
    'create_card_grid',
]