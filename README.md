# 笔落 - AI小说创作平台

一个基于Python的AI辅助小说创作桌面应用程序，支持多种AI模型，提供项目管理、大纲创作、角色设定、场景管理等功能。

## 功能特性

### 核心功能
- 📝 **项目管理**: 创建、编辑、删除小说项目
- 🎭 **角色管理**: 详细的角色设定和关系管理
- 🗺️ **场景管理**: 场景描述和世界观构建
- 📋 **大纲管理**: 章节大纲和情节规划
- ✍️ **内容创作**: 章节内容编写和管理

### AI辅助功能
- 🤖 **多AI模型支持**: OpenAI GPT、Claude、通义千问、文心一言
- 💡 **智能建议**: AI辅助角色设定、情节发展
- 📖 **内容生成**: AI协助章节内容创作
- 🔍 **智能分析**: 情节连贯性和角色一致性检查

### 技术特性
- 🖥️ **跨平台**: 支持Windows、macOS、Linux
- 💾 **本地存储**: SQLite数据库，数据安全可控
- 🎨 **现代UI**: 基于Flet的美观界面
- ⚡ **高性能**: 异步处理，响应迅速

## 技术栈

- **后端框架**: FastAPI
- **UI框架**: Flet
- **数据库**: SQLite (支持PostgreSQL)
- **AI集成**: OpenAI API, Anthropic Claude, 阿里云通义千问, 百度文心一言
- **配置管理**: Pydantic Settings
- **异步支持**: asyncio, aiofiles, aiosqlite

## 快速开始

### 环境要求

- Python 3.10+
- pip 或 uv 包管理器

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd bamboofall_zz
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # macOS/Linux
   source venv/bin/activate
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **配置环境变量**
   ```bash
   # 复制环境变量模板
   cp .env.example .env
   
   # 编辑.env文件，配置AI服务API密钥
   ```

5. **运行应用**
   ```bash
   python main.py
   ```

### 配置说明

在`.env`文件中配置以下参数：

```env
# 应用基本配置
APP_NAME=笔落
APP_VERSION=1.0.0
DEBUG=false

# AI服务配置（至少配置一个）
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_claude_api_key
DASHSCOPE_API_KEY=your_qwen_api_key
WENXIN_API_KEY=your_wenxin_api_key

# 数据库配置
DATABASE_URL=sqlite:///./data/bamboofall.db

# UI配置
UI_THEME=light
WINDOW_WIDTH=1200
WINDOW_HEIGHT=800
```

## 项目结构

```
bamboofall_zz/
├── src/                    # 源代码目录
│   ├── config/            # 配置管理
│   ├── models/            # 数据模型
│   ├── services/          # 业务服务
│   ├── api/               # API接口
│   └── ui/                # 用户界面
├── data/                  # 数据目录
├── logs/                  # 日志目录
├── temp/                  # 临时文件
├── tests/                 # 测试文件
├── main.py               # 应用入口
├── requirements.txt      # 依赖列表
├── .env.example         # 环境变量模板
└── README.md            # 项目说明
```

## 开发指南

### 开发环境设置

1. **安装开发依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **运行测试**
   ```bash
   pytest tests/
   ```

3. **代码格式化**
   ```bash
   black src/
   isort src/
   ```

4. **类型检查**
   ```bash
   mypy src/
   ```

### 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: [<EMAIL>]

## 更新日志

### v1.0.0 (开发中)
- 初始版本发布
- 基础项目管理功能
- AI辅助创作功能
- 跨平台桌面应用

---

**笔落** - 让AI成为你的创作伙伴 ✨